// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_permission_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LocationPermissionEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LocationPermissionEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionEvent()';
  }
}

/// Adds pattern-matching-related methods to [LocationPermissionEvent].
extension LocationPermissionEventPatterns on LocationPermissionEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckPermissionStatus value)? checkPermissionStatus,
    TResult Function(_GetCurrentGrantedPermission value)?
    getCurrentGrantedPermission,
    TResult Function(_IsLocationServiceEnable value)? isLocationServiceEnable,
    TResult Function(_CheckAndRequestPermission value)?
    checkAndRequestPermission,
    TResult Function(_AskBackgroundPermission value)? askBackgroundPermission,
    TResult Function(_OpenAppSettings value)? openAppSettings,
    TResult Function(_OpenDeviceLocationSettings value)?
    openDeviceLocationSettings,
    TResult Function(_CheckLocationService value)? checkLocationService,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CheckPermissionStatus() when checkPermissionStatus != null:
        return checkPermissionStatus(_that);
      case _GetCurrentGrantedPermission()
          when getCurrentGrantedPermission != null:
        return getCurrentGrantedPermission(_that);
      case _IsLocationServiceEnable() when isLocationServiceEnable != null:
        return isLocationServiceEnable(_that);
      case _CheckAndRequestPermission() when checkAndRequestPermission != null:
        return checkAndRequestPermission(_that);
      case _AskBackgroundPermission() when askBackgroundPermission != null:
        return askBackgroundPermission(_that);
      case _OpenAppSettings() when openAppSettings != null:
        return openAppSettings(_that);
      case _OpenDeviceLocationSettings()
          when openDeviceLocationSettings != null:
        return openDeviceLocationSettings(_that);
      case _CheckLocationService() when checkLocationService != null:
        return checkLocationService(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckPermissionStatus value)
    checkPermissionStatus,
    required TResult Function(_GetCurrentGrantedPermission value)
    getCurrentGrantedPermission,
    required TResult Function(_IsLocationServiceEnable value)
    isLocationServiceEnable,
    required TResult Function(_CheckAndRequestPermission value)
    checkAndRequestPermission,
    required TResult Function(_AskBackgroundPermission value)
    askBackgroundPermission,
    required TResult Function(_OpenAppSettings value) openAppSettings,
    required TResult Function(_OpenDeviceLocationSettings value)
    openDeviceLocationSettings,
    required TResult Function(_CheckLocationService value) checkLocationService,
  }) {
    final _that = this;
    switch (_that) {
      case _CheckPermissionStatus():
        return checkPermissionStatus(_that);
      case _GetCurrentGrantedPermission():
        return getCurrentGrantedPermission(_that);
      case _IsLocationServiceEnable():
        return isLocationServiceEnable(_that);
      case _CheckAndRequestPermission():
        return checkAndRequestPermission(_that);
      case _AskBackgroundPermission():
        return askBackgroundPermission(_that);
      case _OpenAppSettings():
        return openAppSettings(_that);
      case _OpenDeviceLocationSettings():
        return openDeviceLocationSettings(_that);
      case _CheckLocationService():
        return checkLocationService(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckPermissionStatus value)? checkPermissionStatus,
    TResult? Function(_GetCurrentGrantedPermission value)?
    getCurrentGrantedPermission,
    TResult? Function(_IsLocationServiceEnable value)? isLocationServiceEnable,
    TResult? Function(_CheckAndRequestPermission value)?
    checkAndRequestPermission,
    TResult? Function(_AskBackgroundPermission value)? askBackgroundPermission,
    TResult? Function(_OpenAppSettings value)? openAppSettings,
    TResult? Function(_OpenDeviceLocationSettings value)?
    openDeviceLocationSettings,
    TResult? Function(_CheckLocationService value)? checkLocationService,
  }) {
    final _that = this;
    switch (_that) {
      case _CheckPermissionStatus() when checkPermissionStatus != null:
        return checkPermissionStatus(_that);
      case _GetCurrentGrantedPermission()
          when getCurrentGrantedPermission != null:
        return getCurrentGrantedPermission(_that);
      case _IsLocationServiceEnable() when isLocationServiceEnable != null:
        return isLocationServiceEnable(_that);
      case _CheckAndRequestPermission() when checkAndRequestPermission != null:
        return checkAndRequestPermission(_that);
      case _AskBackgroundPermission() when askBackgroundPermission != null:
        return askBackgroundPermission(_that);
      case _OpenAppSettings() when openAppSettings != null:
        return openAppSettings(_that);
      case _OpenDeviceLocationSettings()
          when openDeviceLocationSettings != null:
        return openDeviceLocationSettings(_that);
      case _CheckLocationService() when checkLocationService != null:
        return checkLocationService(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkPermissionStatus,
    TResult Function()? getCurrentGrantedPermission,
    TResult Function()? isLocationServiceEnable,
    TResult Function()? checkAndRequestPermission,
    TResult Function()? askBackgroundPermission,
    TResult Function()? openAppSettings,
    TResult Function()? openDeviceLocationSettings,
    TResult Function()? checkLocationService,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _CheckPermissionStatus() when checkPermissionStatus != null:
        return checkPermissionStatus();
      case _GetCurrentGrantedPermission()
          when getCurrentGrantedPermission != null:
        return getCurrentGrantedPermission();
      case _IsLocationServiceEnable() when isLocationServiceEnable != null:
        return isLocationServiceEnable();
      case _CheckAndRequestPermission() when checkAndRequestPermission != null:
        return checkAndRequestPermission();
      case _AskBackgroundPermission() when askBackgroundPermission != null:
        return askBackgroundPermission();
      case _OpenAppSettings() when openAppSettings != null:
        return openAppSettings();
      case _OpenDeviceLocationSettings()
          when openDeviceLocationSettings != null:
        return openDeviceLocationSettings();
      case _CheckLocationService() when checkLocationService != null:
        return checkLocationService();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkPermissionStatus,
    required TResult Function() getCurrentGrantedPermission,
    required TResult Function() isLocationServiceEnable,
    required TResult Function() checkAndRequestPermission,
    required TResult Function() askBackgroundPermission,
    required TResult Function() openAppSettings,
    required TResult Function() openDeviceLocationSettings,
    required TResult Function() checkLocationService,
  }) {
    final _that = this;
    switch (_that) {
      case _CheckPermissionStatus():
        return checkPermissionStatus();
      case _GetCurrentGrantedPermission():
        return getCurrentGrantedPermission();
      case _IsLocationServiceEnable():
        return isLocationServiceEnable();
      case _CheckAndRequestPermission():
        return checkAndRequestPermission();
      case _AskBackgroundPermission():
        return askBackgroundPermission();
      case _OpenAppSettings():
        return openAppSettings();
      case _OpenDeviceLocationSettings():
        return openDeviceLocationSettings();
      case _CheckLocationService():
        return checkLocationService();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkPermissionStatus,
    TResult? Function()? getCurrentGrantedPermission,
    TResult? Function()? isLocationServiceEnable,
    TResult? Function()? checkAndRequestPermission,
    TResult? Function()? askBackgroundPermission,
    TResult? Function()? openAppSettings,
    TResult? Function()? openDeviceLocationSettings,
    TResult? Function()? checkLocationService,
  }) {
    final _that = this;
    switch (_that) {
      case _CheckPermissionStatus() when checkPermissionStatus != null:
        return checkPermissionStatus();
      case _GetCurrentGrantedPermission()
          when getCurrentGrantedPermission != null:
        return getCurrentGrantedPermission();
      case _IsLocationServiceEnable() when isLocationServiceEnable != null:
        return isLocationServiceEnable();
      case _CheckAndRequestPermission() when checkAndRequestPermission != null:
        return checkAndRequestPermission();
      case _AskBackgroundPermission() when askBackgroundPermission != null:
        return askBackgroundPermission();
      case _OpenAppSettings() when openAppSettings != null:
        return openAppSettings();
      case _OpenDeviceLocationSettings()
          when openDeviceLocationSettings != null:
        return openDeviceLocationSettings();
      case _CheckLocationService() when checkLocationService != null:
        return checkLocationService();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _CheckPermissionStatus implements LocationPermissionEvent {
  const _CheckPermissionStatus();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _CheckPermissionStatus);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionEvent.checkPermissionStatus()';
  }
}

/// @nodoc

class _GetCurrentGrantedPermission implements LocationPermissionEvent {
  const _GetCurrentGrantedPermission();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GetCurrentGrantedPermission);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionEvent.getCurrentGrantedPermission()';
  }
}

/// @nodoc

class _IsLocationServiceEnable implements LocationPermissionEvent {
  const _IsLocationServiceEnable();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _IsLocationServiceEnable);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionEvent.isLocationServiceEnable()';
  }
}

/// @nodoc

class _CheckAndRequestPermission implements LocationPermissionEvent {
  const _CheckAndRequestPermission();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CheckAndRequestPermission);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionEvent.checkAndRequestPermission()';
  }
}

/// @nodoc

class _AskBackgroundPermission implements LocationPermissionEvent {
  const _AskBackgroundPermission();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _AskBackgroundPermission);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionEvent.askBackgroundPermission()';
  }
}

/// @nodoc

class _OpenAppSettings implements LocationPermissionEvent {
  const _OpenAppSettings();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _OpenAppSettings);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionEvent.openAppSettings()';
  }
}

/// @nodoc

class _OpenDeviceLocationSettings implements LocationPermissionEvent {
  const _OpenDeviceLocationSettings();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OpenDeviceLocationSettings);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionEvent.openDeviceLocationSettings()';
  }
}

/// @nodoc

class _CheckLocationService implements LocationPermissionEvent {
  const _CheckLocationService();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _CheckLocationService);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'LocationPermissionEvent.checkLocationService()';
  }
}
