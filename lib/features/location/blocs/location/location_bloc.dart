import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:safari_yatri/core/errors/location_failure.dart';
import 'package:safari_yatri/common/logger.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/location/repositories/remote_location_repository.dart';

part 'location_event.dart';
part 'location_state.dart';
part 'location_bloc.freezed.dart';

///It will send current locationt to server plus, listen changes everytime
///and k hunxa vanda real time location track garna yesle help garxa
class LocationBloc extends Bloc<LocationEvent, LocationState> {
  Position? _lastNotifiedPosition;

  Position? _currentPosition; 

  static const double _distanceThreshold = 50.0;

  StreamSubscription<Position>? _positionSubscription;

  final RemoteLocationRepository _remoteLocationRepository;

  LocationBloc({required RemoteLocationRepository remoteLocationRepository})
    : _remoteLocationRepository = remoteLocationRepository,
      super(const LocationState.initial()) {
    on<_StartTracking>(_onStartTracking);
    on<_LocationUpdated>(_onLocationUpdated);
    on<_ErrorOccurred>(_onErrorOccurred);
  }

  Future<void> _onStartTracking(
    _StartTracking event,
    Emitter<LocationState> emit,
  ) async {
    // emit(const LocationState.loading());

    ///sayad unccessary naii thiyo ra comment handeko aafaii aairako just internet gako bela
    ///even if its correct************

    // final serviceEnabled = await Geolocator.isLocationServiceEnabled();
    // if (!serviceEnabled) {
    //   emit(
    //     const LocationState.failure(
    //       LocationFailureWithMessage(
    //         message: 'Location services are disabled. Please enable them.',
    //       ),
    //     ),
    //   );
    //   return;
    // }

    // LocationPermission permission = await Geolocator.checkPermission();
    // if (permission == LocationPermission.denied) {
    //   permission = await Geolocator.requestPermission();
    //   if (permission == LocationPermission.denied) {
    //     emit(
    //       const LocationState.failure(
    //         LocationFailureWithMessage(
    //           message: 'Location permissions are denied. Please grant them.',
    //         ),
    //       ),
    //     );
    //     return;
    //   }
    // }

    // if (permission == LocationPermission.deniedForever) {
    //   emit(
    //     const LocationState.failure(
    //       LocationFailureWithMessage(
    //         message:
    //             'Location permissions are permanently denied. Please enable them in app settings.',
    //       ),
    //     ),
    //   );
    //   return;
    // }

    emit(const LocationState.loading());

    await _positionSubscription?.cancel();

    _positionSubscription = Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 5,
      ),
    ).listen(
      (Position position) {
        dLog.d('************************************+++++++++++++++++++++++++');
        dLog.i('Location updated: ${position.latitude}, ${position.longitude}');
        add(LocationEvent.locationUpdated(position));
      },
      onError: (error) {
        add(
          LocationEvent.errorOccurred(
            message: 'Failed to get location: $error',
          ),
        );

        // print('Location stream error: $error');
      },
      cancelOnError: true,
    );
  }

  Future<void> _onLocationUpdated(
    _LocationUpdated event,
    Emitter<LocationState> emit,
  ) async {
    _currentPosition = event.position;

    bool shouldSendToServer = false;

    if (_lastNotifiedPosition == null) {
      dLog.i('First location update, sending to server.');
      shouldSendToServer = true;
    } else {
      final distance = Geolocator.distanceBetween(
        _lastNotifiedPosition!.latitude,
        _lastNotifiedPosition!.longitude,
        event.position.latitude,
        event.position.longitude,
      );
      dLog.i('Distance since last update: $distance meters');
      if (distance >= _distanceThreshold) {
        dLog.i(
          'Distance threshold reached (${_distanceThreshold}m), sending to server.',
        );
        shouldSendToServer = true;
      } else {
        dLog.i('Distance threshold not reached, not sending to server.');
      }
    }

    if (shouldSendToServer) {
      _lastNotifiedPosition = event.position;
      emit(const LocationState.loading());
      try {
        await _sendLocationToServerInternal(event.position, emit);
        emit(LocationState.loaded(event.position));
      } catch (e) {}
    } else {
      emit(LocationState.loaded(event.position));
    }
  }

  void _onErrorOccurred(_ErrorOccurred event, Emitter<LocationState> emit) {
    emit(
      LocationState.failure(LocationFailureWithMessage(message: event.message)),
    );
  }

  Future<void> _sendLocationToServerInternal(
    Position position,
    Emitter<LocationState> emit,
  ) async {
    try {
      await _remoteLocationRepository.updateLocation(
        lat: position.latitude,
        lng: position.longitude,
      );

      // print(
      //   'Location sent to server: ${position.latitude}, ${position.longitude}',
      // );
    } catch (e) {
      // print('Error sending location update: $e');

      emit(
        LocationState.failure(
          LocationFailureWithMessage(
            message: 'Failed to send location to server: $e',
          ),
        ),
      );

      rethrow;
    }
  }

  @override
  Future<void> close() {
    _positionSubscription?.cancel();

    return super.close();
  }

  Position? get currentPosition => _currentPosition;
}
