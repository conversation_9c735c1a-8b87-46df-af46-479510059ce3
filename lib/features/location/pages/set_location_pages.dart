// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import 'package:safari_yatri/common/blocs/current_latlng_address/current_lat_lng_address_bloc.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/constant/image_constant.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/core/widget/app_map_pin.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_pop_button.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';
import 'package:safari_yatri/features/location/blocs/set_choosed_location/set_choosed_location_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/map_movement/map_movement_cubit.dart';

import '../../../core/theme/app_styles.dart';
import '../../../core/widget/custom_platform_scaffold.dart';
import '../../../core/widget/custom_search_field_cupertino.dart';

class SetLocationPages extends StatefulWidget {
  const SetLocationPages({super.key, this.initialLocation});
  final LatLng? initialLocation;

  @override
  State<SetLocationPages> createState() => _SetLocationPagesState();
}

class _SetLocationPagesState extends State<SetLocationPages> {
  GoogleMapController? _mapController;
  LatLng? _currentLocation;
  LatLng? _selectedLatLng;
  late CurrentLocationBloc _currentLocationBloc;
  final TextEditingController _addressController = TextEditingController();
  late final AppMapPickerController _appMapPickerController;

  @override
  void initState() {
    super.initState();
    _appMapPickerController = AppMapPickerController();

    if (widget.initialLocation != null) {
      _currentLocation = widget.initialLocation;
    } else {
      _currentLocationBloc =
          sl<CurrentLocationBloc>()
            ..add(CurrentLocationEvent.getCurrentLocation());
    }
  }

  @override
  void dispose() {
    _mapController?.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Use initialLocation directly if present
    if (_currentLocation != null) {
      return PlatformScaffold(body: _buildMap());
    }

    return PlatformScaffold(
      body: BlocBuilder<CurrentLocationBloc, CurrentLocationState>(
        bloc: _currentLocationBloc,
        builder: (context, state) {
          state.whenOrNull(
            loaded: (data) {
              _currentLocation = LatLng(data.latitude, data.longitude);
              _mapController?.animateCamera(
                CameraUpdate.newCameraPosition(
                  CameraPosition(
                    target: _currentLocation!,
                    zoom: kMapInitialZoom,
                  ),
                ),
              );
            },
          );
          return _buildMap();
        },
      ),
    );
  }

  Widget _buildMap() {
    return SizedBox.expand(
      child: Stack(
        children: [
          Positioned.fill(
            child: AppMapPicker(
              iconWidget: SvgPicture.asset(
                ImageConstant.mapMarker,
                width: 50,
                height: 50,
              ),
              mapPickerController: _appMapPickerController,
              child: CustomGoogleMap(
                myLocationEnabled: true,
                mapType: MapType.normal,
                onCameraMove: (position) {
                  _appMapPickerController.mapMoving!();
                  sl<MapMovementCubit>().startMoving();
                  _selectedLatLng = position.target;
                },
                onCameraIdle: () {
                  _appMapPickerController.mapFinishedMoving!();
                  sl<MapMovementCubit>().stopMoving();

                  sl<CurrentLatLngAddressBloc>().add(
                    CurrentLatLngAddressEvent.get(
                      position: _selectedLatLng ?? _currentLocation!,
                    ),
                  );
                },
                onMapCreated: (controller) {
                  _mapController = controller;
                  if (_currentLocation != null) {
                    _mapController?.animateCamera(
                      CameraUpdate.newCameraPosition(
                        CameraPosition(
                          target: _currentLocation!,
                          zoom: kMapInitialZoom,
                        ),
                      ),
                    );
                  }
                },
                initialCameraPosition: CameraPosition(
                  target: _currentLocation ?? LatLng(27.7172, 85.3240),
                  zoom: kMapInitialZoom,
                ),
              ),
            ),
          ),
          Positioned(
            top: 16,
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppStyles.space12,
                vertical: AppStyles.space32,
              ),
              child: const CustomBackButton(),
            ),
          ),
          _buildBottomBar(),
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(AppStyles.space16),
            decoration: BoxDecoration(
              color: T.c(context).surface,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 10,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: BlocBuilder<
              CurrentLatLngAddressBloc,
              CurrentLatLngAddressState
            >(
              builder: (context, state) {
                bool isLoadingCurrentLatLngAddr = false;
                String currentAddress = '';
                state.maybeWhen(
                  orElse: () {
                    isLoadingCurrentLatLngAddr = false;
                  },
                  loaded: (data) {
                    isLoadingCurrentLatLngAddr = false;
                    currentAddress = data;
                    _addressController.text = data;
                  },
                );

                return Column(
                  mainAxisSize: MainAxisSize.min,
                  spacing: 12,
                  children: [
                    CustomSearchBarWidget(
                      controller: _addressController,
                      placeholder:
                          isLoadingCurrentLatLngAddr
                              ? ' Searching address...'
                              : currentAddress,
                      onChanged: (text) {},
                    ),
                    // Divider(color: Colors.grey[300]),
                    _buildButton(),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  BlocListener<SetChoosedLocationBloc, SetChoosedLocationState> _buildButton() {
    return BlocListener<SetChoosedLocationBloc, SetChoosedLocationState>(
      listener: (context, state) {
        state.whenOrNull(
          loading: () => AppLoadingDialog.show(context),
          loaded: (data) {
            CustomToast.showSuccess(data);
            AppLoadingDialog.hide(context);
            context.pop();
          },
          failure: (failure) {
            CustomToast.showError(failure.message);
            AppLoadingDialog.hide(context);
          },
        );
      },
      child: CustomButtonPrimary(
        title: 'Confirm Home Address',
        onPressed: () {
          sl<SetChoosedLocationBloc>().add(
            SetChoosedLocationEvent.setHomeLocation(
              _selectedLatLng ?? _currentLocation!,
            ),
          );
        },
      ),
    );
  }
}
