import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/app_cubit/app_cubit.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/errors/location_failure.dart';
import 'package:safari_yatri/core/constant/image_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/location/blocs/location_permission/location_permission_bloc.dart';

import '../../../core/widget/custom_alertbox.dart';
import '../../../core/widget/custom_platform_scaffold.dart';

class LocationPermissionPage extends StatefulWidget {
  const LocationPermissionPage({super.key});

  @override
  State<LocationPermissionPage> createState() => _LocationPermissionPageState();
}

class _LocationPermissionPageState extends State<LocationPermissionPage>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late final AnimationController _lottieController;
  bool _isDialogOpened = false;
  @override
  void initState() {
    super.initState();
    // sl<LocationPermissionBloc>().add(
    //   LocationPermissionEvent.checkPermissionStatus(),
    // );
    WidgetsBinding.instance.addObserver(this);
    _lottieController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 6),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      sl<LocationPermissionBloc>().add(
        LocationPermissionEvent.checkPermissionStatus(),
      );
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _lottieController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      body: BlocConsumer<LocationPermissionBloc, LocationPermissionState>(
        listener: (context, state) {
          state.whenOrNull(
            loaded: (grantedStatus) {
              persistenceSl<AppCubit>().locationPermissionStatus(true);
              context.goNamed(AppRoutesName.roleBaseNavigatorPage);
            },
            failure: (failure) {
              if (_isDialogOpened) {
                _isDialogOpened = false;
                Navigator.of(context).pop();
              }
              return _showErrorDialog(failure);
            },
          );
        },
        builder:
            (context, state) => Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: AppStyles.space64),

                  Center(
                    child: Icon(
                      Icons.location_on,
                      color: T.c(context).primaryFixed,
                      size: MediaQuery.sizeOf(context).height * 0.45,
                    ),
                  ),

                  Text(
                    L.t.locationPermissionScreenTitle,
                    textAlign: TextAlign.center,
                    style: TextTheme.of(context).headlineSmall,
                  ),
                  Text(
                    L.t.locationPermissionScreenDescription,
                    textAlign: TextAlign.center,
                  ),
                  Spacer(),
                  CustomButtonPrimary(
                    title: L.t.locationPermissionScreenGivePermissionButton,
                    onPressed: () {
                      sl<LocationPermissionBloc>().add(
                        const LocationPermissionEvent.checkAndRequestPermission(),
                      );
                    },
                  ),
                  Gap(20),
                ],
              ),
            ),
      ),
    );
  }

  Future<void> _showErrorDialog(Failure failure) async {
    setState(() {
      _isDialogOpened = true;
    });
    // String title = "Location Permission Required";
    String message = L.t.locationPermissionScreenDialogMessage;
    // String description = failure.message;
    String assetName = ImageConstant.appLogo;
    List<Widget> actions = [];

    if (failure is LocationPermissionDeniedFailure ||
        failure is LocationPermissionLimitedFailure ||
        failure is LocationPermissionProvisionalFailure ||
        failure is LocationFailureWithMessage) {
      actions.add(
        CustomButtonPrimary(
          onPressed: () {
            // Navigator.pop(context);
            sl<LocationPermissionBloc>().add(
              const LocationPermissionEvent.checkAndRequestPermission(),
            );
          },
          title: L.t.locationPermissionScreenDialogGivePermission,
        ),
      );
    } else if (failure is LocationPermissionPermanentlyDeniedFailure ||
        failure is LocationPermissionRestrictedFailure) {
      actions.add(
        CustomTextButton(
          title: L.t.locationPermissionScreenDialogOpenSettings,
          onPressed: () {
            sl<LocationPermissionBloc>().add(
              const LocationPermissionEvent.openAppSettings(),
            );
          },
        ),
      );
    }
    showCustomDialog(
      context: context,
      assetName: assetName,
      message: message,
      actions: actions,
      canPop: true,
    );
  }
}
