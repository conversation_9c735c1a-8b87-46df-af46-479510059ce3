import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/common/blocs/locale_cubit/locale_cubit.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';
import 'package:safari_yatri/features/drawer/widget/setting_menu_tile.dart';

import '../../../core/utils/localization_utils.dart';
import '../../../core/widget/custom_platform_scaffold.dart';

class SelectLanguagePage extends StatefulWidget {
  const SelectLanguagePage({super.key});

  @override
  State<SelectLanguagePage> createState() => _SelectLanguagePageState();
}

class _SelectLanguagePageState extends State<SelectLanguagePage> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LocaleCubit, Locale>(
      builder: (context, state) {
        return PlatformScaffold(
          appBar: CustomAppBar(
            title: Text(L.t.drawerScreenSettingsSelectLanguage),
          ),
          body: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Column(
              children: [
                MenuTile(
                  title: 'English',
                  subTitle: 'English',
                  isShowTrailing: true,
                  trailing:
                      state.languageCode == 'en'
                          ? const Icon(Icons.check, color: Colors.green)
                          : null,
                  onTap: () {
                    persistenceSl<LocaleCubit>().changeLocale(const Locale('en', 'US'));
                  },
                ),
                const SizedBox(height: AppStyles.space16),
                MenuTile(
                  title: 'नेपाली',
                  subTitle: 'Nepali',
                  isShowTrailing: true,
                  trailing:
                      state.languageCode == 'ne'
                          ? const Icon(Icons.check, color: Colors.green)
                          : null,
                  onTap: () {
                    persistenceSl<LocaleCubit>().changeLocale(const Locale('ne'));
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
