import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';

import '../../../core/widget/custom_platform_scaffold.dart';
import '../../../core/widget/custom_switch.dart';
import '../widget/setting_menu_tile.dart';

class NotificationPage extends StatelessWidget {
  const NotificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      appBar: CustomAppBar(title: Text(L.t.notificationScreenAppBarTitle)),
      body: Column(
        children: [
          MenuTile(
            title: L.t.notificationScreenRideRequestsTitle,
            subTitle: L.t.notificationScreenRideRequestsSubtitle,
            trailing: Transform.scale(
              scale: 0.75, // Smaller size
              child: CustomSwitch(value: false, onChanged: (value) {}),
            ),
          ),
          MenuTile(
            title: L.t.notificationScreenPromotionsTitle,
            subTitle: L.t.notificationScreenPromotionsSubtitle,
            trailing: Transform.scale(
              scale: 0.75, // Smaller size
              child: CustomSwitch(value: true, onChanged: (value) {}),
            ),
          ),
          MenuTile(
            title: L.t.notificationScreenEarningsTitle,
            subTitle: L.t.notificationScreenEarningsSubtitle,
            trailing: Transform.scale(
              scale: 0.75, // Smaller size
              child: CustomSwitch(value: false, onChanged: (value) {}),
            ),
          ),
          MenuTile(
            title: L.t.notificationScreenSafetyTitle,
            subTitle: L.t.notificationScreenSafetySubtitle,
            trailing: Transform.scale(
              scale: 0.75, // Smaller size
              child: CustomSwitch(value: true, onChanged: (value) {}),
            ),
          ),
        ],
      ),
    );
  }
}
