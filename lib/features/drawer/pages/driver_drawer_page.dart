import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/app_cubit/app_cubit.dart';
import 'package:safari_yatri/core/constant/string_constant.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/features/drawer/widget/drawer_header_widget.dart';
import 'package:safari_yatri/features/passenger/core/blocs/pick_location_picking_status/pickup_location_picking_status_cubit.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/common/blocs/local_user_mode/local_user_mode_cubit.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';

import '../../../core/router/app_route_names.dart';
import '../../../core/widget/custom_button.dart';
import '../../my_profile/bloc/get_my_profile/my_profile_bloc.dart';
import '../widget/drawer_menu_widget.dart';
import '../widget/version_text.dart';

class DriverDrawer extends StatefulWidget {
  const DriverDrawer({super.key});

  @override
  State<DriverDrawer> createState() => _DriverDrawerState();
}

class _DriverDrawerState extends State<DriverDrawer> {
  @override
  initState() {
    super.initState();
    sl<MyProfileBloc>().add(const MyProfileEvent.get(false));
  }

  @override
  Widget build(BuildContext context) {
    return buildDriverAppDrawer(context);
  }

  Widget buildDriverAppDrawer(BuildContext context) {
    return SafeArea(
      child: Drawer(
        child: CustomDrawerHeader(widget: _buildDriverDrawerBody(context)),
      ),
    );
  }

  Widget _buildDriverDrawerBody(BuildContext context) {
    return Column(
      children: [
        DrawerWidget(
          title: L.t.driverDrawerScreenHomeTitle,
          icons: LucideIcons.house,
          onTap: () {
            Navigator.of(context).pop();
          },
        ),
        DrawerWidget(
          title: L.t.driverDrawerScreenMyTripsTitle,
          icons: LucideIcons.mapPin,
          onTap: () => context.pushNamed(AppRoutesName.myPassengerTrips),
        ),

        // DrawerWidget(
        //   title: "My Earnings",
        //   icons: LucideIcons.mapPin,
        //   onTap: () {},
        // ),
        // DrawerWidget(
        //   title: "Payment Methods",
        //   icons: LucideIcons.creditCard,
        //   onTap: () {
        //     context.pushNamed(AppRoutesName.paymentMethodPage);
        //   },
        // ),

        // Divider(thickness: 1, color: dividerColor),
        DrawerWidget(
          title: L.t.drawerScreenSettingsTitle,
          icons: LucideIcons.settings,
          onTap: () {
            context.pushNamed(AppRoutesName.setting);
          },
        ),
        // DrawerWidget(
        //   title: "Help Center",
        //   icons: LucideIcons.handHelping,
        //   onTap: () {},
        // ),
        // Divider(thickness: 1, color: dividerColor),
        DrawerWidget(
          title: L.t.drawerScreenEmergencySafetyTitle,
          icons: LucideIcons.shieldCheck,
          onTap: () {
            context.pushNamed(AppRoutesName.emergencyAndSaftey);
          },
        ),
        const Spacer(),
        Padding(
          padding: const EdgeInsets.all(AppStyles.space12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const AppVersionText(),
              SizedBox(height: AppStyles.space4),
              BlocConsumer<LocalUserModeCubit, LocalUserModeState>(
                listener: (context, state) async {
                  state.whenOrNull(
                    failure: (failure) {
                      AppLoadingDialog.hide(context);
                      CustomToast.showError(failure.message);
                    },
                    loading: () {
                      AppLoadingDialog.show(context);
                    },
                    loaded: (value) {
                      AppLoadingDialog.hide(context);
                      persistenceSl<AppCubit>().switchToLocalUserMode(kPassengerRole);

                      context.goNamed(AppRoutesName.passengerHome);
                    },
                  );
                },
                builder: (context, state) {
                  return CustomButtonPrimary(
                    title: L.t.driverDrawerScreenPassengerModeButton,
                    onPressed: () {
                      sl<PickupLocationPickingStatusCubit>().enable();
                      sl<LocalUserModeCubit>().switchPassengerMode();
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
