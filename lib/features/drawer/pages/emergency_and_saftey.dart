import 'package:flutter/material.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/utils/url_launcher.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

import '../../../core/utils/theme_utils.dart';
import '../../../core/widget/custom_platform_scaffold.dart';

class EmergencyAndSafety extends StatefulWidget {
  const EmergencyAndSafety({super.key});

  @override
  State<EmergencyAndSafety> createState() => _EmergencyAndSafetyState();
}

class _EmergencyAndSafetyState extends State<EmergencyAndSafety> {
  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      appBar: CustomAppBar(title: Text(L.t.emergencyScreenAppBarTitle)),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Top buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildTopButton(
                    Icons.chat_bubble_outline,
                    L.t.emergencyScreenSupportButton,
                  ),
                  _buildTopButton(
                    Icons.people_outline,
                    L.t.emergencyScreenEmergencyContactsButton,
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Call 100 button
              CustomButtonPrimary(
                title: L.t.emergencyScreenCall100Button,
                fontSize: 18,
                fonwtWeight: FontWeight.bold,
                backgroundColor: T.c(context).error,
                onPressed: () {
                  urlLauncher(Uri(scheme: 'tel', path: '100'));
                },
                leadingIcon: Icon(
                  LucideIcons.triangleAlert,
                  color: T.c(context).surface,
                ),
              ),

              const SizedBox(height: 20),

              // Section title
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  L.t.emergencyScreenHowYoureProtectedTitle,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(height: 12),

              // Grid of features
              GridView.count(
                physics: NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                crossAxisCount: 2,
                mainAxisSpacing: 6,
                crossAxisSpacing: 12,
                childAspectRatio: 1,
                children: [
                  _buildProtectionCard(
                    L.t.emergencyScreenFeatureProactiveSafetySupport,
                    // ImageConstant.safateySupport,
                  ),
                  _buildProtectionCard(
                    L.t.emergencyScreenFeatureDriversVerification,
                    // ImageConstant.driverVerification,
                  ),
                  _buildProtectionCard(
                    L.t.emergencyScreenFeatureProtectingPrivacy,
                    // ImageConstant.privacy,
                  ),
                  _buildProtectionCard(
                    L.t.emergencyScreenFeatureStayingSafe,
                    // ImageConstant.safeSide,
                  ),
                  _buildProtectionCard(
                    L.t.emergencyScreenFeatureAccidentsSteps,
                    // ImageConstant.accidents,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopButton(IconData icon, String label) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.symmetric(vertical: 14),
        decoration: BoxDecoration(
          color: T.c(context).surfaceContainerHigh,
          borderRadius: AppStyles.radiusMd,
        ),
        child: Column(
          children: [
            Icon(icon, size: 28),
            const SizedBox(height: 6),
            Text(
              label,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProtectionCard(String title) {
    return Container(
      decoration: BoxDecoration(),
      padding: const EdgeInsets.all(6),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: T.c(context).surfaceContainerHigh,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Transform.rotate(
            //   angle: -0.2,
            //   child: Container(
            //     padding: const EdgeInsets.all(8),
            //     decoration: BoxDecoration(
            //       color: const Color.fromARGB(255, 61, 189, 56), // light green
            //       borderRadius: BorderRadius.circular(6),
            //     ),
            //     child: Image.asset(imagePath ?? '', height: 60),
            //   ),
            // ),
            const SizedBox(height: 10),
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
