import 'package:flutter/material.dart';
import 'package:safari_yatri/core/constant/string_constant.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/bottom_sheet.dart';
import 'package:safari_yatri/core/widget/logout_cubit_listener.dart';
import 'package:safari_yatri/core/widget/phone_number_reveal.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../../core/utils/image_utils.dart';
import '../../../core/widget/custom_chip.dart';
import 'view_profile_dialog.dart';

class DrawerHeaderWidget extends StatelessWidget {
  const DrawerHeaderWidget({
    super.key,
    required this.textTheme,
    required this.name,
    this.imageUrl,
    required this.phoneNumber,
    required this.rating,
    required this.chipTitle,
  });

  final TextTheme textTheme;
  final String name;
  final String? imageUrl;
  final String phoneNumber;
  final double rating;
  final String chipTitle;

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return LogoutCubitListener(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.only(bottom: 20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors:
                isDark
                    ? [colors.surfaceContainer.withAlpha(204), colors.surface]
                    : [colors.primaryContainer.withAlpha(26), colors.surface],
          ),
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(24),
            bottomRight: Radius.circular(24),
          ),
        ),
        child: Stack(
          children: [
            // Background decoration
            Positioned(
              top: -50,
              right: -50,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: colors.primary.withAlpha(13),
                ),
              ),
            ),
            // Main content - Row layout
            Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(AppStyles.space12),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Profile Picture - Left side
                            Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    colors.primary.withAlpha(26),
                                    colors.primary.withAlpha(13),
                                  ],
                                ),
                                border: Border.all(
                                  color: colors.primary.withAlpha(31),
                                  width: 2,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: colors.primary.withAlpha(20),
                                    blurRadius: 16,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: CircleAvatar(
                                radius: 32, // Made smaller to fit the row layout
                                backgroundColor: colors.surfaceContainerHighest,
                                child: ClipOval(
                                  child: SizedBox(
                                    width: 64,
                                    height: 64,
                                    child: ImageUtility.displayImageFromBase64(
                                      imageUrl,
                                    ),
                                  ),
                                ),
                              ),
                            ),
      
                            const SizedBox(width: 16),
      
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    name,
                                    style: textTheme.titleLarge?.copyWith(
                                      color: colors.onSurface,
                                      fontWeight: FontWeight.w700,
                                      letterSpacing: -0.5,
                                      height: 1.2,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
      
                                  PhoneNumberReveal(phoneNumber: phoneNumber),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      CustomChipWidget(
                                        chipTitle:
                                            chipTitle ==
                                                    kPendingRequestForRiderRole
                                                ? kPassengerRole
                                                : chipTitle,
                                      ),
                                      SizedBox(width: 6),
                                      // Rating Badge
                                      // _rating(colors),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () {
                    appShowModalBottomSheet(context, child: ViewProfileDialog());
                  },
                  icon: Icon(LucideIcons.ellipsisVertical),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Container _rating(ColorScheme colors) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 0.5),
      decoration: BoxDecoration(
        color: colors.primaryContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colors.primary.withAlpha(38), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.star_rounded, color: colors.primary, size: 14),
          const SizedBox(width: 4),
          Text(
            rating.toStringAsFixed(1),
            style: textTheme.labelMedium?.copyWith(
              color: colors.onPrimaryContainer,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
