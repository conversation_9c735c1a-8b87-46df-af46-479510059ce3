import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/widget/logout_dialog.dart';

import '../../../core/router/app_route_names.dart';
import '../../../core/utils/localization_utils.dart';

class ViewProfileDialog extends StatefulWidget {
  const ViewProfileDialog({super.key});

  @override
  State<ViewProfileDialog> createState() => _ViewProfileDialogState();
}

class _ViewProfileDialogState extends State<ViewProfileDialog> {


  @override
  void initState() {
    super.initState();

    
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: _decoration(context),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _editProfile(context),
    
                const SizedBox(height: 12),
    
                _logout(context),
    
                const SizedBox(height: 16),
    
                _buildCancel(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  SizedBox _buildCancel(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton(
        onPressed: () {
          Navigator.of(context).pop();
        },
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          side: BorderSide(color: T.c(context).outline),
        ),
        child: Text(
          L.t.viewProfileDialogCancel,
          style: T
              .t(context)
              .labelLarge
              ?.copyWith(color: T.c(context).onSurfaceVariant),
        ),
      ),
    );
  }

  Container _logout(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: T.c(context).surfaceContainerHighest.withAlpha(128),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: T.c(context).outline.withAlpha(51)),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: T.c(context).error.withAlpha(26),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.logout_outlined,
            color: T.c(context).error,
            size: 20,
          ),
        ),
        title: Text(
          L.t.viewProfileDialogLogout,
          style: T
              .t(context)
              .bodyLarge
              ?.copyWith(
                fontWeight: FontWeight.w600,
                color: T.c(context).error,
              ),
        ),
        subtitle: Text(
          L.t.viewProfileDialogLogoutSubtitle,
          style: T
              .t(context)
              .bodySmall
              ?.copyWith(color: T.c(context).onSurfaceVariant),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: T.c(context).onSurfaceVariant,
        ),
        onTap: () {
          Navigator.of(context).pop();
          // Show logout confirmation dialog
          _showLogoutConfirmation(context);
        },
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Container _editProfile(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: T.c(context).surfaceContainerHighest.withAlpha(128),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: T.c(context).outline.withAlpha(51)),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: T.c(context).primary.withAlpha(26),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.edit_outlined,
            color: T.c(context).primary,
            size: 20,
          ),
        ),
        title: Text(
          L.t.viewProfileDialogEditProfile,
          style: T
              .t(context)
              .bodyLarge
              ?.copyWith(
                fontWeight: FontWeight.w600,
                color: T.c(context).onSurface,
              ),
        ),
        // subtitle: Text(
        //   L.t.viewProfileDialogUpdateInfo,
        //   style: T
        //       .t(context)
        //       .bodySmall
        //       ?.copyWith(color: T.c(context).onSurfaceVariant),
        // ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: T.c(context).onSurfaceVariant,
        ),
        onTap: () {
          Navigator.of(context).pop();
          context.pushNamed(AppRoutesName.profileScreen);
        },
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  BoxDecoration _decoration(BuildContext context) {
    return BoxDecoration(
      color: T.c(context).surface,
      borderRadius: BorderRadius.circular(24),
      boxShadow: [
        BoxShadow(
          color: T.c(context).shadow.withAlpha(51),
          blurRadius: 24,
          offset: const Offset(0, 8),
        ),
      ],
    );
  }

  Future<void> _showLogoutConfirmation(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return LogoutAlertDialog();
      },
    );
  }
}
