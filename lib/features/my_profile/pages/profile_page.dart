import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/date_time_utils.dart';
import 'package:safari_yatri/core/utils/image_utils.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_form_field.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/my_profile/bloc/get_my_profile/my_profile_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/update_my_profile/update_my_profile_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/upload_profile_image/upload_profile_image_bloc.dart';
import 'package:safari_yatri/features/my_profile/model/update_user_data_model.dart';
import '../../../core/di/dependency_injection.dart';
import '../../../core/utils/localization_utils.dart';
import '../../../core/widget/custom_platform_scaffold.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  bool isEditing = false;
  XFile? profileImage;

  // Text controllers
  TextEditingController userNameController = TextEditingController();
  TextEditingController emailAddressController = TextEditingController();
  TextEditingController genderController = TextEditingController();
  TextEditingController userAddressController = TextEditingController();

  @override
  void dispose() {
    userNameController.dispose();

    emailAddressController.dispose();
    genderController.dispose();
    userAddressController.dispose();
    super.dispose();
  }

  bool forceFetchCalled = false;

  void forceFetchProfile() {
    if (forceFetchCalled) return;
    forceFetchCalled = true;
    sl<MyProfileBloc>().add(MyProfileEvent.get(true));
  }

  void _updateProfile() {
    UpdateUserDataModel userDataModel = UpdateUserDataModel(
      userName: userNameController.text,
      userAddress: userAddressController.text,
      emailAddress: emailAddressController.text,
      gender: genderController.text,
    );

    sl<UpdateMyProfileBloc>().add(
      UpdateMyProfileEvent.updateMyProfile(userDataModel),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'online':
        return Colors.green;
      case 'inactive':
      case 'offline':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      default:
        return T.c(context).onSurfaceVariant;
    }
  }

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;

    return PlatformScaffold(
      backgroundColor: colorScheme.surface,
      body: MultiBlocListener(
        listeners: [
          BlocListener<UploadProfileImageBloc, UploadProfileImageState>(
            listener: (context, state) {
              state.whenOrNull(
                loading: () {
                  CustomToast.showInfo(L.t.profilePageUploadingImage);
                },
                failure: (failure) {
                  CustomToast.showError(failure.message);
                },
                loaded: (data) {
                  CustomToast.showSuccess(L.t.profilePageProfilePictureUpdated);
                  forceFetchProfile();
                  setState(() {
                    profileImage = null;
                  });
                  context.pop();
                },
              );
            },
          ),
          BlocListener<UpdateMyProfileBloc, UpdateMyProfileState>(
            listener: (context, state) {
              state.whenOrNull(
                loading: () => AppLoadingDialog.show(context),
                failure: (failure) {
                  AppLoadingDialog.hide(context);
                  CustomToast.showError(failure.message);
                },
                loaded: (data) {
                  AppLoadingDialog.hide(context);
                  CustomToast.showSuccess(L.t.profilePageProfileUpdated);
                  forceFetchProfile();
                  setState(() {
                    isEditing = false;
                  });
                  context.pop();
                },
              );
            },
          ),
        ],
        child: BlocBuilder<MyProfileBloc, MyProfileState>(
          builder: (context, state) {
            return state.when(
              initial: () => const SizedBox(),
              loading:
                  () => Center(
                    child: CircularProgressIndicator(
                      color: colorScheme.primary,
                    ),
                  ),
              loaded: (data) {
                // Populate controllers with data
                userNameController.text = data.userName;

                emailAddressController.text = data.emailAddress ?? "";
                userAddressController.text = data.address ?? 'No Address';
                genderController.text = data.gender;

                return RefreshIndicator(
                  color: colorScheme.primary,
                  onRefresh: () async {
                    sl<MyProfileBloc>().add(MyProfileEvent.get(true));
                  },
                  child: _buildUserDetailView(colorScheme, data),
                );
              },
              failure: (failure) {
                return ErrorWidgetWithRetry(
                  failure: failure,
                  onRetry:
                      () => sl<MyProfileBloc>().add(MyProfileEvent.get(true)),
                );
              },
            );
          },
        ),
      ),
      bottomNavigationBar:
          isEditing
              ? Padding(
                padding: const EdgeInsets.all(8.0),
                child: CustomButtonPrimary(
                  title: L.t.profilePageSaveChanges,
                  onPressed: _updateProfile,
                ),
              )
              : null,
    );
  }

  Widget _buildUserDetailView(ColorScheme colorScheme, dynamic data) {
    return CustomScrollView(
      slivers: [
        _buildSliverAppBar(colorScheme, data),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppStyles.space8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildPersonalInfoCard(colorScheme, data),
                _buildAccountStatusCard(colorScheme, data),
                _buildLocationCard(colorScheme, data),
                // _buildWalletCard(colorScheme, data),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSliverAppBar(ColorScheme colorScheme, dynamic data) {
    return SliverAppBar(
      expandedHeight: 320,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      actions: [
        Container(
          margin: const EdgeInsets.only(right: 16),
          height: 40,
          width: 40,
          decoration: BoxDecoration(
            color: colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            onPressed: () {
              setState(() {
                isEditing = !isEditing;
              });
            },
            icon: Icon(
              isEditing ? Icons.close : Icons.edit,
              color: colorScheme.primary,
              size: 16,
            ),
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                colorScheme.primaryContainer.withOpacity(0.8),
                colorScheme.surface,
              ],
            ),
          ),
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 60),
                _buildProfileAvatar(colorScheme, data),
                const SizedBox(height: 16),
                Text(
                  data.userName,
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(data.loginStatus).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _getStatusColor(data.loginStatus),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    data.loginStatus.toUpperCase(),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: _getStatusColor(data.loginStatus),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileAvatar(ColorScheme colorScheme, dynamic data) {
    return Hero(
      tag: 'user_avatar_${data.userId}',
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: colorScheme.primary, width: 3),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: ClipOval(
              child: SizedBox(
                height: 120,
                width: 120,
                child:
                    profileImage != null
                        ? ImageUtility.displayImageFromXFile(profileImage)
                        : ImageUtility.displayImageFromBase64(
                          data.profilePicture,
                        ),
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            right: 0,
            child: GestureDetector(
              onTap: () async {
                profileImage = await ImageUtility.showImageSourceDialog(
                  context,
                );
                if (profileImage != null) {
                  sl<UploadProfileImageBloc>().add(
                    UploadProfileImageEvent.uploadProfileImage(profileImage!),
                  );
                }
                setState(() {});
              },
              child: Container(
                height: 36,
                width: 36,
                decoration: BoxDecoration(
                  color: colorScheme.primary,
                  shape: BoxShape.circle,
                  border: Border.all(color: colorScheme.surface, width: 2),
                ),
                child: Icon(
                  Icons.camera_alt,
                  size: 18,
                  color: colorScheme.onPrimary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoCard(ColorScheme colorScheme, dynamic data) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: colorScheme.primary, size: 24),
                const SizedBox(width: 8),
                Text(
                  L.t.profilePagePersonalInformation,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            CustomFormFieldWidget(
              label: L.t.profilePageFullName,
              prefixIcon: Icons.person,
              controller: userNameController,
              enabled: isEditing,
            ),
            const SizedBox(height: 12),
            PhoneNumberRevealingTextField(phoneNumber: data.phoneNo),
            const SizedBox(height: 12),
            CustomFormFieldWidget(
              label: L.t.profilePageEmailAddress,
              prefixIcon: Icons.email,
              controller: emailAddressController,
              enabled: isEditing,
            ),
            const SizedBox(height: 12),
            CustomFormFieldWidget(
              label: L.t.profilePageGender,
              prefixIcon: Icons.people,
              controller: genderController,
              enabled: isEditing,
            ),
            const SizedBox(height: 12),
            CustomFormFieldWidget(
              label: L.t.profilePageCurrentAddress,
              prefixIcon: Icons.location_on,
              controller: userAddressController,
              enabled: isEditing,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountStatusCard(ColorScheme colorScheme, dynamic data) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.shield, color: colorScheme.primary, size: 24),
                const SizedBox(width: 8),
                Text(
                  L.t.profilePageAccountStatus,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoTile(
              icon: Icons.person,
              title: L.t.profilePageUserType,
              value: data.userType.toUpperCase(),
              color: _getStatusColor(data.userType),
            ),
            _buildInfoTile(
              icon: Icons.ac_unit,
              title: L.t.profilePageLoginStatus,
              value: data.loginStatus.toUpperCase(),
              color: _getStatusColor(data.loginStatus),
            ),
            _buildInfoTile(
              icon: Icons.calendar_today,
              title: L.t.profilePageMemberSince,
              value: AppDateTimeUtils.getStringDateTime(
                data.registeredDate,
                data.registeredTime,
              ),
            ),
            _buildInfoTile(
              icon: Icons.check_circle,
              title: L.t.profilePageAccountActivated,
              value: AppDateTimeUtils.getStringDateTime(
                data.activationDate,
                data.activationTime,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationCard(ColorScheme colorScheme, dynamic data) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.location_on, color: colorScheme.primary, size: 24),
                const SizedBox(width: 8),
                Text(
                  L.t.profilePageLocationSettings,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withOpacity(0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildLocationButton(
                        icon: Icons.home,
                        label: L.t.profilePageHome,
                        subLabel: L.t.profilePageSetAddress,
                        onTap:
                            isEditing
                                ? () {
                                  context.pushNamed(
                                    AppRoutesName.setHomeLocation,
                                    extra: {
                                      'initialLocation': LatLng(
                                        data.homeLatitude,
                                        data.homeLongitude,
                                      ),
                                    },
                                  );
                                }
                                : null,
                        colorScheme: colorScheme,
                      ),
                      // Container(
                      //   width: 1,
                      //   height: 48,
                      //   color: colorScheme.outline,
                      //   margin: const EdgeInsets.symmetric(horizontal: 16),
                      // ),
                      // _buildLocationButton(
                      //   icon: Icons.work,
                      //   label: 'Work',
                      //   subLabel: 'Set Address',
                      //   onTap:
                      //       isEditing
                      //           ? () {
                      //             CustomToast.showInfo(
                      //               'This feature is currently not available',
                      //             );
                      //           }
                      //           : null,
                      //   colorScheme: colorScheme,
                      // ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Row(
                  //   children: [
                  //     Expanded(
                  //       child: _buildLocationInfo(
                  //         'Home Location',
                  //         '${data.homeLatitude.toStringAsFixed(6)}, ${data.homeLongitude.toStringAsFixed(6)}',
                  //         Icons.home,
                  //         colorScheme,
                  //       ),
                  //     ),
                  //     const SizedBox(width: 16),
                  //     Expanded(
                  //       child: _buildLocationInfo(
                  //         'Current Location',
                  //         '${data.currentLatitude.toStringAsFixed(6)}, ${data.currentLongitude.toStringAsFixed(6)}',
                  //         Icons.navigation,
                  //         colorScheme,
                  //       ),
                  //     ),
                  //   ],
                  // ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoTile({
    required IconData icon,
    required String title,
    required String value,
    Color? color,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: T.c(context).surfaceContainerLow,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, size: 16, color: T.c(context).onSurfaceVariant),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: T.c(context).onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: color ?? T.c(context).onSurface,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationButton({
    required IconData icon,
    required String label,
    required String subLabel,
    required void Function()? onTap,
    required ColorScheme colorScheme,
  }) {
    final bool isEnabled = onTap != null;

    return Opacity(
      opacity: isEnabled ? 1.0 : 0.5,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: colorScheme.surfaceContainerHighest,
              radius: 20,
              child: Icon(icon, size: 20, color: colorScheme.onSurface),
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
                Text(
                  subLabel,
                  style: TextStyle(
                    fontSize: 12,
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
