part of 'get_my_accepting_riders_bloc.dart';

@freezed
sealed class GetMyAcceptingRidersEvent with _$GetMyAcceptingRidersEvent {
  const factory GetMyAcceptingRidersEvent.get() = _Get;
  const factory GetMyAcceptingRidersEvent.stop() = _Stop;
  const factory GetMyAcceptingRidersEvent.resume() = _Resume;
  const factory GetMyAcceptingRidersEvent.reset() = _Reset;
  const factory GetMyAcceptingRidersEvent.removeRiderLocally(int riderId) =
      _RemoveRider;
}
