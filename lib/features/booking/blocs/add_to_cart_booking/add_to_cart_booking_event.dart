part of 'add_to_cart_booking_bloc.dart';

@freezed
abstract class AddToCartBookingEvent with _$AddToCartBookingEvent {


  const factory AddToCartBookingEvent.create({
    required double passengerOfferedFare,
    required int maxRiderSearchDistanceInMeter,
  }) = _Create;

  const factory AddToCartBookingEvent.shareRidingMode({
    required bool isShareRidingMode,
  }) = _ShareRidingMode;

  const factory AddToCartBookingEvent.inititalizeRoutes({
    required DirectionRouteModel direction,
    required List<LatLngWithAddress> locations,
  }) = InititalizeRoutes;

  const factory AddToCartBookingEvent.scheduleTripDateTime({
    required DateTime scheduleDataTime,
  }) = _ScheduleTripDateTime;

  const factory AddToCartBookingEvent.initialPassengerCount({
    required int passengerCount,
  }) = _InitialPassengerCount;

  const factory AddToCartBookingEvent.raiseFare({
    required double passengerOfferedFare,
  }) = _RaiseFare;

  const factory AddToCartBookingEvent.expandSearchDiameter({
    required int maxDriverSearchingAreaInMeter,
  }) = _ExpandSearchDiameter;
}
