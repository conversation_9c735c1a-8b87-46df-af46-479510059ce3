import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/services/polling/polling_service.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'get_my_current_passengers_booking_event.dart';
part 'get_my_current_passengers_booking_state.dart';
part 'get_my_current_passengers_booking_bloc.freezed.dart';

///Yo chaii aba passenger le accept garxa rider ko booking taba aauxa yo section maa
class GetMyCurrentPassengerBookingBloc
    extends
        Bloc<
          GetMyCurrentPassengersBookingEvent,
          GetMyCurrentPassengersBookingState
        > {
  final PollingService _pollingService;
  final BookingRepository _bookingRepository;
  final String _pollingId = "GetMyPassengersBooking";
  bool _isInitialized = false;

  GetMyCurrentPassengerBookingBloc({
    required PollingService pollingService,
    required BookingRepository repo,
  }) : _pollingService = pollingService,
       _bookingRepository = repo,
       super(GetMyCurrentPassengersBookingState.initial()) {
    if (!_pollingService.hasTask(_pollingId)) {
      _pollingService.register(
        id: _pollingId,
        immediate: false,
        onPoll: (date) {
          add(GetMyCurrentPassengersBookingEvent.getCurrentBooking());
        },
        interval: const Duration(seconds: 10),
      );
    }
    _pollingService.start(_pollingId);

    on<_GetCurrentBooking>(_onGetCurrentBooking);
    on<_StopCurrentBookings>(_onStop);
    on<_ResumeCurrentBookings>(_onResume);
    on<_Reset>(_onReset);
  }

  Future<void> _onGetCurrentBooking(
    _GetCurrentBooking event,
    Emitter<GetMyCurrentPassengersBookingState> emit,
  ) async {
    if (!_isInitialized) {
      _isInitialized = true;
      emit(GetMyCurrentPassengersBookingState.loading());
    }

    final result = await _bookingRepository.getMyPassengerBookings();
    result.fold(
      (failure) => emit(GetMyCurrentPassengersBookingState.failure(failure)),
      (bookings) => _onSuccess(
        emit,
        isCurrentOngoingRide: true,
        myPassengersBooking: bookings,
      ),
    );
  }

  Future<void> _onStop(
    _StopCurrentBookings event,
    Emitter<GetMyCurrentPassengersBookingState> emit,
  ) async {
    _pollingService.stop(_pollingId);
    
  }

  Future<void> _onResume(
    _ResumeCurrentBookings event,
    Emitter<GetMyCurrentPassengersBookingState> emit,
  ) async {
    await _pollingService.start(_pollingId);
  }

  void _onSuccess(
    Emitter<GetMyCurrentPassengersBookingState> emit, {
    bool isCurrentOngoingRide = false,
    required List<BookingModel> myPassengersBooking,
  }) {
    emit(GetMyCurrentPassengersBookingState.loaded(myPassengersBooking));
  }

  Future<void> _onReset(
    _Reset event,
    Emitter<GetMyCurrentPassengersBookingState> emit,
  ) async {
    emit(GetMyCurrentPassengersBookingState.initial());
  }
}
