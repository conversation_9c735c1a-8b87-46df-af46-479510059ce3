part of 'get_my_current_passengers_booking_bloc.dart';

@freezed
abstract class GetMyCurrentPassengersBookingEvent
    with _$GetMyCurrentPassengersBookingEvent {
  const factory GetMyCurrentPassengersBookingEvent.getCurrentBooking() =
      _GetCurrentBooking;
  const factory GetMyCurrentPassengersBookingEvent.stopCurrentBookings() =
      _StopCurrentBookings;

  const factory GetMyCurrentPassengersBookingEvent.resumeCurrentBookign() =
      _ResumeCurrentBookings;

  const factory GetMyCurrentPassengersBookingEvent.reset() = _Reset;

  // const factory GetMyPassengersBookingEvent.getBookings({
  //   required bool includeCancelled,
  // }) = _GetBookings;
}
