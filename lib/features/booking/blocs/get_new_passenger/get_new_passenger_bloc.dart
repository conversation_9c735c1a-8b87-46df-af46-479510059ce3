import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/services/polling/polling_service.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/models/new_passenger_view_model.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'get_new_passenger_event.dart';
part 'get_new_passenger_state.dart';
part 'get_new_passenger_bloc.freezed.dart';

class GetNewPassengerBloc
    extends Bloc<GetNewPassengerEvent, GetNewPassengerState> {
  final PollingService _pollingService;
  final BookingRepository _bookingRepository;
  bool _isInitialized = false;
  // List<NewPassengerModel> _previousData = [];
  static const String pollingId = 'get_new_passenger';

  GetNewPassengerBloc({
    required PollingService pollingService,
    required BookingRepository bookingRepository,
  }) : _pollingService = pollingService,
       _bookingRepository = bookingRepository,
       super(const GetNewPassengerState.initial()) {
    on<_GetNewPassenger>(_onGetNewPassenger);
    on<_StopListening>(_onStopListening);
    on<_ResumeListening>(_onResumeListening);

    if (!_pollingService.hasTask(pollingId)) {
      _pollingService.register<void>(
        id: pollingId,
        interval: const Duration(seconds: 30),
        immediate: false,
        onPoll: (time) {
          add(GetNewPassengerEvent.get());
        },
      );
    }

    _pollingService.start(pollingId);
  }

  Future<void> _onGetNewPassenger(
    _GetNewPassenger event,
    Emitter<GetNewPassengerState> emit,
  ) async {
    if (!_isInitialized) {
      _isInitialized = true;
      emit(const GetNewPassengerState.loading());
    }

    final result = await _bookingRepository.getNewPassengers();

    result.fold(
      (failure) {
        emit(GetNewPassengerState.failure(failure));
      },
      (data) {
        // if (data.isEmpty) {
        //   emit(GetNewPassengerState.loaded(_previousData));
        //   return;
        // }
        // _previousData = data;

        emit(GetNewPassengerState.loaded(data));
      },
    );
  }

  Future<void> _onStopListening(
    _StopListening event,
    Emitter<GetNewPassengerState> emit,
  ) async {
    _isInitialized = false;
    _pollingService.stop(pollingId);
  }

  Future<void> _onResumeListening(
    _ResumeListening event,
    Emitter<GetNewPassengerState> emit,
  ) async {
    _isInitialized = true;
    _pollingService.resume(pollingId);
  }

  @override
  Future<void> close() {
    _pollingService.dispose(pollingId);
    return super.close();
  }
}
