// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'get_my_current_booking_bloc.dart';

@freezed
abstract class GetMyCurrentBookingEvent with _$GetMyCurrentBookingEvent {
  const factory GetMyCurrentBookingEvent.get(CurrentBookingGetEvent? event) =
      _Get;
  const factory GetMyCurrentBookingEvent.start() = _Start;
  const factory GetMyCurrentBookingEvent.resume() = _Resume;
  const factory GetMyCurrentBookingEvent.stop() = _Stop;
  const factory GetMyCurrentBookingEvent.reset() = _Reset;
}

final class CurrentBookingGetEvent {
  final int riderId;
  final int? bookingId;


  CurrentBookingGetEvent({
    required this.riderId,
    this.bookingId,
  });

  CurrentBookingGetEvent copyWith({
    int? riderId,
    int? bookingId,
    bool? isPreviousBooking,
  }) {
    return CurrentBookingGetEvent(
      riderId: riderId ?? this.riderId,
      bookingId: bookingId ?? this.bookingId,
    );
  }
}
