import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/services/polling/polling_service.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'get_my_current_booking_event.dart';
part 'get_my_current_booking_state.dart';
part 'get_my_current_booking_bloc.freezed.dart';

/// Yesle chaii CurrentOnly and Only one CurrentBooking support garxa
/// CurrentOnly Booking maa jati sukaii hoss bookings tar Euta matra
/// pick garxa rider ko id herera,
class GetMyCurrentBookingBloc
    extends Bloc<GetMyCurrentBookingEvent, GetMyCurrentBookingState> {
  final BookingRepository _bookingRepository;
  final PollingService _pollingService;
  final String _pollingId = "GetMyCurrentBookingBloc";

  BookingModel? _booking;
  bool _isInitialized = false;
  CurrentBookingGetEvent? _currentBookingGetEvent;

  GetMyCurrentBookingBloc({
    required BookingRepository repo,
    required PollingService polling,
  })  : _bookingRepository = repo,
        _pollingService = polling,
        super(GetMyCurrentBookingState.initial()) {
    if (!_pollingService.hasTask(_pollingId)) {
      _pollingService.register(
        id: _pollingId,
        immediate: false,
        onPoll: (date) {
          add(GetMyCurrentBookingEvent.get(_currentBookingGetEvent));
        },
        interval: const Duration(seconds: 20),
      );
    }

    on<_Get>(_onGetCurrentBooking);
    on<_Start>(_onStartPolling);
    on<_Resume>(_onResumePolling);
    on<_Stop>(_onStopPolling);
    on<_Reset>(_onReset);
  }

  Future<void> _onGetCurrentBooking(
    _Get get,
    Emitter<GetMyCurrentBookingState> emit,
  ) async {
    if (!_isInitialized) {
      _isInitialized = true;
      emit(GetMyCurrentBookingState.loading());
    }

    _currentBookingGetEvent = get.event;

    if (_currentBookingGetEvent?.bookingId == null ||
        (_currentBookingGetEvent == null && _booking == null)) {
      await _onGetAllCurrentBookings(emit);
      return;
    } else {
      await _onGetBookingFromBookingId(emit);
    }
  }

  Future<void> _onGetAllCurrentBookings(
    Emitter<GetMyCurrentBookingState> emit,
  ) async {
    final result = await _bookingRepository.getMyBookings(
      bookingStatus: "CurrentOnly",
    );

    result.fold(
      (failure) => emit(GetMyCurrentBookingState.failure(failure)),
      (bookings) {
        try {
          _booking = bookings.firstWhere(
            (e) => e.riderId == _currentBookingGetEvent?.riderId,
          );
          _currentBookingGetEvent = _currentBookingGetEvent?.copyWith(
            bookingId: _booking?.bookingId,
          );
          emit(GetMyCurrentBookingState.loaded(_booking!));
        } catch (e) {
          emit(GetMyCurrentBookingState.failure(UnexpectedFailure(message: e.toString())));
        }
      },
    );
  }

  Future<void> _onGetBookingFromBookingId(
    Emitter<GetMyCurrentBookingState> emit,
  ) async {
    final result = await _bookingRepository.getBookingDetail(
      id: _currentBookingGetEvent?.bookingId ?? _booking!.bookingId,
    );

    result.fold(
      (failure) => emit(GetMyCurrentBookingState.failure(failure)),
      (booking) => emit(GetMyCurrentBookingState.loaded(booking)),
    );
  }

  Future<void> _onStartPolling(
    _Start start,
    Emitter<GetMyCurrentBookingState> emit,
  ) async {
    await _pollingService.start(_pollingId);
  }

  Future<void> _onResumePolling(
    _Resume resume,
    Emitter<GetMyCurrentBookingState> emit,
  ) async {
    await _pollingService.resume(_pollingId);
  }

  Future<void> _onStopPolling(
    _Stop stop,
    Emitter<GetMyCurrentBookingState> emit,
  ) async {
    _pollingService.stop(_pollingId);
  }

  Future<void> _onReset(
    _Reset reset,
    Emitter<GetMyCurrentBookingState> emit,
  ) async {
    _booking = null;
    _isInitialized = false;
    emit(GetMyCurrentBookingState.initial());
  }
}
