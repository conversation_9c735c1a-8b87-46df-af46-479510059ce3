class GetMyAcceptingRidersModel {
  final int riderId;
  final String riderName;
  final String riderPhone;
  final String vehicleNo;
  final double fareAmount;
  final double currentLatitude;
  final double currentLongitude;
  final double rating;

  GetMyAcceptingRidersModel({
    required this.riderId,
    required this.riderName,
    required this.riderPhone,
    required this.vehicleNo,
    required this.fareAmount,
    required this.currentLatitude,
    required this.currentLongitude,
    required this.rating,
  });

  factory GetMyAcceptingRidersModel.fromMap(Map<String, dynamic> map) {
    return GetMyAcceptingRidersModel(
      riderId: map['RiderId'] ?? 0,
      riderName: map['RiderName'] ?? '',
      riderPhone: map['RiderPhone'] ?? '',
      vehicleNo: map['VehicleNo'] ?? '',
      fareAmount: (map['FareAmount'] ?? 0).toDouble(),
      currentLatitude: (map['CurrentLatitude'] ?? 0).toDouble(),
      currentLongitude: (map['CurrentLongitude'] ?? 0).toDouble(),
      rating: (map['RiderRating'] ?? 0).toDouble(),
    );
  }
}
