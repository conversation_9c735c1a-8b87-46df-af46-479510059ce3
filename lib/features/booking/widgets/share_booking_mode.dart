import 'package:flutter/material.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/widget/custom_switch_widget.dart';
import 'package:safari_yatri/features/booking/blocs/add_to_cart_booking/add_to_cart_booking_bloc.dart';

import '../../../core/utils/localization_utils.dart';
import '../../../core/utils/theme_utils.dart';

class ShareBookingModeWidget extends StatefulWidget {
  const ShareBookingModeWidget({super.key});

  @override
  State<ShareBookingModeWidget> createState() => _ShareBookingModeWidgetState();
}

class _ShareBookingModeWidgetState extends State<ShareBookingModeWidget> {
  bool _isToggle = false;
  @override
  Widget build(BuildContext context) {
    return _buildOptionRow(
      title: L.t.shareBookingModeTitle,

      value: _isToggle,
      onChanged: (value) {
        setState(() {
          _isToggle = value;
        });
        sl<AddToCartBookingBloc>().add(
          AddToCartBookingEvent.shareRidingMode(isShareRidingMode: _isToggle),
        );
      },
    );
  }

  Widget _buildOptionRow({
    required String title,
    required bool value,
    required Function(bool) onChanged,
  }) {
    final isDark = T.isDark(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: T
              .t(context)
              .titleMedium
              ?.copyWith(
                color:
                    isDark
                        ? T.c(context).onSurfaceVariant.withAlpha(179)
                        : T.c(context).onSurfaceVariant.withAlpha(153),
              ),
        ),
        CustomSwitchWidget(value: value, onChanged: onChanged),
      ],
    );
  }
}
