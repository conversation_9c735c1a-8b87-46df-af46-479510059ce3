import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/currency_formattor.dart';
import 'package:safari_yatri/core/utils/distance_calculator_utils.dart';
import 'package:safari_yatri/features/booking/blocs/accept_rider_request/accept_rider_request_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_accepting_riders/get_my_accepting_riders_bloc.dart';
import 'package:safari_yatri/features/booking/models/accepting_riders_model.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';

class DriverAcceptedCartOffer extends StatelessWidget {
  const DriverAcceptedCartOffer({
    super.key,
    required this.drivers,
    required this.acceptingRidersBloc,
    required this.currentLocationBloc,
  });

  final List<GetMyAcceptingRidersModel> drivers;
  final GetMyAcceptingRidersBloc acceptingRidersBloc;
  final CurrentLocationBloc currentLocationBloc;

  @override
  Widget build(BuildContext context) {
    return BlocListener<AcceptRiderRequestBloc, AcceptRiderRequestState>(
      listener: (context, state) {
        state.whenOrNull(
          loading: () => AppLoadingDialog.show(context),
          loaded: (riderId) {
            AppLoadingDialog.hide(context);
            sl<GetMyAcceptingRidersBloc>().add(
              GetMyAcceptingRidersEvent.reset(),
            );
            context.pushReplacementNamed(
              AppRoutesName.rideTrackingPageForPassenger,
              extra: {'riderId': riderId, 'bookingId': null},
            );
          },
          failure: (failure) {
            AppLoadingDialog.hide(context);
            CustomToast.showError(failure.message);
          },
        );
      },
      child: BlocBuilder<CurrentLocationBloc, CurrentLocationState>(
        bloc: currentLocationBloc,
        builder: (context, state) {
          LatLng latLng = state.maybeWhen(
            orElse: () => const LatLng(0, 0),
            loaded: (position) => LatLng(position.latitude, position.longitude),
          );
          return SizedBox(
            height: 500,
            child: ListView.separated(
              padding: const EdgeInsets.symmetric(
                horizontal: AppStyles.space12,
                vertical: AppStyles.space32,
              ),
              itemCount: drivers.length,
              separatorBuilder: (context, index) => const SizedBox(height: 10),
              itemBuilder: (context, index) {
                // if (index == 0) {
                //   return Material(
                //     child: SafeArea(
                //       child: Text(
                //         'Offers from Drivers',
                //         style: T.t(context).titleLarge,
                //       ),
                //     ),
                //   );
                // }

                final driver = drivers[index];
                return DriverCard(
                  currentLocation: latLng,
                  driver: driver,
                  onAccept: () {
                    sl<AcceptRiderRequestBloc>().add(
                      AcceptRiderRequestEvent.accept(driver.riderId),
                    );
                  },
                  onDecline: () {
                    acceptingRidersBloc.add(
                      GetMyAcceptingRidersEvent.removeRiderLocally(
                        driver.riderId,
                      ),
                    );
                  },
                  onExpired: () {
                    acceptingRidersBloc.add(
                      GetMyAcceptingRidersEvent.removeRiderLocally(
                        driver.riderId,
                      ),
                    );
                  },
                );
              },
            ),
          );
        },
      ),
    );
  }
}

class DriverCard extends StatefulWidget {
  const DriverCard({
    super.key,
    required this.currentLocation,
    required this.driver,
    required this.onAccept,
    required this.onDecline,
    required this.onExpired,
  });
  final LatLng currentLocation;
  final GetMyAcceptingRidersModel driver;
  final VoidCallback onAccept;
  final VoidCallback onDecline;
  final VoidCallback onExpired;

  @override
  State<DriverCard> createState() => _DriverCardState();
}

class _DriverCardState extends State<DriverCard> {
  static const int maxSeconds = 10;
  int timeLeft = maxSeconds;
  Timer? _timer;
  late String riderDistanceFromPassenger;

  @override
  void initState() {
    super.initState();
    riderDistanceFromPassenger = AppDistanceCalculator.distanceInMeterOrKM(
      widget.currentLocation,
      LatLng(widget.driver.currentLatitude, widget.driver.currentLongitude),
    );
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) return;
      setState(() => timeLeft--);
      if (timeLeft <= 0) {
        timer.cancel();
        widget.onExpired();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withAlpha(20),
              blurRadius: 24,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: colorScheme.outline.withAlpha(31),
            width: 0.5,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Main info row
                Row(
                  children: [
                    // Driver avatar
                    Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            colorScheme.primary,
                            colorScheme.primary.withAlpha(204),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(14),
                      ),
                      child: Icon(
                        Icons.person_rounded,
                        size: 24,
                        color: colorScheme.onPrimary,
                      ),
                    ),
                    const SizedBox(width: 12),

                    // Driver info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.driver.riderName,
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w700,
                              color: colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: colorScheme.surfaceContainerHighest
                                  .withAlpha(153),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              widget.driver.vehicleNo,
                              style: textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurfaceVariant,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Ride details - inline
                    Row(
                      children: [
                        _buildInlineDetail(
                          context,
                          Icons.wallet,
                          CurrencyFormatter.format(widget.driver.fareAmount),

                          colorScheme.primary,
                        ),
                        const SizedBox(width: 16),
                        _buildInlineDetail(
                          context,
                          Icons.location_on_rounded,
                          riderDistanceFromPassenger,
                          colorScheme.secondary,
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Action buttons
                Row(
                  children: [
                    // Decline button
                    Expanded(
                      flex: 2,
                      child: OutlinedButton(
                        onPressed: widget.onDecline,
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(14),
                          ),
                          side: BorderSide(
                            color: colorScheme.error.withAlpha(204),
                            width: 1.2,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.close_rounded,
                              size: 18,
                              color: colorScheme.error,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              'Decline',
                              style: textTheme.labelMedium?.copyWith(
                                color: colorScheme.error,
                                fontSize: 16,
                                fontWeight: FontWeight.w800,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),

                    // Accept button
                    Expanded(
                      flex: 3,
                      child: ElevatedButton(
                        onPressed: widget.onAccept,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colorScheme.primary,
                          foregroundColor: colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(14),
                          ),
                          elevation: 0,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: colorScheme.onPrimary.withAlpha(51),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '${timeLeft}s',
                                style: textTheme.labelSmall?.copyWith(
                                  color: colorScheme.onPrimary,
                                  fontWeight: FontWeight.w800,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              Icons.check_rounded,
                              size: 18,
                              color: colorScheme.onPrimary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Accept',
                              style: textTheme.labelMedium?.copyWith(
                                color: colorScheme.onPrimary,
                                fontSize: 16,
                                fontWeight: FontWeight.w800,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInlineDetail(
    BuildContext context,
    IconData icon,
    String value,
    Color iconColor,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Column(
      children: [
        Icon(icon, size: 20, color: iconColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurface,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
