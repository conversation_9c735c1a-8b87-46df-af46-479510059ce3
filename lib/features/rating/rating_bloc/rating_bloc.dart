import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/rating/models/rating_model.dart';
import 'package:safari_yatri/features/rating/repositories/rating_repository.dart';

part 'rating_event.dart';
part 'rating_state.dart';
part 'rating_bloc.freezed.dart';

class RatingBloc extends Bloc<RatingEvent, RatingState> {
  final RatingRepository _ratingRepository;
  RatingBloc({required RatingRepository repo})
    : _ratingRepository = repo,
      super(RatingState.initial()) {
    on<_Insert>(_onInsert);
  }

  Future<void> _onInsert(_Insert event, Emitter<RatingState> emit) async {
    emit(RatingState.loading());
    final result = await _ratingRepository.insertRating(event.ratingModel);
    result.fold(
      (failure) => emit(RatingState.failure(failure)),
      (data) => emit(RatingState.loaded("Rating Successful")),
    );
  }
}
