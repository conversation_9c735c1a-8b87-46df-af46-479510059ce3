// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rating_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RatingEvent {
  RatingModelForm get ratingModel;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RatingEvent &&
            (identical(other.ratingModel, ratingModel) ||
                other.ratingModel == ratingModel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, ratingModel);

  @override
  String toString() {
    return 'RatingEvent(ratingModel: $ratingModel)';
  }
}

/// Adds pattern-matching-related methods to [RatingEvent].
extension RatingEventPatterns on RatingEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Insert value)? insert,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Insert() when insert != null:
        return insert(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Insert value) insert,
  }) {
    final _that = this;
    switch (_that) {
      case _Insert():
        return insert(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Insert value)? insert,
  }) {
    final _that = this;
    switch (_that) {
      case _Insert() when insert != null:
        return insert(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RatingModelForm ratingModel)? insert,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Insert() when insert != null:
        return insert(_that.ratingModel);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RatingModelForm ratingModel) insert,
  }) {
    final _that = this;
    switch (_that) {
      case _Insert():
        return insert(_that.ratingModel);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RatingModelForm ratingModel)? insert,
  }) {
    final _that = this;
    switch (_that) {
      case _Insert() when insert != null:
        return insert(_that.ratingModel);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Insert implements RatingEvent {
  const _Insert(this.ratingModel);

  @override
  final RatingModelForm ratingModel;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Insert &&
            (identical(other.ratingModel, ratingModel) ||
                other.ratingModel == ratingModel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, ratingModel);

  @override
  String toString() {
    return 'RatingEvent.insert(ratingModel: $ratingModel)';
  }
}
