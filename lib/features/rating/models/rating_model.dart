class RatingModelForm {
  final int bookingId;
  final int revieweeId;
  final int ratingValue;
  final String reviewText;

  const RatingModelForm({
    required this.bookingId,
    required this.revieweeId,
    required this.ratingValue,
    required this.reviewText,
  });

  Map<String, dynamic> toMap() {
    return {
      "BookingId": bookingId,
      "RevieweeId": revieweeId,
      "RatingValue": ratingValue,
      "ReviewText": reviewText,
    };
  }
}
