import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/extensions/string_extension.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/rating/models/rating_model.dart';
import 'package:safari_yatri/features/rating/rating_bloc/rating_bloc.dart';
import '../../../core/utils/localization_utils.dart';
import '../../../core/widget/custom_appbar.dart';

class RatingPage extends StatefulWidget {
  const RatingPage({
    super.key,
    required this.bookingModel,
    required this.isPassenger,
  });
  final BookingModel bookingModel;
  final bool isPassenger;

  @override
  State<RatingPage> createState() => _RatingPageState();
}

class _RatingPageState extends State<RatingPage> {
  int _rating = 0;
  final TextEditingController _commentController = TextEditingController();
  final List<String> _selectedFeedback = [];

  void _submitRating() {
    if (_rating == 0) {
      CustomToast.showError(L.t.ratingPageSelectRatingError);
      return;
    }

    // Create rating form data
    final ratingForm = RatingModelForm(
      bookingId: widget.bookingModel.bookingId,
      revieweeId:
          widget.isPassenger
              ? widget.bookingModel.riderId
              : widget.bookingModel.passengerId,
      ratingValue: _rating,
      reviewText: _buildReviewText(),
    );

    sl<RatingBloc>().add(RatingEvent.insert(ratingForm));
  }

  String _buildReviewText() {
    String reviewText = _commentController.text.trim();
    if (_selectedFeedback.isNotEmpty) {
      reviewText +=
          '${reviewText.isNotEmpty ? '\n' : ''} Quick feedback: ${_selectedFeedback.join(', ')}';
    }
    return reviewText;
  }

  Widget _buildStar(int index) {
    return GestureDetector(
      onTap: () => setState(() => _rating = index),
      child: Icon(
        _rating >= index ? Icons.star : Icons.star_border,
        color: T.c(context).primary,
        size: 40,
      ),
    );
  }

  String _getPersonName() {
    return widget.isPassenger
        ? widget.bookingModel.riderName.riderNameOnly
        : widget.bookingModel.passengerName;
  }

  String _getInitials(String name) {
    List<String> words = name.trim().split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else if (words.isNotEmpty) {
      return words[0][0].toUpperCase();
    }
    return 'U';
  }

  @override
  Widget build(BuildContext context) {
    final personName = _getPersonName();
    final theme = T.t(context);
    final colorScheme = T.c(context);

    return BlocListener<RatingBloc, RatingState>(
      listener: _ratingBlocListener,
      child: Scaffold(
        backgroundColor: colorScheme.surface,
        appBar: CustomAppBar(
          title: Text(
            widget.isPassenger
                ? L.t.ratingPageTitleDriver
                : L.t.ratingPageTitlePassenger,
            style: theme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
          backgroundColor: colorScheme.surface,
          elevation: 0,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header text
              Text(
                L.t.ratingPageQuestion(personName),
                style: theme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // Person card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                color: colorScheme.surfaceContainerLow,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: colorScheme.primary.withOpacity(0.1),
                        child: Text(
                          _getInitials(personName),
                          style: theme.titleLarge?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              personName,
                              style: theme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              L.t.ratingPageCompletedTrip,
                              style: theme.bodyMedium?.copyWith(
                                color: colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Rating section
              Center(
                child: Column(
                  children: [
                    Text(
                      'Rate this ${widget.isPassenger ? 'driver' : 'passenger'}',
                      style: theme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        5,
                        (index) => Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: _buildStar(index + 1),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _rating > 0
                          ? _getRatingText(_rating)
                          : L.t.ratingPageTapToRate,
                      style: theme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Quick feedback section
              Text(
                L.t.ratingPageQuickFeedback,
                style: theme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    _getFeedbackOptions()
                        .map(
                          (option) => FeedbackChip(
                            label: option['label'],
                            icon: option['icon'],
                            isSelected: _selectedFeedback.contains(
                              option['label'],
                            ),
                            onTap: () {
                              setState(() {
                                if (_selectedFeedback.contains(
                                  option['label'],
                                )) {
                                  _selectedFeedback.remove(option['label']);
                                } else {
                                  _selectedFeedback.add(option['label']);
                                }
                              });
                            },
                          ),
                        )
                        .toList(),
              ),

              const SizedBox(height: 24),

              // Comment section
              Text(
                L.t.ratingPageAdditionalComment,
                style: theme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: _commentController,
                maxLines: 4,
                decoration: InputDecoration(
                  hintText: L.t.ratingPageCommentHint,
                  hintStyle: theme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                  filled: true,
                  fillColor: colorScheme.surfaceContainerLow,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: colorScheme.outline),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: colorScheme.outline),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: colorScheme.primary,
                      width: 2,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 32),

              Row(
                children: [
                  Expanded(
                    child: CustomTextButton(
                      title: L.t.ratingPageButtonSkip,
                      onPressed: _skipButton,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: CustomButtonPrimary(
                      title: L.t.ratingPageButtonSubmit,
                      onPressed: _submitRating,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _ratingBlocListener(BuildContext context, RatingState state) {
    state.whenOrNull(
      loading: () => AppLoadingDialog.show(context),
      loaded: (data) {
        CustomToast.showSuccess(data);
        AppLoadingDialog.hide(context);
        context.goNamed(
          widget.isPassenger
              ? AppRoutesName.passengerHome
              : AppRoutesName.driverHome,
        );
      },
      failure: (f) {
        CustomToast.showError(f.message);
        AppLoadingDialog.hide(context);
      },
    );
  }

  void _skipButton() {
    context.goNamed(
      widget.isPassenger
          ? AppRoutesName.passengerHome
          : AppRoutesName.driverHome,
    );
  }

  String _getRatingText(int rating) {
    switch (rating) {
      case 1:
        return L.t.ratingTextPoor;
      case 2:
        return L.t.ratingTextFair;
      case 3:
        return L.t.ratingTextGood;
      case 4:
        return L.t.ratingTextVeryGood;
      case 5:
        return L.t.ratingTextExcellent;
      default:
        return 'Tap to rate';
    }
  }

  List<Map<String, dynamic>> _getFeedbackOptions() {
    if (widget.isPassenger) {
      return [
        {
          'label': L.t.feedbackProfessional,
          'icon': Icons.business_center_outlined,
        },
        {'label': L.t.feedbackSafeDriving, 'icon': Icons.shield_outlined},
        {'label': L.t.feedbackOnTime, 'icon': Icons.access_time_outlined},
        {
          'label': L.t.feedbackFriendly,
          'icon': Icons.sentiment_satisfied_outlined,
        },
        {
          'label': L.t.feedbackCleanVehicle,
          'icon': Icons.local_car_wash_outlined,
        },
        {'label': L.t.feedbackIssue, 'icon': Icons.report_problem_outlined},
      ];
    } else {
      return [
        {
          'label': L.t.feedbackPolite,

          'icon': Icons.sentiment_satisfied_outlined,
        },
        {'label': L.t.feedbackOnTime, 'icon': Icons.access_time_outlined},
        {'label': L.t.feedbackEasyPickup, 'icon': Icons.location_on_outlined},
        {'label': L.t.feedbackRespectful, 'icon': Icons.thumb_up_outlined},
        {'label': L.t.feedbackIssue, 'icon': Icons.report_problem_outlined},
      ];
    }
  }
}

class FeedbackChip extends StatelessWidget {
  final String label;
  final IconData icon;
  final bool isSelected;
  final VoidCallback onTap;

  const FeedbackChip({
    super.key,
    required this.label,
    required this.icon,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = T.c(context);
    final theme = T.t(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? colorScheme.primary : colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? colorScheme.primary : colorScheme.outline,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? colorScheme.onPrimary : colorScheme.onSurface,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: theme.bodySmall?.copyWith(
                color:
                    isSelected ? colorScheme.onPrimary : colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
