import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/network/api_service.dart';
import 'package:safari_yatri/features/rating/models/rating_model.dart';

abstract interface class RatingRepository {
  FutureEither<void> insertRating(RatingModelForm form);
}

class RatingRepositoryI implements RatingRepository {
  final ApiService _apiService;
  RatingRepositoryI({required ApiService apiService})
    : _apiService = apiService;

  @override
  FutureEither<void> insertRating(RatingModelForm form) async {
    return await _apiService.post<void>('Rating/Insert', data: form.toMap());
  }
}
