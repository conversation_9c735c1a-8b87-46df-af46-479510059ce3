import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/utils/currency_formattor.dart';
import 'package:safari_yatri/core/utils/distance_calculator_utils.dart';
import 'package:safari_yatri/features/booking/models/new_passenger_view_model.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';

class RideRequestItem extends StatefulWidget {
  final NewPassengerModel newPassenger;
  final bool? showPhoneIcon;
  final VoidCallback? onTap;
  final VoidCallback? phoneIconOnTap;

  const RideRequestItem({
    super.key,
    required this.newPassenger,
    this.onTap,
    this.phoneIconOnTap,
    this.showPhoneIcon = false,
  });

  @override
  State<RideRequestItem> createState() => _RideRequestItemState();
}

class _RideRequestItemState extends State<RideRequestItem> {
  late final CurrentLocationBloc _currentLocationBloc;
  @override
  initState() {
    super.initState();
    _currentLocationBloc =
        sl<CurrentLocationBloc>()
          ..add(CurrentLocationEvent.getCurrentLocation());
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = ColorScheme.of(context);
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: widget.onTap,
        splashColor: colorScheme.primary.withAlpha(50),
        highlightColor: colorScheme.primary.withAlpha(25),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          decoration: BoxDecoration(
            border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
          ),
          child: Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: Colors.blue[100],
                child: Icon(Icons.person, color: colorScheme.primary),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.newPassenger.cartDetailViews.first.sourceAddress,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      widget
                          .newPassenger
                          .cartDetailViews
                          .last
                          .destinationAddress,
                      style: TextStyle(color: Colors.grey[700], fontSize: 13),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          widget.newPassenger.passengerName,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(width: 4),
                        widget.showPhoneIcon == false
                            ? const Icon(
                              Icons.star,
                              size: 14,
                              color: Colors.orange,
                            )
                            : SizedBox(),
                        // Text(
                        //   '${newPassenger} (${ride.rides})',
                        //   style: const TextStyle(fontSize: 12),
                        // ),
                        const Spacer(),
                        widget.showPhoneIcon == false
                            ? Text(
                              widget.newPassenger.bookingStartTime,

                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            )
                            : SizedBox(),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          CurrencyFormatter.format(
                            widget.newPassenger.passengerFareAmount,
                          ),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                        const SizedBox(width: 6),
                        widget.showPhoneIcon == false
                            ? BlocBuilder<
                              CurrentLocationBloc,
                              CurrentLocationState
                            >(
                              bloc: _currentLocationBloc,
                              builder: (context, state) {
                                LatLng? myCurrentLocation = state.whenOrNull(
                                  loaded: (data) {
                                    return LatLng(
                                      data.latitude,
                                      data.longitude,
                                    );
                                  },
                                );
                                return Text(
                                  AppDistanceCalculator.distanceInMeterOrKM(
                                    myCurrentLocation,
                                    LatLng(
                                      widget
                                          .newPassenger
                                          .cartDetailViews
                                          .first
                                          .sourceLatitude,
                                      widget
                                          .newPassenger
                                          .cartDetailViews
                                          .first
                                          .sourceLongitude,
                                    ),
                                  ),
                                  style: TextStyle(
                                    color: Colors.grey[700],
                                    fontSize: 13,
                                  ),
                                );
                              },
                            )
                            : SizedBox(),
                      ],
                    ),
                  ],
                ),
              ),

              widget.showPhoneIcon == true
                  ? IconButton(
                    icon: Icon(Icons.phone),
                    onPressed: widget.phoneIconOnTap,
                  )
                  : PopupMenuButton(
                    icon: const Icon(Icons.more_vert),
                    itemBuilder:
                        (context) => const [
                          PopupMenuItem(child: Text("View Details")),
                          PopupMenuItem(child: Text("Ignore")),
                        ],
                  ),
            ],
          ),
        ),
      ),
    );
  }
}
