import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/utils/ride_map_helper.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/convert_map_cart_to_markers.dart';
import 'package:safari_yatri/core/utils/currency_formattor.dart';
import 'package:safari_yatri/core/utils/format_distance.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/booking/blocs/accept_passenger_request/accept_passenger_request_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_new_passenger/get_new_passenger_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/polylines_points/polylines_points_bloc.dart';
import 'package:safari_yatri/features/booking/models/new_passenger_view_model.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';

import '../../../../core/widget/custom_platform_scaffold.dart';

class RideRequestDetailsPage extends StatefulWidget {
  final NewPassengerModel ride;

  const RideRequestDetailsPage({super.key, required this.ride});

  @override
  State<RideRequestDetailsPage> createState() => _RideRequestDetailsPageState();
}

class _RideRequestDetailsPageState extends State<RideRequestDetailsPage>
    with TickerProviderStateMixin {
  late NewPassengerModel newPassengerModel;
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  GoogleMapController? _mapController;

  Set<Marker> _markers = {};
  Set<Polyline> _pickupToDestinationPolyline = {};
  Set<Polyline> _currentToPIckupPolyline = {};
  late final CurrentLocationBloc _currentLocationBloc;
  LatLng? _currentLocation;

  @override
  void initState() {
    super.initState();
    newPassengerModel = widget.ride;

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    final uniqueValue =
        "${newPassengerModel.passengerName}${newPassengerModel.totalDistanceInMeter}";
    sl<PolylinesPointsBloc>().add(
      PolylinesPointsEvent.getPolylines(
        locations: newPassengerModel.cartDetailViews,
        uniqueValue: uniqueValue,
      ),
    );

    sl<GetNewPassengerBloc>().add(GetNewPassengerEvent.stop());
    _currentLocationBloc =
        sl<CurrentLocationBloc>()
          ..add(CurrentLocationEvent.getCurrentLocation());

    Future.delayed(const Duration(milliseconds: 100), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _mapController?.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      backgroundColor: T.c(context).surface,
      body: BlocBuilder<CurrentLocationBloc, CurrentLocationState>(
        bloc: _currentLocationBloc,
        builder: (context, currentLocationState) {
          currentLocationState.whenOrNull(
            loaded: (data) {
              _currentLocation = LatLng(data.latitude, data.longitude);
            },
          );
          return BlocConsumer<PolylinesPointsBloc, PolylinesPointsState>(
            listener: (context, state) {
              state.whenOrNull(
                loaded: (data) async {
                  _pickupToDestinationPolyline = AppMapHelper.createPolyline(
                    data.polylinePoints,
                    context: context,
                    width: 6,
                  );
                  _currentToPIckupPolyline = AppMapHelper.createPolyline(
                    data.polylinePointsFromCurrentToStart ?? [],
                    context: context,
                    color: const Color(0xFFFF6B6B),
                    polylineId: "currentToPickup",
                    width: 4,
                  );
                  final latlng = convertCartDetailsToLatLng(
                    newPassengerModel.cartDetailViews,
                  );
                  _markers = await AppMapHelper.generateRouteMarkers(latlng);
                },
              );
            },
            builder: (context, state) {
              state.whenOrNull(
                loaded: (data) {
                  if (_mapController != null) {
                    final latlng = convertCartDetailsToLatLng(
                      newPassengerModel.cartDetailViews,
                    );
                    _zoomToFitMarkers(
                      _currentLocation == null
                          ? []
                          : [_currentLocation!, ...latlng],
                    );
                  }
                },
              );

              return _buildUI(context);
            },
          );
        },
      ),
    );
  }

  Widget _buildUI(BuildContext context) {
    return Column(
      children: [
        // Map Section
        Expanded(
          flex: 6,
          child: Stack(
            children: [
              // Map
              Container(
                margin: const EdgeInsets.only(top: 30),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(
                    bottom: Radius.circular(25),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: T.c(context).shadow.withOpacity(0.1),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    bottom: Radius.circular(25),
                  ),
                  child: CustomGoogleMap(
                    polylines: {
                      ..._pickupToDestinationPolyline,
                      ..._currentToPIckupPolyline,
                    },
                    markers: _markers,
                    initialCameraPosition: CameraPosition(
                      target: LatLng(
                        newPassengerModel.cartDetailViews.first.sourceLatitude,
                        newPassengerModel.cartDetailViews.first.sourceLongitude,
                      ),
                      zoom: kMapInitialZoom,
                    ),
                    onMapCreated: (mapController) {
                      _mapController = mapController;
                    },
                    myLocationEnabled: true,
                  ),
                ),
              ),

              // Gradient overlay
              IgnorePointer(
                child: Container(
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        T.c(context).surface,
                        T.c(context).surface.withOpacity(0.1),
                      ],
                    ),
                  ),
                ),
              ),

              // Title
              Positioned(
                top: 4,
                left: 0,
                right: 0,
                child: IgnorePointer(
                  child: Center(
                    child: Text(
                      "Ride request",
                      style: T
                          .t(context)
                          .headlineLarge
                          ?.copyWith(
                            color: T.c(context).onSurface,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Bottom Sheet
        Expanded(
          flex: 7,
          child: SlideTransition(
            position: _slideAnimation,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: T.c(context).surface,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(25),
                ),
                boxShadow: [
                  BoxShadow(
                    color: T.c(context).shadow.withOpacity(0.1),
                    blurRadius: 15,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          // Passenger Info
                          _buildPassengerInfo(),
                          const SizedBox(height: 20),

                          // Trip Info
                          _buildTripInfo(),

                          const Spacer(),

                          // Accept Button
                          _buildAcceptButton(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPassengerInfo() {
    return Row(
      children: [
        // Avatar
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: T.c(context).primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: T.c(context).primary.withOpacity(0.3),
              width: 2,
            ),
          ),
          child: Center(
            child: Text(
              newPassengerModel.passengerName.isNotEmpty
                  ? newPassengerModel.passengerName[0].toUpperCase()
                  : 'P',
              style: T
                  .t(context)
                  .headlineSmall
                  ?.copyWith(
                    color: T.c(context).primary,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
        ),
        const SizedBox(width: 16),

        // Name and Details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                newPassengerModel.passengerName,
                style: T
                    .t(context)
                    .titleLarge
                    ?.copyWith(
                      color: T.c(context).onSurface,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.star, size: 16, color: T.c(context).tertiary),
                  const SizedBox(width: 4),
                  Text(
                    "4.8",
                    style: T
                        .t(context)
                        .bodyMedium
                        ?.copyWith(color: T.c(context).onSurface),
                  ),
                  const SizedBox(width: 16),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          newPassengerModel.isSharedBookingMode
                              ? T.c(context).tertiary.withOpacity(0.1)
                              : T.c(context).secondary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color:
                            newPassengerModel.isSharedBookingMode
                                ? T.c(context).tertiary.withOpacity(0.3)
                                : T.c(context).secondary.withOpacity(0.3),
                      ),
                    ),
                    child: Text(
                      newPassengerModel.isSharedBookingMode
                          ? "Shared"
                          : "Private",
                      style: T
                          .t(context)
                          .labelSmall
                          ?.copyWith(
                            fontWeight: FontWeight.w600,
                            color:
                                newPassengerModel.isSharedBookingMode
                                    ? T.c(context).tertiary
                                    : T.c(context).secondary,
                          ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTripInfo() {
    return Column(
      children: [
        // Route
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: T.c(context).tertiary,
                    shape: BoxShape.circle,
                  ),
                ),
                Container(
                  width: 2,
                  height: 30,
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  color: T.c(context).outline.withOpacity(0.3),
                ),
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: T.c(context).error,
                    shape: BoxShape.circle,
                  ),
                ),
              ],
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    newPassengerModel.cartDetailViews.first.sourceAddress,
                    style: T
                        .t(context)
                        .bodyMedium
                        ?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: T.c(context).onSurface,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 30),
                  Text(
                    newPassengerModel.cartDetailViews.last.destinationAddress,
                    style: T
                        .t(context)
                        .bodyMedium
                        ?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: T.c(context).onSurface,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Trip Details
        Row(
          children: [
            Expanded(
              child: _buildInfoCard(
                Icons.straighten,
                formatDistance(newPassengerModel.totalDistanceInMeter),
                "Distance",
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildInfoCard(
                Icons.people,
                "${newPassengerModel.passengerCount}",
                "Passengers",
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildInfoCard(
                Icons.access_time,
                newPassengerModel.bookingStartTime,
                "Time",
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoCard(IconData icon, String value, String label) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: T.c(context).surfaceContainerLowest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: T.c(context).outline.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, size: 20, color: T.c(context).onSurfaceVariant),
          const SizedBox(height: 6),
          Text(
            value,
            style: T
                .t(context)
                .bodyMedium
                ?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: T.c(context).onSurface,
                ),
          ),
          Text(
            label,
            style: T
                .t(context)
                .labelSmall
                ?.copyWith(color: T.c(context).onSurfaceVariant),
          ),
        ],
      ),
    );
  }

  Widget _buildAcceptButton() {
    return BlocListener<
      AcceptPassengerRequestBloc,
      AcceptPassengerRequestState
    >(
      listener: (context, state) {
        state.whenOrNull(
          loading: () {
            AppLoadingDialog.show(context);
          },
          failure: (failure) {
            AppLoadingDialog.hide(context);
            CustomToast.showError(failure.message);
          },
          loaded: (message) {
            AppLoadingDialog.hide(context);
            context.pop('showLoadingDilogRiderRequest');
          },
        );
      },
      child: CustomButtonPrimary(
        onPressed: () {
          sl<AcceptPassengerRequestBloc>().add(
            AcceptPassengerRequestEvent.accept(
              passengerId: newPassengerModel.passengerId,
              fareAmount: newPassengerModel.passengerFareAmount,
            ),
          );
        },
        title:
            "Accept for ${CurrencyFormatter.format(newPassengerModel.passengerFareAmount)}",
      ),
    );
  }

  Future<void> _zoomToFitMarkers(List<LatLng> locations) async {
    if (_mapController == null || locations.isEmpty || !mounted) {
      return;
    }

    await Future.delayed(const Duration(milliseconds: 200));
    if (!mounted) return;

    double minLat = locations.first.latitude;
    double maxLat = locations.first.latitude;
    double minLng = locations.first.longitude;
    double maxLng = locations.first.longitude;

    for (final location in locations) {
      minLat = min(minLat, location.latitude);
      maxLat = max(maxLat, location.latitude);
      minLng = min(minLng, location.longitude);
      maxLng = max(maxLng, location.longitude);
    }

    final bounds = LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );

    _mapController?.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, kMapPadding + 40),
    );
  }
}
