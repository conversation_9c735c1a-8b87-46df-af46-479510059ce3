import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/utils/localization_utils.dart';

class StatusToggleButton extends StatefulWidget {
  const StatusToggleButton({super.key});

  @override
  State<StatusToggleButton> createState() => _StatusToggleButtonState();
}

class _StatusToggleButtonState extends State<StatusToggleButton> {
  bool isOnline = true;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          isOnline = !isOnline;
        });
      },
      child: AnimatedContainer(
        duration: Duration(milliseconds: 300),
        width: 160,
        height: 40,
        padding: EdgeInsets.all(4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            colors:
                isOnline
                    ? [AppColors.brandGreen, Colors.green[600]!]
                    : [Colors.grey[400]!, Colors.grey[600]!],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 5,
              offset: Offset(0, 3),
            ),
          ],
        ),
        alignment: isOnline ? Alignment.centerRight : Alignment.centerLeft,
        child: Container(
          width: 90,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            isOnline
                ? L.t.statusToggleButtonOnline
                : L.t.statusToggleButtonOffline,
            style: TextStyle(
              color: isOnline ? Colors.green[800] : Colors.grey[800],
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
