import 'package:flutter/material.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/features/driver/ride/ride_start_completor_bloc/ride_start_completor_bloc.dart';

import '../../../core/utils/localization_utils.dart';

class ConfirmationCodeDialog extends StatefulWidget {
  const ConfirmationCodeDialog({
    super.key,
    required this.bookingId,
    required this.rideStartCompletorBloc,
  });
  final int bookingId;
  final RideStartCompletorBloc rideStartCompletorBloc;
  @override
  State<ConfirmationCodeDialog> createState() => _ConfirmationCodeDialogState();
}

class _ConfirmationCodeDialogState extends State<ConfirmationCodeDialog> {
  final TextEditingController confirmationCodeController =
      TextEditingController();
  @override
  void dispose() {
    super.dispose();
    confirmationCodeController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return Container(
      padding: EdgeInsets.only(bottom: bottomInset),
      decoration: BoxDecoration(
        color: T.c(context).surface,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: T.c(context).shadow.withOpacity(0.2),
            blurRadius: 24,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header section with gradient background
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  T.c(context).primary,
                  T.c(context).primary.withOpacity(0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
            ),
            child: Column(
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: T.c(context).onPrimary.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.lock_outline,
                    size: 32,
                    color: T.c(context).onPrimary,
                  ),
                ),
                const SizedBox(height: 16),
                // Title
                Text(
                  L.t.confirmationCodeDialogEnterConfirmationCode,
                  style: T
                      .t(context)
                      .headlineSmall
                      ?.copyWith(
                        color: T.c(context).onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                // Subtitle
                Text(
                  L.t.confirmationCodeDialogSubtitle,
                  style: T
                      .t(context)
                      .bodyMedium
                      ?.copyWith(
                        color: T.c(context).onPrimary.withOpacity(0.9),
                      ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Content section
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Input field with modern design
                Container(
                  decoration: BoxDecoration(
                    color: T
                        .c(context)
                        .surfaceContainerHighest
                        .withOpacity(0.5),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: T.c(context).outline.withOpacity(0.5),
                    ),
                  ),
                  child: TextField(
                    controller: confirmationCodeController,
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    style: T
                        .t(context)
                        .headlineMedium
                        ?.copyWith(
                          fontWeight: FontWeight.w600,
                          letterSpacing: 8,
                        ),
                    decoration: InputDecoration(
                      hintText: L.t.confirmationCodeDialogHint,
                      hintStyle: T
                          .t(context)
                          .headlineMedium
                          ?.copyWith(
                            color: T
                                .c(context)
                                .onSurfaceVariant
                                .withOpacity(0.5),
                            letterSpacing: 8,
                          ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 20,
                      ),
                    ),
                    maxLength: 6,
                    buildCounter: (
                      context, {
                      required currentLength,
                      required isFocused,
                      maxLength,
                    }) {
                      return Text(
                        '$currentLength/${maxLength ?? 6}',
                        style: T
                            .t(context)
                            .bodySmall
                            ?.copyWith(color: T.c(context).onSurfaceVariant),
                      );
                    },
                  ),
                ),

                const SizedBox(height: 24),

                // Action buttons
                Row(
                  children: [
                    // Cancel button
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          side: BorderSide(color: T.c(context).outline),
                        ),
                        child: Text(
                          L.t.confirmationCodeDialogCancel,
                          style: T
                              .t(context)
                              .labelLarge
                              ?.copyWith(color: T.c(context).onSurfaceVariant),
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Start ride button
                    Expanded(
                      flex: 2,
                      child: FilledButton.icon(
                        onPressed: () {
                          final String confirmationCode =
                              confirmationCodeController.text.trim();
                          if (confirmationCode.isNotEmpty) {
                            widget.rideStartCompletorBloc.add(
                              RideStartCompletorEvent.startRide(
                                bookingId: widget.bookingId,
                                confirmationCode: confirmationCode,
                              ),
                            );
                            Navigator.of(context).pop();
                          } else {
                            CustomToast.showError(
                              L.t.confirmationCodeDialogEmptyCodeError,
                            );
                          }
                        },
                        style: FilledButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          backgroundColor: T.c(context).primary,
                          foregroundColor: T.c(context).onPrimary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        icon: Icon(Icons.play_arrow, size: 20),
                        label: Text(
                          L.t.confirmationCodeDialogStartRide,
                          style: T
                              .t(context)
                              .labelLarge
                              ?.copyWith(
                                color: T.c(context).onPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
