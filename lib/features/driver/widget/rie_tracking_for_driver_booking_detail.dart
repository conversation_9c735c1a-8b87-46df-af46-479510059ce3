import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/currency_formattor.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';

class RideTrackingForDriverBookingDetailCard extends StatelessWidget {
  final BookingModel bookingModel;
  final VoidCallback? onTap;
  final VoidCallback? phoneIconOnTap;

  const RideTrackingForDriverBookingDetailCard({
    super.key,
    required this.bookingModel,
    this.onTap,
    this.phoneIconOnTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        splashColor: T.c(context).primary.withAlpha(50),
        highlightColor: T.c(context).primary.withAlpha(25),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: T.c(context).outline.withAlpha(80)),
            ),
          ),
          child: Row(
            children: [
              CircleAvatar(
                radius: 18,
                backgroundColor: T.c(context).primary.withAlpha(30),
                child: Icon(
                  Icons.person,
                  color: T.c(context).primary,
                  size: 18,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      bookingModel.bookingDetailViews.first.sourceAddress,
                      style: T
                          .t(context)
                          .bodyMedium
                          ?.copyWith(fontWeight: FontWeight.w600),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      bookingModel.bookingDetailViews.last.destinationAddress,
                      style: T
                          .t(context)
                          .bodySmall
                          ?.copyWith(
                            color: T.c(context).onSurface.withAlpha(160),
                          ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          bookingModel.passengerName,
                          style: T
                              .t(context)
                              .bodySmall
                              ?.copyWith(fontWeight: FontWeight.w500),
                        ),
                        const Spacer(),
                        Text(
                          CurrencyFormatter.format(
                            bookingModel.acceptedFareAmount,
                          ),
                          style: T
                              .t(context)
                              .bodySmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: T.c(context).error,
                              ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: phoneIconOnTap,
                icon: Icon(Icons.phone, color: T.c(context).primary),
                iconSize: 20,
                padding: const EdgeInsets.all(8),
                constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
