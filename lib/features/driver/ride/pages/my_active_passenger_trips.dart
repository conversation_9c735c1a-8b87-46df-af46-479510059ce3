import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/widget/build_all_booking.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_passengers_booking.dart/get_my_passengers_booking_bloc.dart';

class MyActivePassengerTrips extends StatefulWidget {
  const MyActivePassengerTrips({super.key});

  @override
  State<MyActivePassengerTrips> createState() => _MyActivePassengerTripsState();
}

class _MyActivePassengerTripsState extends State<MyActivePassengerTrips> {
  @override
  void initState() {
    super.initState();
    sl<GetMyPassengersBookingBloc>().add(
      GetMyPassengersBookingEvent.getActiveBookings(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GetMyPassengersBookingBloc, GetMyPassengersBookingState>(
      builder: (context, state) {
        return state.when(
          initial: () => SizedBox(),
          loading: () => Center(child: CircularProgressIndicator()),
          loaded: (bookings) {
            return BuildAllBookings(
              onRefresh: () {
                sl<GetMyPassengersBookingBloc>().add(
                  GetMyPassengersBookingEvent.getActiveBookings(refresh: true),
                );
              },
              bookings: bookings,
            );
          },
          failure:
              (failure) => ErrorWidgetWithRetry(
                failure: failure,
                onRetry:
                    () => sl<GetMyPassengersBookingBloc>().add(
                      GetMyPassengersBookingEvent.getActiveBookings(
                        refresh: true,
                      ),
                    ),
              ),
        );
      },
    );
  }
}
