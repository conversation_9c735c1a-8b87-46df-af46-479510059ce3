import 'package:flutter/material.dart';
import 'package:safari_yatri/core/widget/trip_tabbar.dart';
import 'package:safari_yatri/features/driver/ride/pages/my_active_passenger_trips.dart';
import 'package:safari_yatri/features/driver/ride/pages/my_current_passenger_trip.dart';

import '../../../../core/utils/localization_utils.dart';


class MyPassengerTripsPage extends StatelessWidget {
  const MyPassengerTripsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return TripTabBar(
      appBarTitle: L.t.tripBookingCartMyTrips,
      currentBooking: MyCurrentPassengerTrip(),
      activeBooking: MyActivePassengerTrips(),
    );
  }
}
