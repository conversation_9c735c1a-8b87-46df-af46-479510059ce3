// Full RideTrackingPageForDriver updated to use BookingModel instead of NewPassengerModel.

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/common/blocs/booking_detail/booking_detail_bloc.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/constant/string_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/models/direction_route_model.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/utils/bottom_sheet.dart';
import 'package:safari_yatri/core/utils/convert_map_cart_to_markers.dart';
import 'package:safari_yatri/core/utils/ride_map_helper.dart';
import 'package:safari_yatri/core/utils/url_launcher.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_pop_button.dart';
import 'package:safari_yatri/core/widget/custom_pop_scope.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/booking/blocs/get_new_passenger/get_new_passenger_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/polylines_points/polylines_points_bloc.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/driver/ride/ride_start_completor_bloc/ride_start_completor_bloc.dart';
import 'package:safari_yatri/features/driver/widget/confirmation_code_for_start_riding.dart';
import 'package:safari_yatri/features/driver/widget/rie_tracking_for_driver_booking_detail.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';

import '../../../../core/utils/localization_utils.dart';
import '../../../../core/widget/custom_platform_scaffold.dart';

class RideTrackingPageForDriver extends StatefulWidget {
  final BookingModel bookingModel;

  ///Kina vanda yadi rider le accidently applicaton close garyo re
  ///anii aba u ferii tehii page maa te pugnu paro tesko lagii chaii yo
  ///flag chaiyeko ho
  final bool isPreviousBooking;

  const RideTrackingPageForDriver({
    super.key,
    required this.bookingModel,
    required this.isPreviousBooking,
  });

  @override
  State<RideTrackingPageForDriver> createState() =>
      _RideTrackingPageForDriverState();
}

class _RideTrackingPageForDriverState extends State<RideTrackingPageForDriver> {
  late BookingModel bookingModel;
  GoogleMapController? _mapController;

  Set<Marker> _driverCurrentLocationToPassengerPickupMarkers = {};
  Set<Marker> _passengerPickupToDestinationMarkers = {};

  Set<Polyline> _pickupToDestinationPolyline = {};
  Set<Polyline> _currentToPickupPolyline = {};
  late final CurrentLocationBloc _currentLocationBloc;
  late final RideStartCompletorBloc _rideStartCompletorBloc;
  LatLng? _currentLocation;

  @override
  void initState() {
    super.initState();
    bookingModel = widget.bookingModel;

    // final uniqueValue =
    //     "${bookingModel.passengerName}${bookingModel.bookingStartTime}${bookingModel.bookingStartTime}";

    sl<PolylinesPointsBloc>().add(
      PolylinesPointsEvent.getPolylines(
        locations: bookingModel.bookingDetailViews,
        uniqueValue: "uniqueValue+${bookingModel.bookingId}",
      ),
    );

    sl<GetNewPassengerBloc>().add(GetNewPassengerEvent.stop());
    _currentLocationBloc =
        sl<CurrentLocationBloc>()
          ..add(CurrentLocationEvent.getCurrentLocation());
    _rideStartCompletorBloc = sl<RideStartCompletorBloc>();

    if (widget.isPreviousBooking) {
      _rideStartCompletorBloc.add(
        RideStartCompletorEvent.initializePreviousValue(
          bookingModel.serviceStatus,
        ),
      );
    }
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return CustomPopScope(
      canPop: true,
      onPopInvokedWithResult: _onPopInvoked,
      child: BlocListener<BookingDetailBloc, BookingDetailState>(
        listener: _bookingDetailListener,
        child: PlatformScaffold(
          body: SafeArea(
            child: BlocConsumer<CurrentLocationBloc, CurrentLocationState>(
              listener: (context, state) {
                state.whenOrNull(
                  loaded: (data) {
                    _currentLocation = LatLng(data.latitude, data.longitude);
                    _createMarkers();
                  },
                );
              },
              bloc: _currentLocationBloc,
              builder: (context, currentLocationState) {
                return currentLocationState.maybeWhen(
                  orElse: () {
                    return const Center(child: CircularProgressIndicator());
                  },
                  failure: (failure) {
                    return ErrorWidgetWithRetry(
                      failure: failure,
                      onRetry: () {
                        _currentLocationBloc.add(
                          CurrentLocationEvent.getCurrentLocation(),
                        );
                      },
                    );
                  },
                  loaded: (data) {
                    _currentLocation = LatLng(data.latitude, data.longitude);
                    return BlocConsumer<
                      PolylinesPointsBloc,
                      PolylinesPointsState
                    >(
                      listener: (context, state) => _polylineListener(state),
                      builder:
                          (context, state) => _buildLoadedData(
                            bottomInset,
                            context,
                            colorScheme,
                            textTheme,
                          ),
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void _onPopInvoked(_, __) async {
    sl<BookingDetailBloc>().add(
      BookingDetailEvent.get(
        bookingId: widget.bookingModel.bookingId,
        forceFetch: true,
      ),
    );
  }

  void _bookingDetailListener(BuildContext context, state) {
    state.whenOrNull(
      loaded: (data) {
        if (data.reasonForCancel.isNotEmpty) {
          appShowModalBottomSheet(
            context,
            child: Column(
              children: [
                Text(L.t.rideTrackingPageForDriverBookingCancelled),
                Text(L.t.rideTrackingPageForDriverReason),
                Text(data.reasonForCancel),
              ],
            ),
          );
          context.goNamed(AppRoutesName.driverHome);
          return;
        }
        if (data.serviceStatus == kServiceCompleted) {
          context.goNamed(
            AppRoutesName.ratingPage,
            extra: {'booking': data.booking, 'isPassenger': false},
          );
          return;
        }
      },
    );
  }

  void _polylineListener(PolylinesPointsState state) async {
    state.whenOrNull(
      loaded: (data) async {
        await _polylineInitializer(data);
      },
    );
  }

  Future<void> _polylineInitializer(DirectionRouteModel data) async {
    _setPolylines(data);
    if (mounted) setState(() {});
    if (_mapController != null) {
      _zoomToFitMarkers([
        if (_currentLocation != null) _currentLocation!,
        ...data.polylinePoints,
      ]);
    }
  }

  void _setPolylines(DirectionRouteModel data) {
    _pickupToDestinationPolyline = AppMapHelper.createPolyline(
      data.polylinePoints,
      width: 8,
      context: context,
    );
    _currentToPickupPolyline = AppMapHelper.createPolyline(
      data.polylinePointsFromCurrentToStart ?? [],
      color: Theme.of(context).colorScheme.secondary,
      polylineId: "currentToPickup",
      width: 6,
      context: context,
    );
    if (!context.mounted) return;
    setState(() {});
  }

  Future<void> _createMarkers() async {
    _driverCurrentLocationToPassengerPickupMarkers =
        await AppMapHelper.generateMarkerCurrentDriverToPassengerPickup([
          _currentLocation!,
          convertCartDetailsToLatLng(bookingModel.bookingDetailViews).first,
        ]);
    _passengerPickupToDestinationMarkers =
        await AppMapHelper.generateRouteMarkers(
          convertCartDetailsToLatLng(bookingModel.bookingDetailViews),
        );
  }

  Padding _buildLoadedData(
    double bottomInset,
    BuildContext context,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: bottomInset),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Flexible(
            flex: 6,
            child: Stack(
              children: [
                _buildGoogleMap(context),
                Positioned(top: 12, left: 12, child: CustomBackButton()),

                Positioned(
                  bottom: 4,
                  left: 12,
                  child: _buildNavigateButton(colorScheme, textTheme),
                ),
              ],
            ),
          ),
          Flexible(
            flex: 2,
            child: Container(
              height: 300,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(25),
                ),
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.shadow.withOpacity(0.2),
                    blurRadius: 8,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 8),

                  RideTrackingForDriverBookingDetailCard(
                    bookingModel: widget.bookingModel,

                    phoneIconOnTap: _onPhoneIconOnTap,
                  ),
                  const SizedBox(height: 8),
                  _buildButton(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  FilledButton _buildNavigateButton(
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return FilledButton.icon(
      icon: Icon(Icons.navigation, color: colorScheme.onPrimary),
      onPressed: _onPressNavigator,
      label: Text(
        L.t.rideTrackingPageForDriverNavigate,
        style: textTheme.labelLarge?.copyWith(color: colorScheme.onPrimary),
      ),
    );
  }

  SizedBox _buildGoogleMap(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.72,
      child: BlocConsumer<RideStartCompletorBloc, RideStartCompletorState>(
        bloc: _rideStartCompletorBloc,
        listener: _rideStartCompleteListener,
        builder: (context, state) {
          final isLoading = state.status == RideStartCompletorStatus.loading;
          final btnState = state.buttonState;
          return CustomGoogleMap(
            polylines:
                isLoading
                    ? {}
                    : btnState == RideStartCompletorButtonState.imHere
                    ? _currentToPickupPolyline
                    : _pickupToDestinationPolyline,
            markers:
                isLoading
                    ? {}
                    : btnState == RideStartCompletorButtonState.imHere
                    ? _driverCurrentLocationToPassengerPickupMarkers
                    : _passengerPickupToDestinationMarkers,
            initialCameraPosition: CameraPosition(
              target: LatLng(
                bookingModel.bookingDetailViews.first.sourceLatitude,
                bookingModel.bookingDetailViews.first.sourceLongitude,
              ),
              zoom: kMapInitialZoom,
            ),
            onMapCreated: (mapController) => _mapController = mapController,
            myLocationEnabled: true,
          );
        },
      ),
    );
  }

  SizedBox _buildButton() {
    return SizedBox(
      width: double.infinity,
      child: BlocBuilder<RideStartCompletorBloc, RideStartCompletorState>(
        bloc: _rideStartCompletorBloc,
        builder: (context, state) {
          final isLoading = state.status == RideStartCompletorStatus.loading;
          final buttonState = state.buttonState;
          return CustomButtonPrimary(
            isLoading: isLoading,
            onPressed:
                isLoading ||
                        buttonState == RideStartCompletorButtonState.completed
                    ? null
                    : () => _onPressedButton(
                      buttonState,
                      widget.bookingModel.bookingId,
                    ),
            title: getButtonTextFromEnum(buttonState),
          );
        },
      ),
    );
  }

  void _rideStartCompleteListener(BuildContext context, state) {
    if (state.status == RideStartCompletorStatus.failure) {
      final failure = state.failure;
      if (failure != null) {
        CustomToast.showError(failure.message);
        if (failure.message == kCancelBookingMessage) {
          sl<GetNewPassengerBloc>().add(GetNewPassengerEvent.resume());
          context.goNamed(AppRoutesName.driverHome);
        }
      }
    }
    if (state.buttonState == RideStartCompletorButtonState.completed) {
      context.goNamed(
        AppRoutesName.ratingPage,
        extra: {'booking': widget.bookingModel, 'isPassenger': false},
      );
    }
  }

  void _onPhoneIconOnTap() {
    if (bookingModel.passengerPhone.isEmpty) {
      CustomToast.showError(L.t.rideTrackingPageForDriverNoPhoneNumber);
      return;
    }
    urlLauncher(Uri(scheme: 'tel', path: bookingModel.passengerPhone));
  }

  void _onPressNavigator() {
    if (_currentLocation == null) return;
    final latlngs = convertCartDetailsToLatLng(
      widget.bookingModel.bookingDetailViews,
    );
    if (latlngs.isEmpty) return;
    final pickup = latlngs.first;
    final queryParams = <String, String>{
      'q': '${pickup.latitude},${pickup.longitude}',
      'mode': 'd',
    };
    final currentState = _rideStartCompletorBloc.state.buttonState;
    if (currentState != RideStartCompletorButtonState.imHere) {
      queryParams['destination'] =
          '${latlngs.last.latitude},${latlngs.last.longitude}';
    } else {
      queryParams['origin'] =
          '${_currentLocation!.latitude},${_currentLocation!.longitude}';
    }
    urlLauncher(Uri(scheme: 'google.navigation', queryParameters: queryParams));
  }

  Future<void> _zoomToFitMarkers(List<LatLng> locations) async {
    if (_mapController == null || locations.isEmpty || !mounted) return;
    await Future.delayed(const Duration(milliseconds: 200));
    if (!mounted) return;
    double minLat = locations.first.latitude;
    double maxLat = locations.first.latitude;
    double minLng = locations.first.longitude;
    double maxLng = locations.first.longitude;
    for (final loc in locations) {
      minLat = min(minLat, loc.latitude);
      maxLat = max(maxLat, loc.latitude);
      minLng = min(minLng, loc.longitude);
      maxLng = max(maxLng, loc.longitude);
    }
    final bounds = LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
    _mapController?.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, kMapPadding + 40),
    );

    // await AppMapHelper.zoomToFit(
    //   mapController: _mapController!,
    //   points: locations,
    // );
  }

  String getButtonTextFromEnum(RideStartCompletorButtonState buttonState) {
    switch (buttonState) {
      case RideStartCompletorButtonState.imHere:
        return L.t.rideTrackingPageForDriverImHere;
      case RideStartCompletorButtonState.start:
        return L.t.rideTrackingPageForDriverStartRide;
      case RideStartCompletorButtonState.complete:
        return L.t.rideTrackingPageForDriverCompleteRide;
      case RideStartCompletorButtonState.completed:
        return L.t.rideTrackingPageForDriverRideCompleted;
    }
  }

  void _onPressedButton(
    RideStartCompletorButtonState buttonState,
    int bookingId,
  ) {
    switch (buttonState) {
      case RideStartCompletorButtonState.imHere:
        _rideStartCompletorBloc.add(
          const RideStartCompletorEvent.originToPickup(),
        );
        break;
      case RideStartCompletorButtonState.start:
        appShowModalBottomSheet(
          context,
          child: ConfirmationCodeDialog(
            bookingId: bookingId,
            rideStartCompletorBloc: _rideStartCompletorBloc,
          ),
        );
        break;
      case RideStartCompletorButtonState.complete:
        _rideStartCompletorBloc.add(
          RideStartCompletorEvent.completeRide(bookingId),
        );
        break;
      case RideStartCompletorButtonState.completed:
        return;
    }
  }
}
