import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/widget/build_all_booking.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_current_passengers_booking/get_my_current_passengers_booking_bloc.dart';

class MyCurrentPassengerTrip extends StatefulWidget {
  const MyCurrentPassengerTrip({super.key});

  @override
  State<MyCurrentPassengerTrip> createState() => _MyCurrentPassengerTripState();
}

class _MyCurrentPassengerTripState extends State<MyCurrentPassengerTrip> {
  @override
  void initState() {
    super.initState();
    sl<GetMyCurrentPassengerBookingBloc>().add(
      GetMyCurrentPassengersBookingEvent.getCurrentBooking(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<
      GetMyCurrentPassengerBookingBloc,
      GetMyCurrentPassengersBookingState
    >(
      builder: (context, state) {
        return state.when(
          initial: () => SizedBox(),
          loading: () => Center(child: CircularProgressIndicator()),
          loaded: (bookings) {
            return BuildAllBookings(
              onRefresh: () {
                sl<GetMyCurrentPassengerBookingBloc>().add(
                  GetMyCurrentPassengersBookingEvent.getCurrentBooking(),
                );
              },

              bookings: bookings,
            );
          },
          failure:
              (failure) => ErrorWidgetWithRetry(
                failure: failure,
                onRetry:
                    () => sl<GetMyCurrentPassengerBookingBloc>().add(
                      GetMyCurrentPassengersBookingEvent.getCurrentBooking(),
                    ),
              ),
        );
      },
    );
  }
}
