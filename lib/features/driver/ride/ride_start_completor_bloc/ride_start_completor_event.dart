part of 'ride_start_completor_bloc.dart';

@freezed
class RideStartCompletorEvent with _$RideStartCompletorEvent {
  const factory RideStartCompletorEvent.originToPickup() = _OriginToPickup;
  const factory RideStartCompletorEvent.startRide({
    required int bookingId,
    required String confirmationCode,
  }) = _StartRide;
  const factory RideStartCompletorEvent.completeRide(int bookingId) =
      _CompleteRide;

  const factory RideStartCompletorEvent.initializePreviousValue(
    String bookingStatus,
  ) = _BookingStatus;
}
