import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/utils/input_validator_helper.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_form_field.dart';
import 'package:safari_yatri/features/driver/request_for_driver/blocs/request_for_driver/request_for_driver_bloc.dart';
import 'package:safari_yatri/features/driver/request_for_driver/models/request_for_driver_form.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/widget/custom_pop_button.dart';

import '../../../../core/widget/custom_platform_scaffold.dart';

class ContactInformationPage extends StatefulWidget {
  const ContactInformationPage({super.key});

  @override
  State<ContactInformationPage> createState() => _ContactInformationPageState();
}

class _ContactInformationPageState extends State<ContactInformationPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  String? _selectedGender;

  final List<String> _genderOptions = ['Male', 'Female', 'Other'];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final state = sl<RequestForDriverBloc>().state;
    state.whenOrNull(
      loaded: (data) {
        _emailController.text = data.data.emailAddress ?? "";
        _phoneController.text = data.data.phoneNo ?? "";
        _addressController.text = data.data.address ?? "";
        _selectedGender = data.data.gender;

        final storedGender = data.data.gender;
        if (storedGender != null) {
          switch (storedGender.toLowerCase()) {
            case 'male':
              _selectedGender = 'Male';
              break;
            case 'female':
              _selectedGender = 'Female';
              break;
            case 'other':
              _selectedGender = 'Other';
              break;
            default:
              _selectedGender = null;
          }
        }
      },
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: AppStyles.space12,
            vertical: AppStyles.space40,
          ),
          child: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomBackButton(),
                  SizedBox(height: AppStyles.space8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 30.0),
                    child: Column(
                      children: [
                        Text(
                          L.t.contactInfoTitle,
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.displayLarge,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: AppStyles.space12),
                  CustomFormFieldWidget(
                    prefixIcon: Icons.email,
                    enabled: false,
                    label: L.t.emailLabel,
                    controller: _emailController,
                    keys: TextInputType.emailAddress,
                    validator: InputValidator.validateEmail,
                  ),
                  SizedBox(height: AppStyles.space8),
                  CustomFormFieldWidget(
                    enabled: false,
                    prefixIcon: Icons.phone,
                    label: L.t.phoneNumberLabel,
                    controller: _phoneController,
                    keys: TextInputType.phone,
                    validator: InputValidator.validatePhone,
                  ),
                  SizedBox(height: AppStyles.space8),
                  CustomDropdownFormFieldWidget(
                    enabled: false,
                    label: L.t.genderLabel,
                    value: _selectedGender,
                    items: _genderOptions,
                    hintText: L.t.genderHint,
                    onChanged: (String? newValue) {
                      setState(() {
                        _selectedGender = newValue;
                      });
                    },
                    validator: InputValidator.validateGender,
                  ),
                  SizedBox(height: AppStyles.space8),
                  CustomFormFieldWidget(
                    enabled: false,
                    prefixIcon: Icons.location_on,
                    label: L.t.addressLabel,
                    controller: _addressController,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return L.t.addressValidation;
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: AppStyles.space64),
                  CustomButtonPrimary(
                    title: L.t.nextButton,
                    onPressed: _onPressed,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onPressed() {
    if (_formKey.currentState!.validate()) {
      if (_selectedGender == null) {
        CustomToast.showError(L.t.genderValidationToast);
        return;
      }
      sl<RequestForDriverBloc>().add(
        RequestForDriverEvent.requestForDriverProgressPage(
          pageName: AppRoutesName.contactInformationPage,
          driverRequestForm: DriverRegistrationRequest(
            emailAddress: _emailController.text,
            phoneNo: _phoneController.text,
            gender: _selectedGender,
            address: _addressController.text,
          ),
        ),
      );
      context.pushNamed(AppRoutesName.vehicleDetailsPage);
    }
  }
}
