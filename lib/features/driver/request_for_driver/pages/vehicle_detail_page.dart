import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:safari_yatri/common/blocs/get_vehicle_type/get_vehicle_type_bloc.dart';
import 'package:safari_yatri/common/blocs/image_picker/image_picker_bloc.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/models/get_vehile_type.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_form_field.dart';
import 'package:safari_yatri/core/widget/custom_pop_button.dart';
import 'package:safari_yatri/core/widget/vehicle_type_widget.dart';
import 'package:safari_yatri/features/driver/request_for_driver/blocs/request_for_driver/request_for_driver_bloc.dart';
import 'package:safari_yatri/features/driver/request_for_driver/models/request_for_driver_form.dart';

import '../../../../core/widget/custom_platform_scaffold.dart';

class VehicleDetailPage extends StatefulWidget {
  const VehicleDetailPage({super.key});

  @override
  State<VehicleDetailPage> createState() => _VehicleDetailPageState();
}

class _VehicleDetailPageState extends State<VehicleDetailPage> {
  final _formKey = GlobalKey<FormState>();
  late final ImagePickerBloc _vehiclePhotoBloc;
  late final ImagePickerBloc _blueBookPhotoBloc;

  final _vehicleNoController = TextEditingController();
  final _ownerNameController = TextEditingController();
  final _ownerPhoneController = TextEditingController();
  GetVehicleType? _selectedVehicleType;

  @override
  initState() {
    super.initState();
    _vehiclePhotoBloc = sl<ImagePickerBloc>();
    _blueBookPhotoBloc = ImagePickerBloc();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final state = sl<RequestForDriverBloc>().state;
    state.whenOrNull(
      loaded: (data) {
        final List<XFile> vehicleImageList = [];
        if (data.data.vehiclePhoto != null) {
          vehicleImageList.add(data.data.vehiclePhoto!);
        }
        _vehiclePhotoBloc.add(LoadInitialImagesEvent(vehicleImageList));

        final List<XFile> blueBookImageList = [];
        if (data.data.blueBookPhoto != null) {
          blueBookImageList.add(data.data.blueBookPhoto!);
        }
        _blueBookPhotoBloc.add(LoadInitialImagesEvent(blueBookImageList));

        _vehicleNoController.text = data.data.vehicleNo ?? "";
        _ownerNameController.text = data.data.ownerName ?? "";
        _ownerPhoneController.text = data.data.ownerPhone ?? "";

        if (data.data.vehicleTypeId != 0) {
          sl<GetVehicleTypeBloc>().state.maybeWhen(
            loaded: (vehicleTypes) {
              _selectedVehicleType = vehicleTypes.firstWhere(
                (type) => type.vehicleTypeId == data.data.vehicleTypeId,
                orElse: () => vehicleTypes.first,
              );
            },
            orElse: () {},
          );
        }
      },
    );
  }

  @override
  void dispose() {
    _vehiclePhotoBloc.close();
    _blueBookPhotoBloc.close();
    _vehicleNoController.dispose();
    _ownerNameController.dispose();
    _ownerPhoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: AppStyles.space12,
            vertical: AppStyles.space40,
          ),
          child: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomBackButton(),
                  SizedBox(height: AppStyles.space8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 30.0),
                    child: Column(
                      children: [
                        Text(
                          L.t.enterYourVehicleInformation,
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.displayLarge,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: AppStyles.space12),

                  VehicleTypeDropdown(
                    selectedVehicleType: _selectedVehicleType,
                    onChanged: (vehicle) {
                      setState(() {
                        _selectedVehicleType = vehicle;
                      });
                    },
                  ),
                  SizedBox(height: AppStyles.space8),
                  CustomFormFieldWidget(
                    label: L.t.vehicleNumber,
                    prefixIcon: Icons.directions_car,
                    controller: _vehicleNoController,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return L.t.pleaseEnterVehicleNumber;
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: AppStyles.space8),
                  CustomFormFieldWidget(
                    prefixIcon: Icons.person,
                    label: L.t.ownerName,
                    controller: _ownerNameController,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return L.t.pleaseEnterOwnerName;
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: AppStyles.space8),
                  CustomFormFieldWidget(
                    prefixIcon: Icons.phone,
                    label: L.t.ownerPhone,
                    controller: _ownerPhoneController,
                    keys: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return L.t.pleaseEnterOwnerPhoneNumber;
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: AppStyles.space12),
                  buildImagePicker(
                    context,
                    label: L.t.uploadVehiclePhoto,
                    bloc: _vehiclePhotoBloc,
                    maxImages: 1,
                  ),
                  SizedBox(height: AppStyles.space12),
                  buildImagePicker(
                    context,
                    label: L.t.uploadBillBookPhoto,
                    bloc: _blueBookPhotoBloc,
                    maxImages: 1,
                  ),
                  SizedBox(height: AppStyles.space64),
                  CustomButtonPrimary(title: L.t.next, onPressed: _onPressed),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildImagePicker(
    BuildContext context, {
    required String label,
    required ImagePickerBloc bloc,
    int maxImages = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: Theme.of(context).textTheme.titleMedium),
        SizedBox(height: AppStyles.space8),
        BlocBuilder<ImagePickerBloc, ImagePickerState>(
          bloc: bloc,
          builder: (context, state) {
            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount:
                  state.selectedImages.length < maxImages
                      ? state.selectedImages.length + 1
                      : state.selectedImages.length,
              itemBuilder: (context, index) {
                if (index < state.selectedImages.length) {
                  final XFile image = state.selectedImages[index];
                  return Stack(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                          image: DecorationImage(
                            image: FileImage(File(image.path)),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () => bloc.add(RemoveImageEvent(index)),
                          child: const CircleAvatar(
                            radius: 12,
                            backgroundColor: Colors.red,
                            child: Icon(
                              Icons.close,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                } else {
                  return GestureDetector(
                    onTap:
                        () => bloc.add(
                          PickImagesEvent(context, ImagePickMode.single),
                        ),
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.add_a_photo,
                          size: 40,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  );
                }
              },
            );
          },
        ),
        BlocListener<ImagePickerBloc, ImagePickerState>(
          bloc: bloc,
          listener: (context, state) {
            if (state.errorMessage != null) {
              CustomToast.showError(state.errorMessage!);
            }
          },
          child: const SizedBox.shrink(),
        ),
      ],
    );
  }

  void _onPressed() {
    if (_formKey.currentState!.validate()) {
      if (_selectedVehicleType == null) {
        CustomToast.showError(L.t.pleaseSelectVehicleType);
        return;
      }
      if (_vehiclePhotoBloc.state.selectedImages.isEmpty) {
        CustomToast.showError(L.t.pleaseUploadVehiclePhoto);
        return;
      }
      if (_blueBookPhotoBloc.state.selectedImages.isEmpty) {
        CustomToast.showError(L.t.pleaseUploadBlueBookPhoto);
        return;
      }

      sl<RequestForDriverBloc>().add(
        RequestForDriverEvent.requestForDriverProgressPage(
          pageName: AppRoutesName.vehicleDetailsPage,
          driverRequestForm: DriverRegistrationRequest(
            vehicleTypeId: _selectedVehicleType!.vehicleTypeId,
            vehicleNo: _vehicleNoController.text,
            ownerName: _ownerNameController.text,
            ownerPhone: _ownerPhoneController.text,
            vehiclePhoto:
                _vehiclePhotoBloc.state.selectedImages.isNotEmpty
                    ? _vehiclePhotoBloc.state.selectedImages[0]
                    : null,
            blueBookPhoto:
                _blueBookPhotoBloc.state.selectedImages.isNotEmpty
                    ? _blueBookPhotoBloc.state.selectedImages[0]
                    : null,
          ),
        ),
      );
      context.pushNamed(AppRoutesName.driverDocumentPage);
    }
  }
}
