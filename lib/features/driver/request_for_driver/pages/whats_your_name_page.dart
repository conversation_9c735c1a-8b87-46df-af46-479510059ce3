import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/get_vehicle_type/get_vehicle_type_bloc.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/common/blocs/local_user_mode/local_user_mode_cubit.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_form_field.dart';
import 'package:safari_yatri/features/driver/request_for_driver/blocs/request_for_driver/request_for_driver_bloc.dart';
import 'package:safari_yatri/features/driver/request_for_driver/models/request_for_driver_form.dart';

import '../../../../core/di/dependency_injection.dart';
import '../../../../core/widget/custom_platform_scaffold.dart';
import '../../../../core/widget/custom_pop_button.dart';

class WhatsYourNamePage extends StatefulWidget {
  const WhatsYourNamePage({super.key});

  @override
  State<WhatsYourNamePage> createState() => _WhatsYourNamePageState();
}

class _WhatsYourNamePageState extends State<WhatsYourNamePage> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    sl<RequestForDriverBloc>().add(
      const RequestForDriverEvent.initializeDataFromRemoteOnce(),
    );
    sl<GetVehicleTypeBloc>().add(const GetVehicleTypeEvent.get());
  }

  @override
  void dispose() {
    _usernameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      body: SafeArea(
        child: BlocListener<RequestForDriverBloc, RequestForDriverState>(
          listener: (context, state) {
            state.whenOrNull(
              loaded: (data) {
                _usernameController.text = data.data.userName ?? '';
              },
            );
          },
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: AppStyles.space12,
              vertical: AppStyles.space40,
            ),
            child: SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Align(
                      alignment: Alignment.centerLeft,
                      child:
                          BlocListener<LocalUserModeCubit, LocalUserModeState>(
                            listener: (context, state) {
                              state.mapOrNull(
                                failure: (value) {
                                  CustomToast.showError(value.failure.message);
                                },
                                loading: (_) {
                                  AppLoadingDialog.show(context);
                                },
                                loaded: (_) {
                                  AppLoadingDialog.hide(context);
                                  context.goNamed(AppRoutesName.passengerHome);
                                },
                              );
                            },
                            child: CustomBackButton(
                              onPressed:
                                  () =>
                                      sl<LocalUserModeCubit>()
                                          .switchPassengerMode(),
                            ),
                          ),
                    ),
                    SizedBox(height: AppStyles.space8),

                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          L.t.whatsYourNamePageTitle,
                          style: Theme.of(context).textTheme.displayLarge,
                          textAlign: TextAlign.center,
                        ),
                        Text(
                          L.t.whatsYourNamePageSubtitle,
                          style: Theme.of(context).textTheme.bodyLarge,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                    SizedBox(height: AppStyles.space12),

                    CustomFormFieldWidget(
                      enabled: false,
                      prefixIcon: Icons.person,
                      label: L.t.fullNameLabel,
                      controller: _usernameController,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return L.t.fullNameValidation;
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: AppStyles.space24),

                    CustomButtonPrimary(
                      title: L.t.nextButton,
                      onPressed: _onPressed,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onPressed() {
    if (_formKey.currentState!.validate()) {
      sl<RequestForDriverBloc>().add(
        RequestForDriverEvent.requestForDriverProgressPage(
          pageName: AppRoutesName.whatsYourNamePage,
          driverRequestForm: DriverRegistrationRequest(
            userName: _usernameController.text,
          ),
        ),
      );
      context.pushNamed(AppRoutesName.contactInformationPage);
    }
  }
}
