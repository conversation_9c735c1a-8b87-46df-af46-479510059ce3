import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/my_profile/bloc/get_my_profile/my_profile_bloc.dart';
import '../../../../core/router/app_route_names.dart';
import '../../../../core/theme/app_styles.dart';
import '../../../../core/utils/image_utils.dart';
import '../../../../core/utils/localization_utils.dart';
import '../../../../core/utils/theme_utils.dart';
import '../../../../core/widget/custom_platform_scaffold.dart';
import '../../../../core/widget/status_chip.dart';

class DocumentReviewPage extends StatefulWidget {
  const DocumentReviewPage({super.key});

  @override
  State<DocumentReviewPage> createState() => _DocumentReviewPageState();
}

class _DocumentReviewPageState extends State<DocumentReviewPage> {
  @override
  void initState() {
    super.initState();
    sl<MyProfileBloc>().add(const MyProfileEvent.get());
  }

  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      // backgroundColor: Colors.grey[200],
      appBar: CustomAppBar(title: Text(L.t.documentReviewAppBarTitle)),
      body: BlocBuilder<MyProfileBloc, MyProfileState>(
        builder: (context, state) {
          return state.when(
            initial: () => SizedBox(),
            loading: () => const Center(child: CircularProgressIndicator()),

            failure:
                (failure) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        L.t.documentReviewLoadFailureTitle,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        failure.message,
                        style: const TextStyle(color: Colors.grey),
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () {
                          sl<MyProfileBloc>().add(const MyProfileEvent.get());
                        },
                        child: Text(L.t.documentReviewRetryButton),
                      ),
                    ],
                  ),
                ),

            loaded:
                (profile) => SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppStyles.space12,
                    vertical: AppStyles.space12,
                  ),
                  child: Column(
                    spacing: AppStyles.space12,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: T.c(context).surfaceContainerHigh,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CircleAvatar(
                              radius: 28,
                              backgroundColor: Colors.grey.shade300,
                              child: ClipOval(
                                child: SizedBox(
                                  width: 64,
                                  height: 64,
                                  child: ImageUtility.displayImageFromBase64(
                                    profile.profilePicture,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  profile.userName,
                                  style: T.t(context).titleMedium,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  profile.phoneNo,
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(color: Colors.grey.shade600),
                                ),
                                const SizedBox(height: 8),
                                StatusChip(
                                  label: L.t.documentReviewStatusUnderReview,
                                  icon: Icons.error_outline,
                                  color: Color(0xFFD35400),
                                  backgroundColor: Color(0xFFFFF4E5),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const StatusCard(),
                      const ReviewDetailsCard(),
                      CustomButtonPrimary(
                        title: L.t.documentReviewBackToHome,
                        onPressed: () {
                          if (context.canPop()) {
                            context.pop();
                            return;
                          }
                          context.goNamed(AppRoutesName.passengerHome);
                        },
                      ),
                    ],
                  ),
                ),
          );
        },
      ),
    );
  }
}

class StatusCard extends StatelessWidget {
  const StatusCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: T.c(context).surfaceContainerHigh,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            L.t.documentReviewStatusCardTitle,
            style: T.t(context).headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildStatusTile(
            icon: Icons.check_circle,
            title: L.t.documentReviewStatusSubmittedTitle,
            subtitle: L.t.documentReviewStatusSubmittedSubtitle,
            color: const Color(0xFFEAF8EE),
            iconColor: Colors.green,
          ),
          const SizedBox(height: 12),
          _buildStatusTile(
            icon: Icons.access_time,
            title: L.t.documentReviewStatusCheckTitle,
            subtitle: L.t.documentReviewStatusCheckSubtitle,
            color: const Color(0xFFFFF4E5),
            iconColor: Color(0xFFD35400),
          ),
          const SizedBox(height: 12),
          _buildStatusTile(
            icon: Icons.mail_outline,
            title: L.t.documentReviewStatusFinalTitle,
            subtitle: L.t.documentReviewStatusFinalSubtitle,
            color: const Color(0xFFF2F2F2),
            iconColor: Colors.grey,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required Color iconColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, color: iconColor, size: 28),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(fontSize: 13, color: Colors.black87),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ReviewDetailsCard extends StatelessWidget {
  const ReviewDetailsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      constraints: const BoxConstraints(maxWidth: 400),
      decoration: BoxDecoration(
        color: T.c(context).surfaceContainerHigh,
        borderRadius: BorderRadius.circular(12),
      ),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            L.t.documentReviewReviewDetailsTitle,
            style: T.t(context).headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildDetailRow(
            L.t.documentReviewDetailEstimated,
            "48 hours",
            context,
          ),
          const SizedBox(height: 12),
          _buildDetailRow(
            L.t.documentReviewDetailNotification,
            "Email & SMS",
            context,
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: T.c(context).surface,
              borderRadius: BorderRadius.circular(8),
            ),
            child: RichText(
              text: TextSpan(
                style: T.t(context).bodySmall,
                children: [
                  TextSpan(
                    text: L.t.documentReviewDetailNoteTitle,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  TextSpan(text: L.t.documentReviewDetailNoteContent),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: T.t(context).titleMedium),
        Text(value, style: T.t(context).titleMedium),
      ],
    );
  }
}
