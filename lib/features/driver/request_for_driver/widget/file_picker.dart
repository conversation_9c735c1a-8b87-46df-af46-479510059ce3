import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

Widget buildDocumentPicker({
  required XFile? file,
  required VoidCallback onTap,
  String? label,
}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      height: 180,
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 8, offset: Offset(0, 4)),
        ],
      ),
      child:
          file != null
              ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.insert_drive_file, size: 40, color: Colors.blue),
                  SizedBox(height: 8),
                  Text(
                    file.name,
                    style: TextStyle(fontSize: 16),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              )
              : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.upload_file, size: 50, color: Colors.grey),

                  SizedBox(height: 12),
                  Text(
                    label ?? "Tap to upload document",
                    style: TextStyle(fontSize: 16, color: Colors.grey[700]),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
    ),
  );
}
