import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/app_cubit/app_cubit.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/common/blocs/once_cubit/once_cubit.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/l10n/app_localizations.dart';
import '../../core/utils/theme_utils.dart';

class OnBoardingPage extends StatefulWidget {
  const OnBoardingPage({super.key});

  @override
  State<OnBoardingPage> createState() => _OnBoardingPageState();
}

class _OnBoardingPageState extends State<OnBoardingPage>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  int _currentPage = 0;
  final int _totalPages = 3;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onIntroEnd() {
    persistenceSl<AppCubit>().setOnBoardingCompleted();
    sl<OnceCubit>().completedOnBoaring();
    context.goNamed(AppRoutesName.signIn);
  }

  void _onNext() {
    if (_currentPage < _totalPages - 1) {
      _animationController.reset();
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _onIntroEnd();
    }
  }

  void _onSkip() {
    _onIntroEnd();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
    _animationController.forward();
  }

  // Widget _buildImage(String assetName) {
  //   return Container(
  //     height: 280,
  //     width: 280,
  //     decoration: BoxDecoration(
  //       borderRadius: BorderRadius.circular(20),
  //       boxShadow: [
  //         BoxShadow(
  //           color: Colors.black.withOpacity(0.1),
  //           blurRadius: 20,
  //           offset: const Offset(0, 10),
  //         ),
  //       ],
  //     ),
  //     child: ClipRRect(
  //       borderRadius: BorderRadius.circular(20),
  //       child: SvgPicture.asset(assetName, fit: BoxFit.contain),
  //     ),
  //   );
  // }

  Widget _buildDots() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(_totalPages, (index) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          height: 8,
          width: _currentPage == index ? 24 : 8,
          decoration: BoxDecoration(
            color:
                _currentPage == index
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).primaryColor.withOpacity(0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        );
      }),
    );
  }

  List<OnboardingPageData> _getPages(AppLocalizations l10n) {
    return [
      OnboardingPageData(
        title: l10n.onBoardingfirstScreenTitle,
        description: l10n.onBoardingfirstScreenDesc,
        // imagePath: ImageConstant.firstOnBoard,
      ),
      OnboardingPageData(
        title: l10n.onBoardingSecondScreenTitle,
        description: l10n.onBoardingSecondScreenDesc,
        // imagePath: ImageConstant.secondOnBoard,
      ),
      OnboardingPageData(
        title: l10n.onBoardingThirdScreenTitle,
        description: l10n.onBoardingThirdScreenDesc,
        // imagePath: ImageConstant.thirdOnBoard,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final pages = _getPages(l10n);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: T.c(context).surface,
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _onSkip,
                    style: TextButton.styleFrom(
                      foregroundColor: theme.primaryColor,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                    child: Text(
                      l10n.skip,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: theme.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Main content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: _onPageChanged,
                itemCount: _totalPages,
                itemBuilder: (context, index) {
                  final page = pages[index];
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Image placeholder (uncomment when you have images)
                            // _buildImage(page.imagePath),
                            // const SizedBox(height: 48),

                            // Placeholder for image space
                            Container(
                              height: 280,
                              width: 280,
                              decoration: BoxDecoration(
                                color: theme.primaryColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: theme.primaryColor.withOpacity(0.2),
                                  width: 2,
                                ),
                              ),
                              child: Icon(
                                Icons.image_outlined,
                                size: 64,
                                color: theme.primaryColor.withOpacity(0.5),
                              ),
                            ),
                            const SizedBox(height: 48),

                            // Title
                            Text(
                              page.title,
                              textAlign: TextAlign.center,
                              style:
                                  T
                                      .t(context)
                                      .headlineMedium
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        height: 1.2,
                                      ) ??
                                  TextStyle(
                                    fontSize: 28,
                                    fontWeight: FontWeight.bold,
                                    height: 1.2,
                                    color: theme.colorScheme.onSurface,
                                  ),
                            ),
                            const SizedBox(height: 24),

                            // Description
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              child: Text(
                                page.description,
                                textAlign: TextAlign.center,
                                style:
                                    T
                                        .t(context)
                                        .bodyLarge
                                        ?.copyWith(
                                          height: 1.5,
                                          color: theme.colorScheme.onSurface
                                              .withOpacity(0.7),
                                        ) ??
                                    TextStyle(
                                      fontSize: 16,
                                      height: 1.5,
                                      color: theme.colorScheme.onSurface
                                          .withOpacity(0.7),
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            // Bottom section with dots and button
            Padding(
              padding: const EdgeInsets.all(32),
              child: Column(
                children: [
                  // Dots indicator
                  _buildDots(),
                  const SizedBox(height: 32),

                  // Next/Done button
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: _onNext,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.primaryColor,
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            _currentPage == _totalPages - 1
                                ? l10n.done
                                : l10n.next,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          if (_currentPage != _totalPages - 1) ...[
                            const SizedBox(width: 8),
                            const Icon(Icons.arrow_forward, size: 20),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class OnboardingPageData {
  final String title;
  final String description;
  final String? imagePath;

  const OnboardingPageData({
    required this.title,
    required this.description,
    this.imagePath,
  });
}
