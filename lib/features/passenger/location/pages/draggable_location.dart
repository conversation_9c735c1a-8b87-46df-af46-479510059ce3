import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/common/blocs/current_latlng_address/current_lat_lng_address_bloc.dart';
import 'package:safari_yatri/core/constant/image_constant.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/core/widget/app_map_pin.dart';
import 'package:safari_yatri/core/widget/current_location_navigator_button.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/location/blocs/current_location_navigator/current_location_navigator_cubit.dart';
import 'package:safari_yatri/features/passenger/core/blocs/is_coming_from_draggable/is_coming_from_draggable_cubit.dart';
import 'package:safari_yatri/features/passenger/core/blocs/map_movement/map_movement_cubit.dart';
import 'package:safari_yatri/core/widget/custom_pop_button.dart';
import 'package:safari_yatri/features/passenger/core/blocs/passenger_route/passenger_route_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/pick_location_picking_status/pickup_location_picking_status_cubit.dart';
import 'package:safari_yatri/features/passenger/location/models/location_picking_type.dart';
import 'package:safari_yatri/features/passenger/location/models/passenger_location.dart';

import '../../../../core/utils/localization_utils.dart';
import '../../../../core/widget/custom_platform_scaffold.dart';

class DraggableLocationPickerMap extends StatefulWidget {
  final LocationPickingType type;
  const DraggableLocationPickerMap({super.key, required this.type});

  @override
  State<DraggableLocationPickerMap> createState() =>
      _DraggableLocationPickerMapState();
}

class _DraggableLocationPickerMapState
    extends State<DraggableLocationPickerMap> {
  LatLng? _currentLocation;
  LatLngWithAddress? _pickupLocation;
  LatLngWithAddress? _destinationLocation;
  late final AppMapPickerController mapPickerController;
  final Completer<GoogleMapController> _mapController = Completer();
  CameraPosition? _cameraPosition;
  bool _isLoading = false;
  bool _shouldPopTwice = false;

  @override
  void initState() {
    super.initState();
    mapPickerController = AppMapPickerController();
  }

  @override
  void dispose() {
    // Properly dispose of the map controller if it's completed
    if (_mapController.isCompleted) {
      _mapController.future.then((controller) => controller.dispose());
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      body: BlocListener<
        CurrentLocationNavigatorCubit,
        CurrentLocationNavigatorState
      >(
        listener: (context, state) {
          state.whenOrNull(
            loaded: (data) {
              _currentLocation = data;
              _mapController.future.then((controller) async {
                try {
                  await controller.animateCamera(
                    CameraUpdate.newCameraPosition(
                      CameraPosition(target: data, zoom: kMapInitialZoom),
                    ),
                  );
                  // Add a small delay to ensure animation completes
                  await Future.delayed(const Duration(milliseconds: 300));
                } catch (e) {
                  // Handle potential errors during camera animation
                  debugPrint('Error animating camera: $e');
                }
              });
            },
          );
        },
        child: BlocConsumer<PassengerRouteBloc, PassengerRouteState>(
          listener: (context, state) {
            state.whenOrNull(
              loaded: (data) {
                if (_shouldPopTwice) {
                  _shouldPopTwice = false;
                  // Check if context is still mounted before navigation
                  if (mounted) {
                    sl<IsComingFromDraggableCubit>().isComingFromDraggable(
                      true,
                    );
                    context.goNamed(AppRoutesName.passengerHome);
                  }
                }
              },
            );
          },
          builder: (context, state) {
            state.whenOrNull(
              loading: () {
                _isLoading = true;
              },
              loaded: (data) {
                _isLoading = false;
                if (widget.type.isPickup) {
                  _pickupLocation = data.pickupLocation;
                } else {
                  if (widget.type.isDestination && widget.type.index != null) {
                    // Add bounds checking for the dropoff locations list
                    if (data.dropoffLocations.length > widget.type.index!) {
                      _destinationLocation =
                          data.dropoffLocations[widget.type.index!];
                    }
                  }

                  if (widget.type.isDestination && widget.type.index == null) {
                    _destinationLocation =
                        data.currentPassengerLocationWithAddress;
                  }
                }

                // Add null safety checks
                if (widget.type.isPickup && _pickupLocation != null) {
                  _currentLocation = LatLng(
                    _pickupLocation!.latitude,
                    _pickupLocation!.longitude,
                  );
                } else if (!widget.type.isPickup &&
                    _destinationLocation != null) {
                  _currentLocation = LatLng(
                    _destinationLocation!.latitude,
                    _destinationLocation!.longitude,
                  );
                }

                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted) {
                    setState(() {});
                  }
                });
              },
            );
            return _currentLocation != null
                ? Stack(
                  children: [
                    BlocBuilder<
                      CurrentLatLngAddressBloc,
                      CurrentLatLngAddressState
                    >(
                      builder: (context, state) {
                        return AppMapPicker(
                          iconWidget: SvgPicture.asset(
                            ImageConstant.mapMarker,
                            width: 50,
                            height: 50,
                          ),
                          mapPickerController: mapPickerController,
                          child: CustomGoogleMap(
                            myLocationEnabled: true,
                            zoomControlsEnabled: false,
                            mapToolbarEnabled: false,
                            myLocationButtonEnabled: false,
                            mapType: MapType.normal,
                            initialCameraPosition: CameraPosition(
                              target: _currentLocation!,
                              zoom: kMapInitialZoom,
                            ),
                            onMapCreated:
                                (controller) =>
                                    _mapController.complete(controller),
                            onCameraMoveStarted: _onCameraMoveStarted,
                            onCameraMove:
                                (position) => _cameraPosition = position,
                            onCameraIdle: _onCameraIdle,
                          ),
                        );
                      },
                    ),

                    Positioned(
                      top: MediaQuery.of(context).padding.top + 16,
                      left: 16,
                      child: CustomBackButton(),
                    ),
                    Positioned(
                      bottom: 16,
                      left: 16,
                      right: 16,
                      child: BlocBuilder<MapMovementCubit, MapMovementState>(
                        builder: (context, state) {
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Align(
                                alignment: Alignment.bottomRight,
                                child: Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: CurrentLocationNavigatorButton(),
                                ),
                              ),
                              const Gap(8),
                              SafeArea(
                                child: CustomButtonPrimary(
                                  isLoading: _isLoading,
                                  onPressed:
                                      state == MapMovementState.moving
                                          ? null
                                          : _onDone,
                                  title: L.t.done,
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ],
                )
                : const Center(
                  child: CircularProgressIndicator(),
                ); // Show loading indicator when location is null
          },
        ),
      ),
    );
  }

  void _onCameraMoveStarted() {
    sl<PickupLocationPickingStatusCubit>().startPicking();
    sl<MapMovementCubit>().startMoving();
    mapPickerController.mapMoving?.call();
  }

  Future<void> _onCameraIdle() async {
    sl<PickupLocationPickingStatusCubit>().stopPicking();
    if (_cameraPosition?.target != null) {
      sl<CurrentLatLngAddressBloc>().add(
        CurrentLatLngAddressEvent.get(position: _cameraPosition!.target),
      );
    }
    sl<MapMovementCubit>().stopMoving();
    mapPickerController.mapFinishedMoving?.call();
  }

  void _onDone() {
    if (_cameraPosition?.target == null) {
      debugPrint('Camera position is null, cannot proceed');
      return;
    }

    final LatLng newTarget = _cameraPosition!.target;

    bool isSameLocation(LatLng a, LatLng b) {
      // You can adjust the threshold as needed for precision
      const double threshold = 0.0001;
      return (a.latitude - b.latitude).abs() < threshold &&
          (a.longitude - b.longitude).abs() < threshold;
    }

    bool shouldSendUpdate = true;

    if (widget.type.isPickup && _pickupLocation != null) {
      final old = LatLng(_pickupLocation!.latitude, _pickupLocation!.longitude);
      shouldSendUpdate = !isSameLocation(newTarget, old);
    } else if (!widget.type.isPickup && _destinationLocation != null) {
      final old = LatLng(
        _destinationLocation!.latitude,
        _destinationLocation!.longitude,
      );
      shouldSendUpdate = !isSameLocation(newTarget, old);
    }

    if (!shouldSendUpdate) {
      debugPrint('Location is same. Just popping without updating.');
      context.goNamed(AppRoutesName.passengerHome);
      return;
    }

    _shouldPopTwice = true;

    if (widget.type.isPickup) {
      sl<PassengerRouteBloc>().add(
        PassengerRouteEvent.pickUpLocation(position: newTarget),
      );
    } else {
      sl<PickupLocationPickingStatusCubit>().disable();

      if (widget.type.isDestination && widget.type.index != null) {
        sl<PassengerRouteBloc>().add(
          PassengerRouteEvent.updateDestinationLocation(
            position: newTarget,
            index: widget.type.index!,
          ),
        );
      } else {
        sl<PassengerRouteBloc>().add(
          PassengerRouteEvent.destinationLocation(position: newTarget),
        );
      }
    }
  }
}
