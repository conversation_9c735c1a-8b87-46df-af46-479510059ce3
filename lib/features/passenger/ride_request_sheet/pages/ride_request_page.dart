// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/selected_vehicle_type/selected_vehicle_type_cubit.dart';

import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/widget/animated_text_loader.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_pop_scope.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/booking/blocs/clear_my_cart/clear_my_cart_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_accepting_riders/get_my_accepting_riders_bloc.dart';
import 'package:safari_yatri/features/booking/models/booking_add_to_cart.dart';
import 'package:safari_yatri/features/booking/widgets/driver_accepted_cart_offer.dart';
import 'package:safari_yatri/features/booking/widgets/fare_adjuster.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';
import 'package:safari_yatri/features/passenger/ride_request_sheet/pages/ride_request_page_map_widget.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_styles.dart';
import '../../../../core/utils/theme_utils.dart';

class RideRequestPage extends StatefulWidget {
  const RideRequestPage({super.key, required this.bookingAddToCartModel});
  final BookingAddToCartModel bookingAddToCartModel;
  @override
  State<RideRequestPage> createState() => _RideRequestPageState();
}

class _RideRequestPageState extends State<RideRequestPage> {
  late int _currentFare;
  late String pickUpSourceAddress;
  late String destinationAddress;
  late final GetMyAcceptingRidersBloc _getMyAcceptingRiderBloc;
  late CurrentLocationBloc _currentLocationBloc;
  late AnimatedTextLoaderController _animatedTextLoaderController;
  @override
  void initState() {
    super.initState();

    _animatedTextLoaderController = AnimatedTextLoaderController();

    _currentFare = widget.bookingAddToCartModel.passengerFareAmount.toInt();
    pickUpSourceAddress =
        widget.bookingAddToCartModel.cartDetailViews.first.sourceAddress;
    destinationAddress =
        widget.bookingAddToCartModel.cartDetailViews.first.destinationAddress;
    _getMyAcceptingRiderBloc = sl<GetMyAcceptingRidersBloc>();
    // _getMyAcceptingRiderBloc.add(GetMyAcceptingRidersEvent.get());
    _getMyAcceptingRiderBloc.add(GetMyAcceptingRidersEvent.resume());

    _currentLocationBloc = sl<CurrentLocationBloc>();
    _currentLocationBloc.add(const CurrentLocationEvent.getCurrentLocation());
  }

  @override
  void dispose() {
    super.dispose();

    _getMyAcceptingRiderBloc.add(GetMyAcceptingRidersEvent.stop());
    _getMyAcceptingRiderBloc.add(GetMyAcceptingRidersEvent.reset());
  }

  void _onFareChanged(int newFare) {
    setState(() {
      _currentFare = newFare;
    });
  }

  @override
  Widget build(BuildContext context) {
    return CustomPopScope(child: _buildStack());
  }

  Widget _buildStack() {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Material(
      color: Colors.transparent,
      shadowColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      child: Stack(
        children: [
          BlocBuilder<SelectedVehicleTypeCubit, SelectedVehicleTypeState>(
            builder: (context, state) {
              return RideRequestPageMapWidget(
                currentLocationBloc: _currentLocationBloc,
                maxRiderSearchDiameter:
                    state.vehicleType?.maxSearchDistanceInMeter ?? 3000,
              );
            },
          ),

          BlocBuilder<GetMyAcceptingRidersBloc, GetMyAcceptingRidersState>(
            bloc: _getMyAcceptingRiderBloc,
            builder: (context, state) {
              return state.maybeWhen(
                orElse: () => SizedBox(),
                // loading: () => const Center(child: CircularProgressIndicator()),
                failure:
                    (failure) => ErrorWidgetWithRetry(
                      failure: failure,
                      onRetry:
                          () => _getMyAcceptingRiderBloc.add(
                            GetMyAcceptingRidersEvent.resume(),
                          ),
                    ),
                loaded: (riders) {
                  if (riders.isEmpty) return SizedBox();
                  return DriverAcceptedCartOffer(
                    drivers: riders,
                    acceptingRidersBloc: _getMyAcceptingRiderBloc,
                    currentLocationBloc: _currentLocationBloc,
                  );
                },
              );
            },
          ),
          DraggableScrollableSheet(
            initialChildSize: 0.43,
            minChildSize: 0.43,
            maxChildSize: 0.65,
            builder:
                (context, scrollController) => Container(
                  padding: const EdgeInsets.all(AppStyles.space16),
                  decoration: BoxDecoration(
                    color: T.c(context).surface,
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(20),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 15,
                        spreadRadius: 3,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: SingleChildScrollView(
                    controller: scrollController,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: AnimatedTextLoader(
                            controller: _animatedTextLoaderController,
                            textStyle: textTheme.headlineSmall,
                            lastMessage: getLastMessage(),
                            messages: [
                              L.t.statusLookingForDrivers,
                              L.t.statusSendingOffer,
                              L.t.statusWaitingForResponse,
                              // L.t.rideRequest_searchingMessages,
                              // L.t.rideRequest_searchingMessages1,
                              // L.t.rideRequest_searchingMessages2,
                              // L.t.rideRequest_searchingMessages3,
                              // L.t.rideRequest_searchingMessages4,
                            ],
                            showLoadingDots: true,
                            switchDuration: Duration(seconds: 15),
                          ),
                        ),

                        // Divider(color: Colors.grey[300], height: 1),
                        const SizedBox(height: AppStyles.space24),
                        FareAdjuster(
                          onPressed: _onPressedFareAdustor,
                          initialFare: _currentFare,
                          onFareChanged: _onFareChanged,
                        ),

                        const SizedBox(height: AppStyles.space24),
                        _buildSectionHeader(
                          context,
                          L.t.rideRequest_payment,
                          textTheme,
                        ),
                        _buildPaymentDetail(context, textTheme, _currentFare),
                        const SizedBox(height: AppStyles.space16),
                        _buildSectionHeader(
                          context,
                          L.t.rideRequest_yourRide,
                          textTheme,
                        ),
                        _buildRideDetail(
                          context,
                          Icons.radio_button_checked,
                          AppColors.pickUpLocationColor,
                          pickUpSourceAddress,
                          textTheme,
                        ),
                        _buildRideDetail(
                          context,
                          Icons.location_on,
                          AppColors.destinationLocationColor,
                          destinationAddress,
                          textTheme,
                        ),
                        const SizedBox(height: AppStyles.space32),
                        BlocListener<ClearMyCartBloc, ClearMyCartState>(
                          child: CustomButtonPrimary(
                            title: L.t.rideRequest_cancelRequest,
                            textColor: T.c(context).errorContainer,
                            backgroundColor: T.c(context).error,
                            onPressed: () {
                              sl<ClearMyCartBloc>().add(
                                ClearMyCartEvent.clearMyCart(),
                              );
                            },
                          ),
                          listener: (context, state) {
                            state.whenOrNull(
                              failure: (failure) {
                                AppLoadingDialog.hide(context);
                                CustomToast.showError(failure.message);
                              },
                              loading: () {
                                AppLoadingDialog.show(context);
                              },
                              loaded: (data) {
                                context.pop();
                                _getMyAcceptingRiderBloc.add(
                                  GetMyAcceptingRidersEvent.reset(),
                                );

                                AppLoadingDialog.hide(context);
                                CustomToast.showSuccess(
                                  L.t.rideRequest_cancelSuccess,
                                );
                              },
                            );
                          },
                        ),
                        const SizedBox(height: AppStyles.space16),
                      ],
                    ),
                  ),
                ),
          ),
        ],
      ),
    );
  }

  /// Helper method to build section headers with consistent styling.
  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    TextTheme textTheme,
  ) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.only(bottom: AppStyles.space8),
        child: Text(
          title,
          style: textTheme.titleSmall?.copyWith(
            color: Colors.grey[700],
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// Helper method to build payment detail row.
  Widget _buildPaymentDetail(
    BuildContext context,
    TextTheme textTheme,
    int fare,
  ) {
    final isDark = T.isDark(context);

    return Align(
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          Text(
            "NPR $fare",
            style: T
                .t(context)
                .displaySmall
                ?.copyWith(
                  color:
                      isDark
                          ? T.c(context).onSurfaceVariant.withAlpha(179)
                          : T.c(context).onSurfaceVariant.withAlpha(153),
                ),
          ),
        ],
      ),
    );
  }

  /// Helper method to build individual ride detail rows (pickup/destination).
  Widget _buildRideDetail(
    BuildContext context,
    IconData icon,
    Color color,
    String text,
    TextTheme textTheme,
  ) {
    final isDark = T.isDark(context);
    return Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: AppStyles.space4),
        child: Row(
          children: [
            Icon(icon, size: 18, color: color),
            const SizedBox(width: AppStyles.space8),
            Text(
              text,
              style: T
                  .t(context)
                  .titleMedium
                  ?.copyWith(
                    color:
                        isDark
                            ? T.c(context).onSurfaceVariant.withAlpha(179)
                            : T.c(context).onSurfaceVariant.withAlpha(153),
                  ),
            ),
          ],
        ),
      ),
    );
  }

  String getLastMessage() {
    if (_currentFare < widget.bookingAddToCartModel.systemFareAmount.toInt()) {
      return L.t.fareTooLowMessage(_currentFare);
    }

    return L.t.noDriversFoundMessage;
  }

  void _onPressedFareAdustor() {
    _animatedTextLoaderController.reset();
  }
}
