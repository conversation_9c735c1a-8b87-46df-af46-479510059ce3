import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/constant/image_constant.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/core/widget/ripple_animation.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';

class RideRequestPageMapWidget extends StatefulWidget {
  const RideRequestPageMapWidget({
    super.key,
    required this.maxRiderSearchDiameter,
    required this.currentLocationBloc,
  });

  final int maxRiderSearchDiameter;
  final CurrentLocationBloc currentLocationBloc;

  @override
  State<RideRequestPageMapWidget> createState() =>
      _RideRequestPageMapWidgetState();
}

class _RideRequestPageMapWidgetState extends State<RideRequestPageMapWidget> {
  GoogleMapController? _mapController;
  LatLng _currentLatLng = const LatLng(27.7172, 85.3240);

  Timer? _zoomTimer;
  int _zoomTickCounter = 0;
  int currentSearchCount = 0;
  bool _isCurrentLocationLoaded = false;
  double _currentIconSize = 60.0;
  bool _isZooming = false; // Prevent multiple zoom animations

  static const double _minZoom = 15.8;
  static const double _maxZoom = 21.0;
  static const int _zoomTickThreshold = 180;
  static const double _initialIconSize = 60.0;
  static const double _maxIconSize = 80.0;

  CameraPosition _lastCameraPosition = CameraPosition(
    target: LatLng(27.7172, 85.3240),
    zoom: kMapInitialZoom,
  );

  @override
  void initState() {
    super.initState();
    _currentIconSize = _initialIconSize;
  }

  void _startSearchTimer() {
    if (_zoomTimer == null && _isCurrentLocationLoaded) {
      _zoomTimer = Timer.periodic(const Duration(milliseconds: 3000), (_) {
        zoomOutSlightly();
        _zoomTickCounter++;
        if (_zoomTickCounter >= _zoomTickThreshold) {
          _zoomTickCounter = 0;
        }
      });
    }
  }

  void zoomOutSlightly() async {
    if (_mapController == null || _isZooming) return;

    _isZooming = true;

    final currentZoom = _lastCameraPosition.zoom;
    final newZoom = (currentZoom - 0.025).clamp(_minZoom, _maxZoom);

    if (newZoom == currentZoom) {
      _isZooming = false;
      return;
    }

    final updatedPosition = CameraPosition(
      target: _lastCameraPosition.target,
      zoom: newZoom,
    );

    // Calculate new icon size based on zoom level
    final zoomProgress =
        (kMapInitialZoom - newZoom) / (kMapInitialZoom - _minZoom);
    final newIconSize =
        _initialIconSize + ((_maxIconSize - _initialIconSize) * zoomProgress);

    try {
      await _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(updatedPosition),
      );

      setState(() {
        _lastCameraPosition = updatedPosition;
        _currentIconSize = newIconSize.clamp(_initialIconSize, _maxIconSize);
      });
    } catch (e) {
      // Handle any animation errors gracefully
    } finally {
      _isZooming = false;
    }
  }

  @override
  void dispose() {
    _zoomTimer?.cancel();
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      child: BlocListener<CurrentLocationBloc, CurrentLocationState>(
        bloc: widget.currentLocationBloc,
        listener: (context, state) {
          state.maybeWhen(
            loaded: (data) {
              _currentLatLng = LatLng(data.latitude, data.longitude);
              _lastCameraPosition = CameraPosition(
                target: _currentLatLng,
                zoom: kMapInitialZoom,
              );
              _mapController?.moveCamera(
                CameraUpdate.newCameraPosition(_lastCameraPosition),
              );

              _isCurrentLocationLoaded = true;
              _startSearchTimer();
              setState(() {});
            },
            orElse: () {},
          );
        },
        child: SizedBox(
          height: MediaQuery.sizeOf(context).height * 0.6,
          child: LayoutBuilder(
            builder: (context, constraints) {
              return Stack(
                children: [
                  CustomGoogleMap(
                    onMapCreated: (controller) {
                      _mapController = controller;
                      if (_isCurrentLocationLoaded) _startSearchTimer();
                    },
                    initialCameraPosition: _lastCameraPosition,
                    onCameraMove: (position) {
                      _lastCameraPosition = position;
                    },
                  ),
                  Positioned(
                    bottom: constraints.maxHeight * 0.5 - 20,
                    left: 0,
                    right: 0,
                    child: LayeredRippleEffect(
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        height: _currentIconSize,
                        width: _currentIconSize,
                        child: SvgPicture.asset(ImageConstant.mapDestination1),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
