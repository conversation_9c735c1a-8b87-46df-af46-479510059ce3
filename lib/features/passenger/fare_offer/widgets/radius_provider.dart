import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/common/blocs/selected_vehicle_type/selected_vehicle_type_cubit.dart';
import 'package:safari_yatri/core/utils/format_distance.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';

class SearchRadiusMainSelector extends StatelessWidget {
  const SearchRadiusMainSelector({
    super.key,
    required this.onRadiusChanged,
    this.initialRadius = 2000,
  });

  final ValueChanged<int> onRadiusChanged;
  final int initialRadius;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SelectedVehicleTypeCubit, SelectedVehicleTypeState>(
      builder: (context, state) {
        return _RadiusSelectionWidget(
          radiusOptions: [
            1000,
            2000,
            state.vehicleType!.maxSearchDistanceInMeter,
          ],
          onRadiusChanged: onRadiusChanged,
          initialRadius: initialRadius,
        );
      },
    );
  }
}

class _RadiusSelectionWidget extends StatefulWidget {
  const _RadiusSelectionWidget({
    required this.radiusOptions,
    required this.onRadiusChanged,
    required this.initialRadius,
  });

  final List<int> radiusOptions;
  final ValueChanged<int> onRadiusChanged;
  final int initialRadius;

  @override
  State<_RadiusSelectionWidget> createState() => _RadiusSelectionWidgetState();
}

class _RadiusSelectionWidgetState extends State<_RadiusSelectionWidget> {
  late List<int> _radiusOptions;
  late int _selectedRadius;

  @override
  void initState() {
    super.initState();
    _selectedRadius = widget.initialRadius;
    _initializeRadiusOptions();
  }

  void _initializeRadiusOptions() {
    final uniqueRadii = widget.radiusOptions.toSet().toList()..sort();
    _radiusOptions = uniqueRadii;

    if (!_radiusOptions.contains(_selectedRadius)) {
      _selectedRadius = _radiusOptions.first;
    }
  }

  void _onSelectRadius(int radius) {
    if (radius == _selectedRadius) return;

    setState(() {
      _selectedRadius = radius;
    });

    HapticFeedback.lightImpact();
    widget.onRadiusChanged(radius);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      width: double.maxFinite,
      margin: const EdgeInsets.symmetric(horizontal: 16).copyWith(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(theme),
          const SizedBox(height: 16),
          _buildRadiusOptions(theme, colorScheme),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          L.t.searchRadiusTitle,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            letterSpacing: -0.5,

            color: theme.textTheme.titleLarge?.color?.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          L.t.searchRadiusSubtitle,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.textTheme.bodyMedium?.color?.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildRadiusOptions(ThemeData theme, ColorScheme colorScheme) {
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children:
          _radiusOptions.map((radius) {
            return _buildRadiusChip(radius, theme, colorScheme);
          }).toList(),
    );
  }

  Widget _buildRadiusChip(
    int radius,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final isSelected = radius == _selectedRadius;

    return GestureDetector(
      onTap: () => _onSelectRadius(radius),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          gradient:
              isSelected
                  ? LinearGradient(
                    colors: [
                      colorScheme.primary,
                      colorScheme.primary.withOpacity(0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
          color:
              isSelected
                  ? null
                  : theme.brightness == Brightness.dark
                  ? colorScheme.surfaceContainerHighest
                  : colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border:
              isSelected
                  ? null
                  : Border.all(
                    color: colorScheme.outline.withOpacity(0.3),
                    width: 1,
                  ),
          boxShadow: [
            BoxShadow(
              color:
                  isSelected
                      ? colorScheme.primary.withOpacity(0.25)
                      : Colors.black.withOpacity(0.05),
              blurRadius: isSelected ? 8 : 2,
              offset: Offset(0, isSelected ? 2 : 1),
            ),
          ],
        ),
        child: Text(
          formatDistance(radius),
          style: theme.textTheme.bodyMedium!.copyWith(
            color: isSelected ? colorScheme.onPrimary : colorScheme.onSurface,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
