import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/errors/location_failure.dart';
import 'package:safari_yatri/common/repositories/direction_repository.dart';
import 'package:safari_yatri/core/models/direction_route_model.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/core/utils/location_permission_status_failure_mapper.dart';
import 'package:safari_yatri/features/location/repositories/location_repository.dart';
import 'package:safari_yatri/features/location/repositories/places_repository.dart';
import 'package:safari_yatri/features/passenger/location/models/passenger_location.dart';

part 'passenger_route_event.dart';
part 'passenger_route_state.dart';
part 'passenger_route_bloc.freezed.dart';

///Passenger kaha bata kaha samma jane ho ta, vanera
///pickup, destinations, current locaiton haru hamile yaha bata lina sakinxa
class PassengerRouteBloc
    extends Bloc<PassengerRouteEvent, PassengerRouteState> {
  LatLngWithAddress? _pickUpLocation;
  final List<LatLngWithAddress> _dropoffLocations = [];
  LatLng? _currentLocation;
  LatLngWithAddress? _currentPassengerLocationWithAddress;

  final LocationRepository _locationRepository;
  final DirectionRepository _directionRepository;
  final PlacesRepository _placesRepository;

  int totalFailedCountForAPI = 0;

  String? _previousDestinationAddress;
  String? _previousPickupAddress;
  List<LatLng> _polylinePoints = [];
  bool isFailure = false;
  Failure? lFailure;
  DirectionRouteModel? _directionRouteModel;

  PassengerRouteBloc({
    required PlacesRepository placesRepository,
    required LocationRepository repo,
    required DirectionRepository directionRepository,
  }) : _locationRepository = repo,
       _directionRepository = directionRepository,
       _placesRepository = placesRepository,
       super(const PassengerRouteState.initial()) {
    on<_PickUpLocation>(_onPickUpLocation);
    on<_DestinationLocation>(_onDestinationLocation);
    on<_UpdateDestinationLocation>(_onUpdateDestinationLocation);
    on<_GetRoutes>(_onGetRoutes);
    on<_InitRoutes>(_onInitRoutes);
    on<_ResetPassengerRoute>(_onResetPassengerRoute);
    on<_RemoveDestionationLocation>(_onRemoveDestinationLocation);
    on<_UpdatePickupLocationWithPlaceId>(_onUpdatePickupLocationWithPlaceId);
    on<_AddOrUpdateDropoffLocationWithPlaceId>(
      _onAddOrUpdateDropoffLocationWithPlaceId,
    );
  }

  Future<void> _onRemoveDestinationLocation(
    _RemoveDestionationLocation event,
    Emitter<PassengerRouteState> emit,
  ) async {
    emit(const PassengerRouteState.loading());

    if (event.index >= 0 && event.index < _dropoffLocations.length) {
      _dropoffLocations.removeAt(event.index);
    }

    await _loadedEmitter(emit);
  }

  Future<void> _onPickUpLocation(
    _PickUpLocation event,
    Emitter<PassengerRouteState> emit,
  ) async {
    emit(const PassengerRouteState.loading());

    final failureOrAddress = await _locationRepository
        .getAddressFromCoordinates(
          event.position.latitude,
          event.position.longitude,
        );

    await failureOrAddress.fold(
      (failure) async => emit(PassengerRouteState.failure(failure)),
      (address) async {
        _pickUpLocation = LatLngWithAddress(
          longitude: event.position.longitude,
          latitude: event.position.latitude,
          address: address,
        );
        await _loadedEmitter(emit);
      },
    );
  }

  Future<void> _onDestinationLocation(
    _DestinationLocation event,
    Emitter<PassengerRouteState> emit,
  ) async {
    emit(const PassengerRouteState.loading());

    if (_pickUpLocation == null) {
      emit(
        PassengerRouteState.failure(
          LocationFailureWithMessage(message: "Pickup location is not set."),
        ),
      );
      return;
    }

    final failureOrAddress = await _locationRepository
        .getAddressFromCoordinates(
          event.position.latitude,
          event.position.longitude,
        );

    await failureOrAddress.fold(
      (failure) async => emit(PassengerRouteState.failure(failure)),
      (address) async {
        final dropoffLocation = LatLngWithAddress(
          longitude: event.position.longitude,
          latitude: event.position.latitude,
          address: address,
        );
        _dropoffLocations.add(dropoffLocation);
        await _loadedEmitter(emit);
      },
    );
  }

  Future<void> _onUpdateDestinationLocation(
    _UpdateDestinationLocation event,
    Emitter<PassengerRouteState> emit,
  ) async {
    emit(const PassengerRouteState.loading());

    final failureOrAddress = await _locationRepository
        .getAddressFromCoordinates(
          event.position.latitude,
          event.position.longitude,
        );

    await failureOrAddress.fold(
      (failure) async => emit(PassengerRouteState.failure(failure)),
      (address) async {
        final updatedLocation = LatLngWithAddress(
          longitude: event.position.longitude,
          latitude: event.position.latitude,
          address: address,
        );

        if (event.index < _dropoffLocations.length) {
          _dropoffLocations[event.index] = updatedLocation;
        }

        await _loadedEmitter(emit);
      },
    );
  }

  Future<void> _onGetRoutes(
    _GetRoutes event,
    Emitter<PassengerRouteState> emit,
  ) async {
    emit(const PassengerRouteState.loading());

    final currentPosition = await _locationRepository.getCurrentPosition();

    currentPosition.fold(
      (failure) => emit(PassengerRouteState.failure(failure)),
      (position) {
        _currentLocation = LatLng(position.latitude, position.longitude);
      },
    );

    if (_pickUpLocation == null) {
      emit(
        PassengerRouteState.failure(
          LocationFailureWithMessage(message: "Pickup location is not set."),
        ),
      );
      return;
    }

    await _loadedEmitter(emit);
  }

  Future<void> _onInitRoutes(
    _InitRoutes event,
    Emitter<PassengerRouteState> emit,
  ) async {
    emit(const PassengerRouteState.loading());

    final failureOrCurrentPermission =
        await _locationRepository.checkPermission();

    failureOrCurrentPermission.fold(
      (failure) {
        emit(PassengerRouteState.failure(failure));
        return;
      },
      (status) {
        if (status != PermissionStatus.granted) {
          LocationFailure locationFailure = mapLocationFailure(status);
          emit(PassengerRouteState.failure(locationFailure));
          return;
        }
      },
    );

    final currentPosition = await _locationRepository.getCurrentPosition();

    currentPosition.fold(
      (failure) => emit(PassengerRouteState.failure(failure)),
      (position) {
        _currentLocation = LatLng(position.latitude, position.longitude);
      },
    );

    if (_pickUpLocation == null && _currentLocation != null) {
      _pickUpLocation = await _getAddressWithCoordinates(_currentLocation!);
    }

    if (_pickUpLocation == null) {
      emit(
        PassengerRouteState.failure(
          LocationFailureWithMessage(message: "Pickup location is not set."),
        ),
      );
      return;
    }

    _currentPassengerLocationWithAddress = _pickUpLocation;
    await _loadedEmitter(emit);
  }

  Future<void> _loadedEmitter(Emitter<PassengerRouteState> emit) async {
    bool isCurrentAddressSameAsPrevious =
        _previousDestinationAddress == _dropoffLocations.lastOrNull?.address &&
        _previousPickupAddress == _pickUpLocation?.address;

    if (_dropoffLocations.isNotEmpty && !isCurrentAddressSameAsPrevious) {
      final failureOrRoute = await _directionRepository.getDirectionRoute(
        start: LatLng(_pickUpLocation!.latitude, _pickUpLocation!.longitude),
        destinations:
            _dropoffLocations
                .map((e) => LatLng(e.latitude, e.longitude))
                .toList(),
      );

      failureOrRoute.fold(
        (failure) {
          isFailure = true;
          totalFailedCountForAPI++;
          lFailure = failure;
          _polylinePoints = [
            LatLng(_pickUpLocation!.latitude, _pickUpLocation!.longitude),
            ..._dropoffLocations.map((e) => LatLng(e.latitude, e.longitude)),
          ];
        },
        (routes) {
          totalFailedCountForAPI = 0;
          isFailure = false;
          lFailure = null;
          //
          _polylinePoints = routes.polylinePoints;
          _directionRouteModel = routes;
          ////helps to track previous location and weather to fetch again or not
          _previousDestinationAddress = _dropoffLocations.last.address;
          _previousPickupAddress = _pickUpLocation!.address;
        },
      );
    }

    if (totalFailedCountForAPI == 4 && isFailure) {
      isFailure = false;
      totalFailedCountForAPI = 0;
      emit(PassengerRouteState.failure(lFailure!));

      return;
    }

    if (isFailure &&
        _directionRouteModel == null &&
        totalFailedCountForAPI <= 3) {
      _loadedEmitter(emit);
      return;
    }

    emit(
      PassengerRouteState.loaded(
        PassengerRouteLoaded(
          directionRoute: _directionRouteModel,
          currentPassengerLocationWithAddress:
              _currentPassengerLocationWithAddress,
          currentLocation: _currentLocation,
          pickupLocation: _pickUpLocation!,
          dropoffLocations: [..._dropoffLocations],
          polylinePoints: _polylinePoints,
        ),
      ),
    );
  }

  Future<void> _onResetPassengerRoute(
    _ResetPassengerRoute event,
    Emitter<PassengerRouteState> emit,
  ) async {
    emit(const PassengerRouteState.loading());
    _dropoffLocations.clear();
    _polylinePoints.clear();
    _directionRouteModel = null;
    await _loadedEmitter(emit);
  }

  Future<LatLngWithAddress?> _getAddressWithCoordinates(
    LatLng currentLocation,
  ) async {
    final failureOrAddress = await _locationRepository
        .getAddressFromCoordinates(
          currentLocation.latitude,
          currentLocation.longitude,
        );

    return failureOrAddress.fold(
      (failure) => null,
      (address) => LatLngWithAddress(
        longitude: currentLocation.longitude,
        latitude: currentLocation.latitude,
        address: address,
      ),
    );
  }

  Future<void> _onUpdatePickupLocationWithPlaceId(
    _UpdatePickupLocationWithPlaceId event,
    Emitter<PassengerRouteState> emit,
  ) async {
    emit(const PassengerRouteState.loading());

    final failureOrAddress = await _placesRepository.getPlaceDetails(
      event.placeId,
    );

    failureOrAddress.fold((failure) => null, (address) {
      _pickUpLocation = LatLngWithAddress(
        longitude: address.longitude!,
        latitude: address.latitude!,
        address: address.address,
      );
    });

    await _loadedEmitter(emit);
  }

  Future<void> _onAddOrUpdateDropoffLocationWithPlaceId(
    _AddOrUpdateDropoffLocationWithPlaceId event,
    Emitter<PassengerRouteState> emit,
  ) async {
    emit(const PassengerRouteState.loading());

    final failureOrAddress = await _placesRepository.getPlaceDetails(
      event.placeId,
    );

    failureOrAddress.fold((failure) => null, (address) {
      final dropoffLocation = LatLngWithAddress(
        longitude: address.longitude!,
        latitude: address.latitude!,
        address: address.address,
      );
      if (event.index != null) {
        _dropoffLocations[event.index!] = dropoffLocation;
      } else {
        _dropoffLocations.add(dropoffLocation);
      }
    });
    await _loadedEmitter(emit);
  }
}
