import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/booking/repositories/booking_repository.dart';

part 'my_all_current_only_booking_event.dart';
part 'my_all_current_only_booking_state.dart';
part 'my_all_current_only_booking_bloc.freezed.dart';

////Yesle chaii passenger ko sabaii ongoing/current booking haru fetch garera show garauna help garxa
///kina chaiyo vanda existing CurrentOnly bloc maa garda too many things can come so I use this
class MyAllCurrentOnlyBookingBloc
    extends Bloc<MyAllCurrentOnlyBookingEvent, MyAllCurrentOnlyBookingState> {
  final BookingRepository _repository;
  MyAllCurrentOnlyBookingBloc({required BookingRepository repo})
    : _repository = repo,
      super(MyAllCurrentOnlyBookingState.initial()) {
    on<_Get>(_onGet);
    on<_Reset>(_onReset);
  }

  Future<void> _onGet(
    _Get event,
    Emitter<MyAllCurrentOnlyBookingState> emit,
  ) async {
    emit(MyAllCurrentOnlyBookingState.loading());
    final result = await _repository.getMyBookings(
      bookingStatus: "CurrentOnly",
    );
    result.fold(
      (l) => emit(MyAllCurrentOnlyBookingState.failure(l)),
      (r) => emit(MyAllCurrentOnlyBookingState.loaded(r)),
    );
  }

  Future<void> _onReset(
    _Reset event,
    Emitter<MyAllCurrentOnlyBookingState> emit,
  ) async {
    emit(MyAllCurrentOnlyBookingState.initial());
  }
}
