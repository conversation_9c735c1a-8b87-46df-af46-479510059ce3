import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';

class ConfirmationCodeSection extends StatelessWidget {
  final String confirmationCode;

  const ConfirmationCodeSection({super.key, required this.confirmationCode});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: T.c(context).primaryContainer,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: T.c(context).primary.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Column(
        children: [
          _buildHeader(context),
          const SizedBox(height: 16),
          _buildCodeDisplay(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: T.c(context).primary,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.verified_outlined,
            color: T.c(context).onPrimary,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Confirmation Code",
                style: T
                    .t(context)
                    .titleMedium
                    ?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: T.c(context).onPrimaryContainer,
                    ),
              ),
              Text(
                "Show this code to your rider for verification",
                style: T
                    .t(context)
                    .bodySmall
                    ?.copyWith(
                      color: T.c(context).onPrimaryContainer.withOpacity(0.7),
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCodeDisplay(BuildContext context) {
    return GestureDetector(
      // onTap: () => _copyToClipboard(context),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: T.c(context).surface,
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: T.c(context).primary.withOpacity(0.2)),
          boxShadow: [
            BoxShadow(
              color: T.c(context).shadow.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              confirmationCode,
              style: T
                  .t(context)
                  .headlineMedium
                  ?.copyWith(
                    fontWeight: FontWeight.bold,
                    letterSpacing: 6,
                    color: T.c(context).primary,
                  ),
            ),
            const SizedBox(width: 16),
            // Icon(Icons.copy_outlined, color: T.c(context).primary, size: 24),
          ],
        ),
      ),
    );
  }

  // void _copyToClipboard(BuildContext context) {
  //   Clipboard.setData(ClipboardData(text: confirmationCode));
  //   CustomToast.showSuccess("Code copied to clipboard!");
  // }
}
