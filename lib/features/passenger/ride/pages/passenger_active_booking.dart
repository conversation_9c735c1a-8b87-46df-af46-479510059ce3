import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/widget/build_all_booking.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_booking/get_my_booking_bloc.dart';

class PassengerActiveBooking extends StatefulWidget {
  const PassengerActiveBooking({super.key});

  @override
  State<PassengerActiveBooking> createState() => _PassengerActiveBookingState();
}

class _PassengerActiveBookingState extends State<PassengerActiveBooking> {
  @override
  initState() {
    super.initState();
    sl<GetMyBookingBloc>().add(GetMyBookingEvent.getActiveBookings());
  }

  @override
  Widget build(BuildContext context) {
    return _buildMyActiveBookings();
  }

  BlocBuilder<GetMyBookingBloc, GetMyBookingState> _buildMyActiveBookings() {
    return BlocBuilder<GetMyBookingBloc, GetMyBookingState>(
      builder: (context, state) {
        return state.maybeWhen(
          loading: () => const Center(child: CircularProgressIndicator()),
          loaded: (bookings) {
            return BuildAllBookings(
              bookings: bookings,
              onRefresh: () {
                sl<GetMyBookingBloc>().add(
                  GetMyBookingEvent.getActiveBookings(),
                );
              },
            );
          },
          failure:
              (f) => ErrorWidgetWithRetry(
                failure: f,
                onRetry: () {
                  sl<GetMyBookingBloc>().add(
                    GetMyBookingEvent.getActiveBookings(),
                  );
                },
              ),
          orElse: () => const SizedBox(),
        );
      },
    );
  }
}
