import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/core/widget/custom_pop_button.dart';
import 'package:safari_yatri/core/widget/custom_shimmer.dart';
import 'package:safari_yatri/features/passenger/core/blocs/my_all_current_only_booking/my_all_current_only_booking_bloc.dart';

import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/constant/size_constant.dart';
import 'package:safari_yatri/core/constant/string_constant.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/utils/bottom_sheet.dart';
import 'package:safari_yatri/core/utils/ride_map_helper.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/widget/app_google_map.dart';
import 'package:safari_yatri/core/widget/error_widget_with_retry.dart';
import 'package:safari_yatri/features/booking/blocs/cancel_request/cancel_request_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_current_booking/get_my_current_booking_bloc.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/passenger_route/passenger_route_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/pick_location_picking_status/pickup_location_picking_status_cubit.dart';
import 'package:safari_yatri/features/passenger/ride/pages/cancel_booking.dart';
import 'package:safari_yatri/features/passenger/ride/widgets/confirmation_code.dart';

import '../../../../core/widget/custom_platform_scaffold.dart';

class RideTrackingPageForPassenger extends StatefulWidget {
  const RideTrackingPageForPassenger({
    super.key,
    required this.riderId,
    this.bookingId,
  });
  final int riderId;
  final int? bookingId;
  @override
  State<RideTrackingPageForPassenger> createState() =>
      _RideTrackingPageForPassengerState();
}

class _RideTrackingPageForPassengerState
    extends State<RideTrackingPageForPassenger> {
  GoogleMapController? _mapController;
  LatLng _currentLocation = const LatLng(27.7172, 85.3240);
  late CurrentLocationBloc _currentLocationBloc;
  late GetMyCurrentBookingBloc _myCurrentBookingBloc;
  late CancelRequestBloc _cancelRequestBloc;
  LatLng? driverCurrentLocation;
  Set<Marker> markers = {};
  late final CurrentBookingGetEvent _currentBookingGetEvent;
  @override
  void initState() {
    super.initState();
    _currentLocationBloc =
        sl<CurrentLocationBloc>()
          ..add(CurrentLocationEvent.getCurrentLocation());

    _currentBookingGetEvent = CurrentBookingGetEvent(
      riderId: widget.riderId,
      bookingId: widget.bookingId,
    );
    _myCurrentBookingBloc =
        sl<GetMyCurrentBookingBloc>()
          ..add(GetMyCurrentBookingEvent.get(_currentBookingGetEvent))
          ..add(GetMyCurrentBookingEvent.start());
    _cancelRequestBloc = sl<CancelRequestBloc>();
  }

  @override
  void dispose() {
    _myCurrentBookingBloc.add(GetMyCurrentBookingEvent.stop());
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            _currentLocationBloc.add(CurrentLocationEvent.getCurrentLocation());
            _myCurrentBookingBloc.add(
              GetMyCurrentBookingEvent.get(_currentBookingGetEvent),
            );
          },
          child: BlocListener<CurrentLocationBloc, CurrentLocationState>(
            bloc: _currentLocationBloc,
            listener: (context, state) {
              state.whenOrNull(
                loaded: (data) {
                  _currentLocation = LatLng(data.latitude, data.longitude);
                },
              );
            },
            child: SizedBox.expand(
              child: Stack(
                children: [
                  Positioned.fill(bottom: 100, child: _buildGoogleMap()),
                  Positioned(top: 12, left: 12, child: CustomBackButton()),
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: _buildBookingBloc(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _moveToNewPosition() {
    _mapController?.animateCamera(
      CameraUpdate.newCameraPosition(CameraPosition(target: _currentLocation)),
    );
  }

  BlocConsumer<GetMyCurrentBookingBloc, GetMyCurrentBookingState>
  _buildBookingBloc() {
    return BlocConsumer<GetMyCurrentBookingBloc, GetMyCurrentBookingState>(
      bloc: _myCurrentBookingBloc,
      listener: (context, state) {
        state.whenOrNull(
          loaded: (booking) {
            driverCurrentLocation = LatLng(
              booking.riderCurrentLatitude ?? 0,
              booking.riderCurrentLongitude ?? 0,
            );
            _moveToNewPosition();

            _zoomToFit();

            _setMarkers(booking.serviceStatus == kServiceStarted);

            if (booking.serviceStatus == kServiceCompleted) {
              _resetRouteAndCurrentRide();

              context.goNamed(
                AppRoutesName.ratingPage,
                extra: {'booking': booking, 'isPassenger': true},
              );
            }
          },
        );
      },
      builder: (context, state) {
        return state.maybeWhen(
          loading: () => _buildShimmerUI(),
          failure: (failure) => _buildErrorWidget(failure),
          loaded: (data) {
            return data.serviceStatus == kServiceCancelled
                ? _cancelledBooking(context)
                : _buildTrackingPage(context, data);
          },
          orElse: () => const SizedBox(),
        );
      },
    );
  }

  Container _cancelledBooking(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16),

      decoration: BoxDecoration(color: T.c(context).surface),

      child: Center(
        child: Text("Booking Cancelled", style: T.t(context).headlineSmall),
      ),
    );
  }

  Widget _buildGoogleMap() {
    return BlocBuilder<GetMyCurrentBookingBloc, GetMyCurrentBookingState>(
      builder: (context, state) {
        state.whenOrNull(
          loaded: (booking) async {
            driverCurrentLocation = LatLng(
              booking.riderCurrentLatitude ?? 0,
              booking.riderCurrentLongitude ?? 0,
            );
          },
        );

        return CustomGoogleMap(
          markers: markers,
          initialCameraPosition: CameraPosition(
            target: _currentLocation,
            zoom: kMapInitialZoom,
          ),
          myLocationEnabled: true,
          onMapCreated: (controller) {
            _mapController = controller;
          },
        );
      },
    );
  }

  void _setMarkers(bool isBookingStarted) async {
    markers =
        isBookingStarted
            ? await AppMapHelper.generalSafariMarker(driverCurrentLocation!)
            : await AppMapHelper.generateMarkerCurrentDriverToPassengerPickup([
              driverCurrentLocation!,
              _currentLocation,
            ]);
    if (mounted) setState(() {});
  }

  Future<void> _zoomToFit() async {
    if (driverCurrentLocation != null) {
      await AppMapHelper.zoomToFit(
        mapController: _mapController!,
        points: [driverCurrentLocation!, _currentLocation],
      );
    }
  }

  Container _buildTrackingPage(BuildContext context, BookingModel booking) {
    return Container(
      decoration: _buildBoxShadow(context),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            booking.serviceConfirmationCode == null ||
                    booking.serviceConfirmationCode!.isEmpty
                ? SizedBox()
                : ConfirmationCodeSection(
                  confirmationCode: booking.serviceConfirmationCode!,
                ),
            const SizedBox(height: 20),
            booking.serviceStatus != kServiceBooked
                ? const SizedBox()
                : _buildCancelButton(booking),

            booking.serviceStatus == kServiceStarted ||
                    booking.serviceStatus == kServiceCompleted
                ? CustomButtonPrimary(title: 'Pay now', onPressed: () {})
                : SizedBox(),
          ],
        ),
      ),
    );
  }

  BoxDecoration _buildBoxShadow(BuildContext context) {
    return BoxDecoration(
      color: T.c(context).surface,
      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      boxShadow: [
        BoxShadow(
          color: T.c(context).shadow.withOpacity(0.1),
          blurRadius: 15,
          offset: const Offset(0, -5),
        ),
      ],
    );
  }

  ErrorWidgetWithRetry _buildErrorWidget(Failure failure) {
    return ErrorWidgetWithRetry(
      failure: failure,
      onRetry: () {
        _myCurrentBookingBloc.add(
          GetMyCurrentBookingEvent.get(_currentBookingGetEvent),
        );
      },
    );
  }

  BlocListener<CancelRequestBloc, CancelRequestState> _buildCancelButton(
    BookingModel booking,
  ) {
    return BlocListener<CancelRequestBloc, CancelRequestState>(
      bloc: _cancelRequestBloc,
      listener: _cancelRequestListener,
      child: SizedBox(
        width: double.infinity,
        child: FilledButton(
          onPressed: () => _onCancelPressed(booking.bookingId),
          style: FilledButton.styleFrom(
            backgroundColor: Colors.red.withOpacity(0.1),
            foregroundColor: Colors.red.shade800,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
          child: const Text("Cancel Ride"),
        ),
      ),
    );
  }

  void _cancelRequestListener(BuildContext context, CancelRequestState state) {
    state.whenOrNull(
      loading: () => AppLoadingDialog.show(context),
      loaded: (data) {
        AppLoadingDialog.hide(context);
        CustomToast.showSuccess("Ride cancelled successfully");

        _resetRouteAndCurrentRide();

        context.goNamed(AppRoutesName.passengerHome);
      },
      failure: (failure) {
        AppLoadingDialog.hide(context);
        CustomToast.showError(failure.message);
      },
    );
  }

  Widget _buildShimmerUI() {
    return AppCustomShimmer(height: 120);
  }

  void _onCancelPressed(int bookingId) {
    appShowModalBottomSheet(
      context,
      child: CancelRideBottomSheet(
        onReasonSelected: (reason) {
          // Close bottom sheet before showing dialog
          Navigator.pop(context);
          _cancelRequestBloc.add(
            CancelRequestEvent.cancelRequest(bookingId, reason),
          );
        },
      ),
    );
  }

  void _resetRouteAndCurrentRide() {
    sl<MyAllCurrentOnlyBookingBloc>().add(MyAllCurrentOnlyBookingEvent.get());
    _myCurrentBookingBloc.add(GetMyCurrentBookingEvent.reset());
    sl<PassengerRouteBloc>().add(PassengerRouteEvent.resetPassengerRoute());
    sl<PickupLocationPickingStatusCubit>().enable();
  }
}
