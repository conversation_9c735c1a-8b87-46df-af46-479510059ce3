import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/widget/build_all_booking.dart';
import 'package:safari_yatri/features/passenger/core/blocs/my_all_current_only_booking/my_all_current_only_booking_bloc.dart';

class PassengerCurrentBooking extends StatefulWidget {
  const PassengerCurrentBooking({super.key});

  @override
  State<PassengerCurrentBooking> createState() =>
      _PassengerCurrentBookingState();
}

class _PassengerCurrentBookingState extends State<PassengerCurrentBooking> {
  @override
  initState() {
    super.initState();
    sl<MyAllCurrentOnlyBookingBloc>().add(MyAllCurrentOnlyBookingEvent.get());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<
      MyAllCurrentOnlyBookingBloc,
      MyAllCurrentOnlyBookingState
    >(
      builder: (context, state) {
        return state.maybeWhen(
          loaded: (data) {
            return BuildAllBookings(
              bookings: data,
              onRefresh: () {
                sl<MyAllCurrentOnlyBookingBloc>().add(
                  MyAllCurrentOnlyBookingEvent.get(),
                );
              },
            );
          },
          orElse: () => Center(child: CircularProgressIndicator()),
        );
      },
    );
  }
}
