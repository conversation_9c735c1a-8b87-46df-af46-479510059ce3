// Cancel Ride Bottom Sheet Widget
import 'package:flutter/material.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';

class CancelRideBottomSheet extends StatefulWidget {
  final Function(String reason) onReasonSelected;

  const CancelRideBottomSheet({super.key, required this.onReasonSelected});

  @override
  State<CancelRideBottomSheet> createState() => _CancelRideBottomSheetState();
}

class _CancelRideBottomSheetState extends State<CancelRideBottomSheet> {
  final List<String> _reasons = [
    "Driver is late",
    "Booked by mistake",
    "Found another ride",
    "Driver not responding",
    "Emergency situation",
  ];

  String? _selectedReason;
  final TextEditingController _customReasonController = TextEditingController();

  @override
  void dispose() {
    _customReasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: T.c(context).surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom + 16,
          left: 16,
          right: 16,
          top: 24,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 24),
            _buildReasonList(),
            const SizedBox(height: 16),
            _buildCustomReasonField(context),
            const SizedBox(height: 24),
            _buildConfirmButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        // Drag handle
        Center(
          child: Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: T.c(context).onSurface.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        ),
        const SizedBox(height: 20),

        // Title and subtitle
        Center(
          child: Column(
            children: [
              Text(
                "Cancel Ride",
                style: T
                    .t(context)
                    .headlineSmall
                    ?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.red.shade700,
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                "Please select a reason for cancellation",
                style: T
                    .t(context)
                    .bodyMedium
                    ?.copyWith(color: T.c(context).onSurface.withOpacity(0.7)),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReasonList() {
    return Column(
      children:
          _reasons.map((reason) {
            final isSelected = _selectedReason == reason;
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                border: Border.all(
                  color:
                      isSelected
                          ? T.c(context).primary
                          : T.c(context).onSurface.withOpacity(0.2),
                  width: isSelected ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(12),
                color:
                    isSelected
                        ? T.c(context).primary.withOpacity(0.05)
                        : T.c(context).surface,
              ),
              child: RadioListTile<String>(
                title: Text(
                  reason,
                  style: T
                      .t(context)
                      .bodyLarge
                      ?.copyWith(
                        color:
                            isSelected
                                ? T.c(context).primary
                                : T.c(context).onSurface,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                ),
                value: reason,
                groupValue: _selectedReason,
                activeColor: T.c(context).primary,
                onChanged: (val) {
                  setState(() => _selectedReason = val);
                  FocusScope.of(context).unfocus();
                  _customReasonController.clear();
                },
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 4,
                ),
              ),
            );
          }).toList(),
    );
  }

  Widget _buildCustomReasonField(BuildContext context) {
    return TextField(
      controller: _customReasonController,
      decoration: InputDecoration(
        labelText: "Other reason",
        hintText: "Enter your reason here",
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: T.c(context).onSurface.withOpacity(0.2),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: T.c(context).primary, width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: T.c(context).onSurface.withOpacity(0.2),
          ),
        ),
        labelStyle: T
            .t(context)
            .bodyMedium
            ?.copyWith(color: T.c(context).onSurface.withOpacity(0.7)),
        hintStyle: T
            .t(context)
            .bodyMedium
            ?.copyWith(color: T.c(context).onSurface.withOpacity(0.5)),
      ),
      style: T.t(context).bodyLarge?.copyWith(color: T.c(context).onSurface),
      maxLines: 3,
      minLines: 1,
      onTap: () => setState(() => _selectedReason = null),
    );
  }

  Widget _buildConfirmButton(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.red.shade400, Colors.red.shade600],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _handleConfirmCancellation,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.cancel, color: Colors.white),
                const SizedBox(width: 8),
                Text(
                  "Confirm Cancellation",
                  style: T
                      .t(context)
                      .titleMedium
                      ?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleConfirmCancellation() {
    final reason =
        _customReasonController.text.trim().isNotEmpty
            ? _customReasonController.text.trim()
            : _selectedReason;

    if (reason == null || reason.isEmpty) {
      CustomToast.showError("Please select or enter a reason");
      return;
    }

    widget.onReasonSelected(reason);
  }
}
