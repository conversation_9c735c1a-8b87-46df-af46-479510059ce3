import 'package:flutter/material.dart';

import 'package:safari_yatri/core/widget/trip_tabbar.dart';
import 'package:safari_yatri/features/passenger/ride/pages/passenger_active_booking.dart';
import 'package:safari_yatri/features/passenger/ride/pages/passenger_current_booking.dart';

import '../../../../core/utils/localization_utils.dart';

class PassengerTripPage extends StatelessWidget {
  const PassengerTripPage({super.key});

  @override
  Widget build(BuildContext context) {
    return TripTabBar(
      appBarTitle: L.t.tripBookingCartMyTrips,
      currentBooking: const PassengerCurrentBooking(),
      activeBooking: const PassengerActiveBooking(),
    );
  }
}
