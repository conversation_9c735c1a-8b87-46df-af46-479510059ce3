// First, create a new widget file: search_radius_widget.dart

import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';

class SearchRadiusWidget extends StatefulWidget {
  final Function(int) onRadiusChanged;
  final int initialRadius;

  const SearchRadiusWidget({
    super.key,
    required this.onRadiusChanged,
    this.initialRadius = 25,
  });

  @override
  State<SearchRadiusWidget> createState() => _SearchRadiusWidgetState();
}

class _SearchRadiusWidgetState extends State<SearchRadiusWidget> {
  int selectedRadius = 25;
  final List<int> radiusOptions = [5, 10, 25, 50, 100];

  @override
  void initState() {
    super.initState();
    selectedRadius = widget.initialRadius;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search Radius',
          style: T
              .t(context)
              .bodyMedium
              ?.copyWith(
                fontWeight: FontWeight.w500,
                color: T.c(context).onSurface,
              ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 50,
          decoration: BoxDecoration(
            color: T.c(context).surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children:
                radiusOptions.map((radius) {
                  final isSelected = selectedRadius == radius;
                  return Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          selectedRadius = radius;
                        });
                        widget.onRadiusChanged(radius);
                      },
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        margin: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? T.c(context).primary
                                  : Colors.transparent,
                          borderRadius: BorderRadius.circular(8),
                          border:
                              isSelected
                                  ? null
                                  : Border.all(
                                    color: T
                                        .c(context)
                                        .outline
                                        .withOpacity(0.2),
                                  ),
                        ),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.radio_button_checked,
                                size: 16,
                                color:
                                    isSelected
                                        ? T.c(context).onPrimary
                                        : T.c(context).onSurfaceVariant,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                '${radius}km',
                                style: T
                                    .t(context)
                                    .bodySmall
                                    ?.copyWith(
                                      color:
                                          isSelected
                                              ? T.c(context).onPrimary
                                              : T.c(context).onSurfaceVariant,
                                      fontWeight:
                                          isSelected
                                              ? FontWeight.w600
                                              : FontWeight.w400,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }
}

// Alternative compact version - single row with chips
class SearchRadiusChipWidget extends StatefulWidget {
  final Function(int) onRadiusChanged;
  final int initialRadius;

  const SearchRadiusChipWidget({
    super.key,
    required this.onRadiusChanged,
    this.initialRadius = 25,
  });

  @override
  State<SearchRadiusChipWidget> createState() => _SearchRadiusChipWidgetState();
}

class _SearchRadiusChipWidgetState extends State<SearchRadiusChipWidget> {
  int selectedRadius = 25;
  final List<int> radiusOptions = [5, 10, 25, 50, 100];

  @override
  void initState() {
    super.initState();
    selectedRadius = widget.initialRadius;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const SizedBox(width: 8),
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children:
                  radiusOptions.map((radius) {
                    final isSelected = selectedRadius == radius;
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedRadius = radius;
                          });
                          widget.onRadiusChanged(radius);
                        },
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color:
                                isSelected
                                    ? T.c(context).primary
                                    : T.c(context).surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color:
                                  isSelected
                                      ? T.c(context).primary
                                      : T.c(context).outline.withOpacity(0.3),
                            ),
                          ),
                          child: Text(
                            '${radius}km',
                            style: T
                                .t(context)
                                .bodySmall
                                ?.copyWith(
                                  color:
                                      isSelected
                                          ? T.c(context).onPrimary
                                          : T.c(context).onSurfaceVariant,
                                  fontWeight:
                                      isSelected
                                          ? FontWeight.w600
                                          : FontWeight.w400,
                                ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ),
      ],
    );
  }
}
