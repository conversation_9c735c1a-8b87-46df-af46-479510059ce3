import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/extensions/string_extension.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/utils/currency_formattor.dart';
import 'package:safari_yatri/core/utils/date_time_utils.dart';
import 'package:safari_yatri/core/utils/get_service_status.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';
import 'package:safari_yatri/features/passenger/core/blocs/my_all_current_only_booking/my_all_current_only_booking_bloc.dart';

///Yesle chaii pahile booking haru lai show garxa,
///
///like kasto vanda complete navako, booked matra vako
///booking haru laii show garnu paro
///
///either passenger le cance or complete hunu paro remove huna laii
class GetMyAllCurrentBookingCarouselWidget extends StatelessWidget {
  const GetMyAllCurrentBookingCarouselWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<
      MyAllCurrentOnlyBookingBloc,
      MyAllCurrentOnlyBookingState
    >(
      builder: (context, state) {
        return state.maybeWhen(
          loaded: (data) {
            if (data.isNotEmpty) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
                    child: Row(
                      children: [
                        Icon(
                          Icons.directions_car,
                          color: T.c(context).onSurface,
                          size: 32,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          L.t.currentBooking,
                          style: T
                              .t(context)
                              .headlineLarge
                              ?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: T.c(context).onSurface,
                              ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(
                    height: 140,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      itemCount: data.length,
                      itemBuilder: (context, index) {
                        final booking = data[index];
                        return SimpleCurrentBookingCard(booking: booking);
                      },
                    ),
                  ),
                ],
              );
            } else {
              return const SizedBox.shrink();
            }
          },
          orElse: () => const SizedBox.shrink(),
        );
      },
    );
  }
}

class SimpleCurrentBookingCard extends StatelessWidget {
  final BookingModel booking;

  const SimpleCurrentBookingCard({super.key, required this.booking});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.pushNamed(
          AppRoutesName.rideTrackingPageForPassenger,
          extra: {'riderId': booking.riderId, 'bookingId': booking.bookingId},
        );
      },
      child: Container(
        width: 280,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: T.c(context).surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: T.c(context).outline.withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: T.c(context).shadow.withOpacity(0.06),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: T.c(context).primaryContainer,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.person,
                    size: 16,
                    color: T.c(context).onPrimaryContainer,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    booking.riderName.riderNameOnly,
                    style: T
                        .t(context)
                        .titleSmall
                        ?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: T.c(context).onSurface,
                        ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: getBookingStatusColor(
                      booking.serviceStatus,
                      context,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    getBookingServiceTranslatedStatus(booking.serviceStatus),
                    style: T
                        .t(context)
                        .labelSmall
                        ?.copyWith(color: T.c(context).onPrimary),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Middle: Key info in simple layout
            Row(
              children: [
                // const SizedBox(width: 12),
                Flexible(
                  flex: 2,
                  child: _buildSimpleInfo(
                    context: context,
                    icon: Icons.payment,
                    label: L.t.tripBookingCartFareAmount,
                    value: CurrencyFormatter.format(
                      booking.acceptedFareAmount.toInt(),
                    ),
                    color: T.c(context).secondary,
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: _buildSimpleInfo(
                    context: context,
                    icon: Icons.confirmation_number,
                    label: L.t.tripBookingCartStartDate,
                    value: AppDateTimeUtils.getMMDDYYHHMMAFormatInString(
                      booking.bookingStartDate,
                    ),
                    color: T.c(context).tertiary,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Bottom: Date and action
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 14,
                  color: T.c(context).onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    "Tap to view details",
                    style: T
                        .t(context)
                        .bodySmall
                        ?.copyWith(color: T.c(context).onSurfaceVariant),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 14,
                  color: T.c(context).primary,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleInfo({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 14, color: color),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                label,
                style: T
                    .t(context)
                    .labelSmall
                    ?.copyWith(color: T.c(context).onSurfaceVariant),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: T
              .t(context)
              .bodyMedium
              ?.copyWith(
                fontWeight: FontWeight.w600,
                color: T.c(context).onSurface,
              ),
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}
