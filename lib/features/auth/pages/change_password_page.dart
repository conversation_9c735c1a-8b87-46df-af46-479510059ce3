import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';
import 'package:safari_yatri/features/auth/blocs/change_password/change_password_bloc.dart';
import 'package:safari_yatri/features/auth/models/password_change.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
// import '../../../core/utils/input_validator_helper.dart';
import '../../../core/utils/localization_utils.dart';
import '../../../core/widget/custom_button.dart';
import '../../../core/widget/custom_form_field.dart';
import '../../../core/widget/custom_platform_scaffold.dart';

class ChangePasswordPage extends StatefulWidget {
  const ChangePasswordPage({super.key});

  @override
  State<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends State<ChangePasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _newConfirmPasswordController = TextEditingController();

  @override
  void dispose() {
    _passwordController.dispose();
    _newPasswordController.dispose();
    _newConfirmPasswordController.dispose();
    super.dispose();
  }

  String? _validateRequired(String? value) {
    if (value == null || value.isEmpty) {
      return L.t.drawerScreenSettingsChangePasswordValidationRequired;
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return L.t.drawerScreenSettingsChangePasswordValidationNewRequired;
    }
    if (value.length < 6) {
      return L.t.drawerScreenSettingsChangePasswordValidationMinLength;
    }

    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return L.t.drawerScreenSettingsChangePasswordValidationConfirmRequired;
    }
    if (value != _newPasswordController.text) {
      return L.t.drawerScreenSettingsChangePasswordValidationNotMatch;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      appBar: CustomAppBar(
        title: Text(L.t.drawerScreenSettingsChangePasswordAppBarTitle),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: AppStyles.space8,
          vertical: AppStyles.space12,
        ),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              CustomPasswordFormFieldWidget(
                prefixIcon: LucideIcons.lock,
                label: L.t.drawerScreenSettingsChangePasswordOld,
                controller: _passwordController,
                validator: _validateRequired,
                keys: TextInputType.visiblePassword,
              ),
              const SizedBox(height: AppStyles.space12),
              CustomPasswordFormFieldWidget(
                prefixIcon: LucideIcons.lock,
                label: L.t.drawerScreenSettingsChangePasswordNew,
                controller: _newPasswordController,
                keys: TextInputType.visiblePassword,
                validator: _validatePassword,
              ),
              const SizedBox(height: AppStyles.space12),
              CustomPasswordFormFieldWidget(
                prefixIcon: LucideIcons.lock,
                label: L.t.drawerScreenSettingsChangePasswordConfirm,
                controller: _newConfirmPasswordController,
                validator: _validateConfirmPassword,
                keys: TextInputType.visiblePassword,
              ),
              const SizedBox(height: AppStyles.space12),

              BlocConsumer<ChangePasswordBloc, ChangePasswordState>(
                listener: (context, state) {
                  state.whenOrNull(
                    loaded: (data) {
                      context.pop();
                      CustomToast.showSuccess("Password updated successfully!");
                    },
                    failure: (failure) {
                      CustomToast.showError(failure.message);
                    },
                  );
                },
                builder: (context, state) {
                  final bool isLoading = state.maybeWhen(
                    loading: () => true,
                    orElse: () => false,
                  );
                  return CustomButtonPrimary(
                    title: L.t.drawerScreenSettingsChangePasswordButton,
                    isLoading: isLoading,
                    onPressed:
                        isLoading
                            ? null
                            : () {
                              if (_formKey.currentState!.validate()) {
                                sl<ChangePasswordBloc>().add(
                                  ChangePasswordEvent.passwordChange(
                                    PasswordChangeForm(
                                      password: _passwordController.text,
                                      newPassword: _newPasswordController.text,
                                      retypePassword:
                                          _newConfirmPasswordController.text,
                                    ),
                                  ),
                                );
                              }
                            },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
