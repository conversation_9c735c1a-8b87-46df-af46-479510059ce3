import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/core/utils/input_validator_helper.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_office/get_office_bloc.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../../common/widgets/custom_toast.dart';
import '../../../core/di/dependency_injection.dart';
import '../../../core/router/app_route_names.dart';
import '../../../core/utils/localization_utils.dart';
import '../../../core/theme/app_styles.dart';
import '../../../core/widget/custom_button.dart';
import '../../../core/widget/custom_form_field.dart';
import '../../../core/widget/custom_platform_scaffold.dart';
import '../blocs/user_register/user_register_bloc.dart';
import '../enum/gender_enum.dart';
import '../models/user_register_form.dart';
import '../widgets/applogo.dart';
import 'package:safari_yatri/common/extensions/string_extension.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _currentAddress = TextEditingController();

  Gender _gender = Gender.male;
  @override
  initState() {
    super.initState();

    ///kina get gareko vanda hamilaii office setting chainxa
    ///like otp life time anii weather directly login or not
    ///after registeration
    sl<GetOfficeBloc>().add(const GetOfficeEvent.get());
  }

  @override
  void dispose() {
    super.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _currentAddress.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(AppStyles.space12),
          child: Column(
            spacing: AppStyles.space12,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: AppStyles.space16,
                  children: [
                    AppHeader(
                      appHeaderTitle: L.t.signUpScreenAppHeaderTitle,
                      appHeaderSubTitle: L.t.signUpScreenAppHeaderTitle,
                    ),
                    CustomFormFieldWidget(
                      prefixIcon: LucideIcons.user,
                      label: L.t.signUpScreenFullNameLabel,
                      controller: _nameController,
                      keys: TextInputType.name,
                      validator: InputValidator.validateName,
                    ),
                    CustomFormFieldWidget(
                      prefixIcon: LucideIcons.phone,
                      label: L.t.signUpScreenPhoneNumberLabel,
                      controller: _phoneController,
                      keys: TextInputType.phone,
                      validator: InputValidator.validatePhone,
                    ),
                    CustomFormFieldWidget(
                      prefixIcon: LucideIcons.mail,
                      label: L.t.signUpScreenEmailAddressLabel,
                      controller: _emailController,
                      keys: TextInputType.emailAddress,
                      validator: InputValidator.validateEmail,
                    ),

                    CustomFormFieldWidget(
                      prefixIcon: LucideIcons.mapPin,
                      label: L.t.signUpScreenCurrentAddressLabel,
                      controller: _currentAddress,
                      keys: TextInputType.streetAddress,
                      validator: InputValidator.address,
                    ),

                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          L.t.signUpScreenGenderLabel,
                          style: TextStyle(fontSize: 15, color: Colors.black54),
                        ),
                        Row(
                          children:
                              Gender.values.map((e) {
                                return Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Radio<Gender>(
                                      value: e,
                                      groupValue: _gender,
                                      onChanged: (g) {
                                        setState(() {
                                          _gender = g!;
                                        });
                                      },
                                    ),
                                    Text(
                                      e.name[0].toUpperCase() +
                                          e.name.substring(1),
                                    ),
                                    const SizedBox(width: 16),
                                  ],
                                );
                              }).toList(),
                        ),
                      ],
                    ),
                    BlocConsumer<UserRegisterBloc, UserRegisterState>(
                      listener: (context, state) {
                        state.whenOrNull(
                          loaded: (data) {
                            context.pushNamed(AppRoutesName.verifyOpt);
                          },
                          failure: (failure) {
                            CustomToast.showError(failure.message);
                          },
                        );
                      },
                      builder: (context, state) {
                        final bool isLoading = state.maybeWhen(
                          loading: () => true,
                          orElse: () => false,
                        );

                        return CustomButtonPrimary(
                          title: L.t.signUpScreenSignUpButtonTitle,
                          isLoading: isLoading,
                          onPressed: () {
                            if (_formKey.currentState!.validate()) {
                              sl<UserRegisterBloc>().add(
                                UserRegisterEvent.signUp(
                                  UserRegisterForm(
                                    userAddress: _currentAddress.text,
                                    gender:
                                        _gender.name.firstCharacterUpperCase,
                                    userName: _nameController.text,
                                    phoneNumber: _phoneController.text,
                                    email:
                                        _emailController.text.isEmpty
                                            ? null
                                            : _emailController.text,
                                  ),
                                ),
                              );
                            }
                          },
                        );
                      },
                    ),

                    Align(
                      alignment: Alignment.bottomCenter,
                      child: RichText(
                        text: TextSpan(
                          text: L.t.signUpScreenAlreadyHaveAccountText,
                          style: Theme.of(context).textTheme.titleSmall,
                          children: [
                            TextSpan(
                              text: L.t.signUpScreenSignInText,
                              style: Theme.of(
                                context,
                              ).textTheme.titleSmall?.copyWith(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                              recognizer:
                                  TapGestureRecognizer()
                                    ..onTap = () {
                                      context.pushNamed(AppRoutesName.signIn);
                                    },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
