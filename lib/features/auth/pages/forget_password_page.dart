import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/widget/custom_platform_scaffold.dart' show PlatformScaffold;
import 'package:safari_yatri/features/admin/core/blocs/get_office/get_office_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/forget_password/forget_password_bloc.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import '../../../core/di/dependency_injection.dart';
import '../../../core/utils/input_validator_helper.dart';
import '../../../core/theme/app_styles.dart';
import '../../../core/widget/custom_button.dart';
import '../../../core/widget/custom_form_field.dart';
import '../widgets/applogo.dart';

class ForgetPasswordPage extends StatefulWidget {
  const ForgetPasswordPage({super.key});

  @override
  State<ForgetPasswordPage> createState() => _ForgetPasswordPageState();
}

class _ForgetPasswordPageState extends State<ForgetPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();

  @override
  initState() {
    super.initState();
    sl<GetOfficeBloc>().add(const GetOfficeEvent.get());
  }

  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(AppStyles.space12),
          child: Form(
            key: _formKey,
            child: Column(
              spacing: AppStyles.space12,
              children: [
                AppHeader(
                  appHeaderTitle: 'Forget your password?',
                  appHeaderSubTitle:
                      'Please enter your phone number to reset Password',
                ),
                CustomFormFieldWidget(
                  prefixIcon: LucideIcons.mail,
                  label: 'Phone Number',
                  controller: _phoneController,
                  keys: TextInputType.emailAddress,
                  validator: InputValidator.validatePhone,
                ),
                BlocConsumer<ForgetPasswordBloc, ForgetPasswordState>(
                  listener: (context, state) {
                    state.whenOrNull(
                      loaded: (data) {
                        CustomToast.showSuccess(
                          "Password is sent to phone number",
                        );
                        context.pop();
                      },
                      failure: (failure) {
                        if (failure.message == "Account status: Registered") {
                          context.goNamed(
                            AppRoutesName.verifyOpt,
                            extra: _phoneController.text,
                          );
                          return;
                        }
                        CustomToast.showError(failure.message);
                      },
                    );
                  },
                  builder: (context, state) {
                    final bool isLoading = state.maybeWhen(
                      loading: () => true,
                      orElse: () => false,
                    );
                    return CustomButtonPrimary(
                      title: 'Forgot Password',
                      isLoading: isLoading,
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          sl<ForgetPasswordBloc>().add(
                            ForgetPasswordEvent.forgetPassword(
                              _phoneController.text,
                            ),
                          );
                        }
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
