// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

import 'package:safari_yatri/features/admin/core/blocs/remember_me/remember_me_cubit.dart';

import '../../../common/blocs/app_cubit/app_cubit.dart';
import '../../../common/widgets/custom_toast.dart';
import '../../../core/animations/icon_animation.dart';
import '../../../core/di/dependency_injection.dart';
import '../../../core/router/app_route_names.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_styles.dart';
import '../../../core/utils/input_validator_helper.dart';
import '../../../core/utils/localization_utils.dart';
import '../../../core/utils/theme_utils.dart';
import '../../../core/widget/custom_button.dart';
import '../../../core/widget/custom_form_field.dart';
import '../../../core/widget/custom_platform_scaffold.dart';
import '../blocs/password_toggle_cubit/password_toggle_cubit.dart';
import '../blocs/user_login/user_login_bloc.dart';
import '../models/user_login_form.dart';
import '../widgets/applogo.dart';
import '../widgets/footer.dart';

class SignInPage extends StatefulWidget {
  const SignInPage({super.key});

  @override
  State<SignInPage> createState() => _SignInPageState();
}

class _SignInPageState extends State<SignInPage> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _initializedTextFields = false;
  @override
  initState() {
    super.initState();
    persistenceSl<RememberMeCubit>().loadCredentials();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RememberMeCubit, RememberMeState>(
      listener: (context, state) {
        if (state.isChecked && !_initializedTextFields) {
          _initializedTextFields = true;
          _phoneController.text = state.phone;
          _passwordController.text = state.password;
        }
      },
      builder: (context, state) {
        return PlatformScaffold(
          body: SingleChildScrollView(
            padding: EdgeInsets.all(AppStyles.space12),
            child: Column(
              spacing: AppStyles.space12,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: AppStyles.space12,
                    children: [
                      AppHeader(
                        showBackButton: false,
                        appHeaderTitle: L.t.loginScreenAppHeaderTitle,
                        appHeaderSubTitle: L.t.loginScreenAppHeaderSubTitle,
                      ),
                      CustomFormFieldWidget(
                        prefixIcon: LucideIcons.phone,
                        label: L.t.loginScreenPhoneNumberFormLabel,
                        controller: _phoneController,
                        keys: TextInputType.numberWithOptions(),
                        validator: InputValidator.validatePhone,
                      ),
                      BlocBuilder<PasswordToggleCubit, PasswordToggleState>(
                        builder: (context, state) {
                          return CustomFormFieldWidget(
                            prefixIcon: LucideIcons.lock,
                            label: L.t.loginScreenPasswordFormLabel,
                            controller: _passwordController,
                            keys: TextInputType.visiblePassword,
                            obscureText: state.isToggle,
                            validator: InputValidator.validatePassword,
                            suffixWidget: AnimatedIconToggle(
                              icon1: LucideIcons.eye,
                              icon2: LucideIcons.eyeClosed,
                              onToggle: () {
                                sl<PasswordToggleCubit>().passwordToggle();
                              },
                            ),
                          );
                        },
                      ),
                      BlocBuilder<RememberMeCubit, RememberMeState>(
                        builder: (context, rememberState) {
                          return Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  ShadCheckbox(
                                    value: rememberState.isChecked,
                                    onChanged: (value) {
                                      persistenceSl<RememberMeCubit>()
                                          .toggleRememberMe(value);
                                    },
                                    color: T.c(context).primary,
                                    decoration: ShadDecoration(
                                      border: ShadBorder.all(
                                        color: T.c(context).primary,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: AppStyles.space8),
                                  GestureDetector(
                                    onTap: () {
                                      sl<RememberMeCubit>().toggleRememberMe(
                                        !rememberState.isChecked,
                                      );
                                    },
                                    child: Text(
                                      L.t.loginScreenRememberMeLabel,
                                      style: Theme.of(
                                        context,
                                      ).textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              InkWell(
                                onTap: () {
                                  context.pushNamed(
                                    AppRoutesName.forgetPassword,
                                  );
                                },
                                child: Text(
                                  L.t.loginScreenForgetPasswordBtnTextLabel,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 15,
                                    color: AppColors.lightError,
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                      BlocConsumer<UserLoginBloc, UserLoginState>(
                        listener: (context, state) {
                          state.whenOrNull(
                            loaded: (data) async {
                              persistenceSl<AppCubit>().setUserLoggedIn(true);

                              persistenceSl<RememberMeCubit>().saveCredentials(
                                _phoneController.text,
                                _passwordController.text,
                              );

                              CustomToast.showSuccess(
                                L.t.loginScreenSucessToastLabel,
                              );

                              context.goNamed(
                                AppRoutesName.locationPermissionHandler,
                              );
                            },
                            failure: (failure) {
                              if (failure.message == "Login not active yet.") {
                                CustomToast.showError(
                                  L.t.loginScreenErrorToastLoginNotActiveLabel,
                                );
                                context.goNamed(AppRoutesName.forgetPassword);
                                return;
                              }
                              CustomToast.showError(failure.message);
                            },
                          );
                        },
                        builder: (context, state) {
                          final bool isLoading = state.maybeWhen(
                            loading: () => true,
                            orElse: () => false,
                          );
                          return CustomButtonPrimary(
                            title: L.t.loginScreenButtonTitle,
                            isLoading: isLoading,
                            onPressed:
                                isLoading
                                    ? () {}
                                    : () {
                                      if (_formKey.currentState!.validate()) {
                                        sl<UserLoginBloc>().add(
                                          UserLoginEvent.singIn(
                                            UserLoginForm(
                                              loginId: _phoneController.text,
                                              password:
                                                  _passwordController.text,
                                            ),
                                          ),
                                        );
                                      }
                                    },
                          );
                        },
                      ),
                      Align(
                        alignment: Alignment.bottomCenter,
                        child: RichText(
                          text: TextSpan(
                            text: L.t.loginScreenDontHaveAnAccount,
                            style: Theme.of(context).textTheme.titleSmall,
                            children: [
                              TextSpan(
                                text: L.t.loginScreenSignUpButtonTitle,
                                style: Theme.of(
                                  context,
                                ).textTheme.titleSmall?.copyWith(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                                recognizer:
                                    TapGestureRecognizer()
                                      ..onTap = () {
                                        context.pushNamed(AppRoutesName.signUp);
                                      },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                FooterText(),
              ],
            ),
          ),
        );
      },
    );
  }
}
