import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/app_cubit/app_cubit.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/utils/app_loading_dialogs.dart';
import 'package:safari_yatri/core/widget/custom_button.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_office/get_office_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/resend_otp/resend_opt_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/verify_otp/verify_otp_bloc.dart';
import 'package:safari_yatri/features/auth/pages/otp_text_field.dart';
import '../../../core/utils/localization_utils.dart';
import '../../../core/widget/custom_platform_scaffold.dart';
import '../widgets/applogo.dart';

class OTPVerificationPage extends StatefulWidget {
  const OTPVerificationPage({super.key, this.phoneNumber});
  final String? phoneNumber;

  @override
  State<OTPVerificationPage> createState() => _OTPVerificationPageState();
}

class _OTPVerificationPageState extends State<OTPVerificationPage> {
  Timer? _timer;
  int _secondsRemaining = 180;
  bool _canResend = false;
  bool showError = false;
  bool _autoLoginAfterOtpVerification = true;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    widget.phoneNumber == null ? startTimer() : _resendOtp();
  }

  void startTimer() {
    _canResend = false;
    final getOfficeState = sl<GetOfficeBloc>().state;
    _secondsRemaining = getOfficeState.maybeWhen(
      loaded: (data) {
        _autoLoginAfterOtpVerification = data.autoLoginAfterOtpVerification;
        return data.otpLifeInSecond;
      },
      orElse: () => 180,
    );

    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_secondsRemaining == 0) {
        timer.cancel();
        setState(() => _canResend = true);
      } else {
        setState(() => _secondsRemaining--);
      }
    });
  }

  void _resendOtp() {
    sl<ResendOptBloc>().add(ResendOptEvent.resend());
    setState(() => showError = false);
    _timer?.cancel();
    startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<VerifyOtpBloc, VerifyOtpState>(
      listener: (context, state) {
        state.whenOrNull(
          loading: () => AppLoadingDialog.show(context),
          loaded: (message) {
            AppLoadingDialog.hide(context);
            CustomToast.showSuccess(message);

            if (!_autoLoginAfterOtpVerification) {
              CustomToast.showInfo(L.t.verifyOtpScreenPleaseLoginToast);
              context.goNamed(AppRoutesName.signIn);
              return;
            }

            persistenceSl<AppCubit>().setUserLoggedIn(true);
            context.goNamed(AppRoutesName.locationPermissionHandler);
          },
          failure: (f) {
            AppLoadingDialog.hide(context);
            CustomToast.showError(f.message);
            setState(() => showError = true);
          },
        );
      },
      child: PlatformScaffold(
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AppHeader(
                  appHeaderTitle: L.t.verifyOtpScreenAppHeaderTitle,
                  appHeaderSubTitle: L.t.verifyOtpScreenAppHeaderSubTitle,
                ),
                const SizedBox(height: AppStyles.space64),

                OTPInput(
                  length: 4,
                  hasError: showError,
                  onCompleted: (otp) {
                    sl<VerifyOtpBloc>().add(
                      VerifyOtpEvent.verifyOtp(
                        opt: otp,
                        phoneNumber: widget.phoneNumber,
                      ),
                    );
                  },
                ),
                const SizedBox(height: 24),

                BlocListener<ResendOptBloc, ResendOptState>(
                  listener: (context, state) {
                    state.whenOrNull(
                      loaded: (_) {
                        setState(() => showError = false);
                        CustomToast.showInfo(
                          L.t.verifyOtpScreenResendSuccessToast,
                        );
                      },
                      failure: (failure) {
                        CustomToast.showError(failure.message);
                        setState(() => showError = true);
                      },
                    );
                  },
                  child:
                      _canResend
                          ? CustomButtonPrimary(
                            height: 40,
                            title: L.t.verifyOtpScreenResendButtonText,
                            onPressed: _resendOtp,
                          )
                          : Text(
                            L.t.verifyOtpScreenResendCountdownText(
                              _secondsRemaining,
                            ),
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(color: Colors.grey),
                          ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
