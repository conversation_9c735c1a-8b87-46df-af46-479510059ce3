import 'package:dartz/dartz.dart';
import 'package:safari_yatri/common/typedef/either_type.dart';
import 'package:safari_yatri/core/network/api_service.dart';
import 'package:safari_yatri/core/services/cache_service.dart';
import 'package:safari_yatri/features/auth/models/user_register_form.dart';

import '../models/password_change.dart';
import '../models/user_login_form.dart';

abstract interface class AuthRepository {
  FutureEither<String> signUp({required UserRegisterForm userRegisterForm});

  FutureEither<String> signIn({required UserLoginForm userLoginForm});

  FutureEither<String> verifyOTP({required String otp, String? phoneNumber});

  FutureEither<String> resendOTP();

  FutureEither<String> signOut();

  FutureEither<String> forgetPassword({required String phoneNumber});

  FutureEither<String> changePassword({
    required PasswordChangeForm passwordChangeForm,
  });
}

class AuthRepositoryI implements AuthRepository {
  final ApiService _apiService;
  AuthRepositoryI({required ApiService apiService}) : _apiService = apiService;

  @override
  FutureEither<String> signUp({
    required UserRegisterForm userRegisterForm,
  }) async {
    final response = await _apiService.post<String>(
      'UserList/Insert',
      data: {...userRegisterForm.toMap()},
    );

    if (response.isRight()) {
      await CacheService.instance.setUserPhoneNumber(
        userRegisterForm.phoneNumber,
      );
    }

    return response.fold((failure) => Left(failure), (data) => Right(data));
  }

  @override
  FutureEither<String> signIn({required UserLoginForm userLoginForm}) async {
    final response = await _apiService.post<Map>(
      'Auth/Login',
      data: {...userLoginForm.toMap()},
    );

    // if (response.isRight()) {
    //   await CacheService.instance.setAuthToken();
    // }

    return response.fold((failure) => Left(failure), (data) async {
      await CacheService.instance.setUserPhoneNumber(userLoginForm.loginId);
      await CacheService.instance.setAuthToken(
        data['Token'],
        DateTime.parse(data['Expiration']),
        refreshToken: data['RefreshToken'],
      );
      return Right("Login Successful");
    });
  }

  @override
  FutureEither<String> signOut() async {
    throw UnimplementedError();
  }

  @override
  FutureEither<String> verifyOTP({required String otp, String? phoneNumber}) async {
     phoneNumber??=await CacheService.instance.getUserPhoneNumber();
    final response = await _apiService.put<Map>(
      'Auth/ActivateLoginByOtp',
      data: {
        "LoginId":phoneNumber!,
        "Otp": otp,
      },
    );

    return response.fold((failure) => Left(failure), (data) async {
      await CacheService.instance.setAuthToken(
        data['Token'],
        DateTime.parse(data['Expiration']),
        refreshToken: data['RefreshToken'],
      );
      return Right("Verified");
    });
  }

  @override
  FutureEither<String> resendOTP() async {
    final phoneNumber = await CacheService.instance.getUserPhoneNumber();
    final response = await _apiService.get<String>(
      'Auth/GetNewOtp?phoneNo=$phoneNumber',
    );
    return response.fold((failure) => Left(failure), (data) => Right(data));
  }

  @override
  FutureEither<String> forgetPassword({required String phoneNumber}) async {
    final response = await _apiService.post<String>(
      'Auth/ForgotPassword?PhoneNo=$phoneNumber',
    );
    return response.fold((failure) => Left(failure), (data) => Right(data));
  }

  @override
  FutureEither<String> changePassword({
    required PasswordChangeForm passwordChangeForm,
  }) async {
    final response = await _apiService.put<String>(
      'MyProfile/ChangePassword',
      data: {
        "LoginId": await CacheService.instance.getUserPhoneNumber(),
        "Password": passwordChangeForm.password,
        "NewPassword": passwordChangeForm.newPassword,
        "RetypePassword": passwordChangeForm.retypePassword,
      },
    );

    return response.fold(
      (failure) => Left(failure),
      (data) => Right("Password Updated Successfuly!"),
    );
  }
}
