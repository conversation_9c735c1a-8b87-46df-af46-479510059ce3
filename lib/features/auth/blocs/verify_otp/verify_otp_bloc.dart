import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/auth/repositories/auth_repository.dart';

part 'verify_otp_event.dart';
part 'verify_otp_state.dart';
part 'verify_otp_bloc.freezed.dart';

class VerifyOtpBloc extends Bloc<VerifyOtpEvent, VerifyOtpState> {
  final AuthRepository _authRepository;

  VerifyOtpBloc({required AuthRepository repo})
    : _authRepository = repo,
      super(VerifyOtpState.initial()) {
    on<_VerifyOtp>(_onVerifyOtp);
  }

  Future<void> _onVerifyOtp(
    _VerifyOtp event,
    Emitter<VerifyOtpState> emit,
  ) async {
    emit(VerifyOtpState.loading());
    final result = await _authRepository.verifyOTP(otp: event.opt,phoneNumber: event.phoneNumber );

    ///***** Test failure */
    // final result = await Future.delayed(
    //   Duration(seconds: 2),
    // ).then((value) => Left(TestFailure(message: "Test failure")));

    result.fold(
      (failure) => emit(VerifyOtpState.failure(failure)),
      (data) => emit(VerifyOtpState.loaded(data)),
    );
  }
}
