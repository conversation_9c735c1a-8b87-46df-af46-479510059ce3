// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'verify_otp_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VerifyOtpEvent {
  String get opt;
  String? get phoneNumber;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is VerifyOtpEvent &&
            (identical(other.opt, opt) || other.opt == opt) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, opt, phoneNumber);

  @override
  String toString() {
    return 'VerifyOtpEvent(opt: $opt, phoneNumber: $phoneNumber)';
  }
}

/// Adds pattern-matching-related methods to [VerifyOtpEvent].
extension VerifyOtpEventPatterns on VerifyOtpEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_VerifyOtp value)? verifyOtp,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _VerifyOtp() when verifyOtp != null:
        return verifyOtp(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_VerifyOtp value) verifyOtp,
  }) {
    final _that = this;
    switch (_that) {
      case _VerifyOtp():
        return verifyOtp(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_VerifyOtp value)? verifyOtp,
  }) {
    final _that = this;
    switch (_that) {
      case _VerifyOtp() when verifyOtp != null:
        return verifyOtp(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String opt, String? phoneNumber)? verifyOtp,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _VerifyOtp() when verifyOtp != null:
        return verifyOtp(_that.opt, _that.phoneNumber);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String opt, String? phoneNumber) verifyOtp,
  }) {
    final _that = this;
    switch (_that) {
      case _VerifyOtp():
        return verifyOtp(_that.opt, _that.phoneNumber);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String opt, String? phoneNumber)? verifyOtp,
  }) {
    final _that = this;
    switch (_that) {
      case _VerifyOtp() when verifyOtp != null:
        return verifyOtp(_that.opt, _that.phoneNumber);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _VerifyOtp implements VerifyOtpEvent {
  const _VerifyOtp({required this.opt, this.phoneNumber});

  @override
  final String opt;
  @override
  final String? phoneNumber;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _VerifyOtp &&
            (identical(other.opt, opt) || other.opt == opt) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, opt, phoneNumber);

  @override
  String toString() {
    return 'VerifyOtpEvent.verifyOtp(opt: $opt, phoneNumber: $phoneNumber)';
  }
}
