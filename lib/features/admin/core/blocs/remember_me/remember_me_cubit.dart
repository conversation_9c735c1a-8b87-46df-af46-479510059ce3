// remember_me_cubit.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:safari_yatri/core/services/cache_service.dart';


part 'remember_me_state.dart';

class RememberMeCubit extends Cubit<RememberMeState> {
  RememberMeCubit() : super(RememberMeState.initial());

  Future<void> loadCredentials() async {
    try {
      final hasCredentials = await CacheService.instance.hasRememberMeCredentials();
      if (hasCredentials) {
        final credentials = await CacheService.instance.getRememberMeCredentials();
        emit(
          state.copyWith(
            isChecked: true,
            phone: credentials['phone'] ?? '',
            password: credentials['password'] ?? '',
          ),
        );
      }
    } catch (e) {
      // Log error if needed
    }
  }

  void toggleRememberMe(bool value) {
    emit(state.copyWith(isChecked: value));
    if (!value) {
      clearCredentials();
    }
  }

  Future<void> saveCredentials(String phone, String password) async {
    if (state.isChecked) {
      await CacheService.instance.setRememberMeCredentials(
        phone: phone,
        password: password,
      );
    }
  }

  Future<void> clearCredentials() async {
    await CacheService.instance.clearRememberMeCredentials();
  }
}
