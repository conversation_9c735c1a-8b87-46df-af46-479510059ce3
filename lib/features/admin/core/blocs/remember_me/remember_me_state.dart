// remember_me_state.dart
part of 'remember_me_cubit.dart';

class RememberMeState extends Equatable {
  final bool isChecked;
  final String phone;
  final String password;

  const RememberMeState({
    required this.isChecked,
    required this.phone,
    required this.password,
  });

  factory RememberMeState.initial() =>
      const RememberMeState(isChecked: false, phone: '', password: '');

  RememberMeState copyWith({bool? isChecked, String? phone, String? password}) {
    return RememberMeState(
      isChecked: isChecked ?? this.isChecked,
      phone: phone ?? this.phone,
      password: password ?? this.password,
    );
  }

  @override
  List<Object?> get props => [isChecked, phone, password];
}
