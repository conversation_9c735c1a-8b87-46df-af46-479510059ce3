import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/common/extensions/string_extension.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/admin/core/models/user_model.dart';
import 'package:safari_yatri/features/admin/core/repositories/user_repository.dart';

part 'user_list_event.dart';
part 'user_list_state.dart';
part 'user_list_bloc.freezed.dart';

class UserListBloc extends Bloc<UserListEvent, UserListState> {
  final UserRepository _userRepository;

  // Cache: key = "Passenger|Active", value = list of users
  final Map<String, List<UserModel>> _userCache = {};

  UserListBloc({required UserRepository repo})
    : _userRepository = repo,
      super(UserListState.initial()) {
    on<_GetUserList>(_onGetUserList);
  }

  Future<void> _onGetUserList(
    _GetUserList event,
    Emitter<UserListState> emit,
  ) async {
    final userType = event.userType.firstCharacterUpperCase;
    final loginStatus = event.loginStatus.firstCharacterUpperCase;
    final key = '$userType|$loginStatus';

    // Return from cache if available and not force fetching
    if (_userCache.containsKey(key) && !event.forceFetch) {
      emit(UserListState.loaded(_userCache[key]!));
      return;
    }

    emit(UserListState.loading());

    final result = await _userRepository.getUserList(userType, loginStatus);

    result.fold((failure) => emit(UserListState.failure(failure)), (users) {
      _userCache[key] = users; // Cache the result
      emit(UserListState.loaded(users));
    });
  }
}
