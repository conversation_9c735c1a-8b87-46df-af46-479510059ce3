import 'package:flutter/material.dart';
import 'package:safari_yatri/common/blocs/logout/logout_cubit.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';
import 'package:safari_yatri/core/widget/logout_cubit_listener.dart';
import 'package:safari_yatri/core/widget/logout_dialog.dart';
import '../../../drawer/widget/drawer_header_widget.dart';
import '../../../drawer/widget/version_text.dart';
import '../../../my_profile/bloc/get_my_profile/my_profile_bloc.dart';

class AdminDrawerWidget extends StatefulWidget {
  const AdminDrawerWidget({super.key});

  @override
  State<AdminDrawerWidget> createState() => _AdminDrawerWidgetState();
}

class _AdminDrawerWidgetState extends State<AdminDrawerWidget> {
  late LogoutCubit logoutCubit;
  @override
  initState() {
    super.initState();
    logoutCubit = persistenceSl<LogoutCubit>();
    sl<MyProfileBloc>().add(const MyProfileEvent.get());
  }

  @override
  Widget build(BuildContext context) {
    return buildPassengerAppDrawer(context);
  }

  Widget buildPassengerAppDrawer(BuildContext context) {
    return LogoutCubitListener(
      child: SafeArea(
        child: Drawer(
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topRight: Radius.zero,
              bottomRight: Radius.zero,
            ),
          ),
          child: CustomDrawerHeader(widget: _buildDrawerBody(context)),
        ),
      ),
    );
  }

  Widget _buildDrawerBody(BuildContext context) {
    return Column(
      children: [
        const Spacer(),
        Padding(
          padding: const EdgeInsets.all(AppStyles.space12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const AppVersionText(),
              SizedBox(height: AppStyles.space4),
              Container(
                decoration: BoxDecoration(
                  color: T.c(context).surfaceContainerHighest.withAlpha(128),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: T.c(context).outline.withAlpha(51)),
                ),
                child: ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: T.c(context).error.withAlpha(26),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.logout_outlined,
                      color: T.c(context).error,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    'Logout',
                    style: T
                        .t(context)
                        .bodyLarge
                        ?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: T.c(context).error,
                        ),
                  ),
                  subtitle: Text(
                    'Sign out of your account',
                    style: T
                        .t(context)
                        .bodySmall
                        ?.copyWith(color: T.c(context).onSurfaceVariant),
                  ),
                  trailing: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: T.c(context).onSurfaceVariant,
                  ),
                  onTap: () {
                    // Navigator.of(context).pop();
                    // Show logout confirmation dialog
                    _showLogoutConfirmation(context);
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showLogoutConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return LogoutAlertDialog();
      },
    );
  }
}
