import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_admin_role_list/get_admin_role_list_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_admin_user_role/get_admin_user_role_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/manage_user_list/manage_user_list_bloc.dart';
import 'package:safari_yatri/features/admin/core/models/role_model.dart';

class AdminRoleSelectorWidget extends StatefulWidget {
  final int userId;

  const AdminRoleSelectorWidget({super.key, required this.userId});

  @override
  State<AdminRoleSelectorWidget> createState() =>
      _AdminRoleSelectorWidgetState();
}

class _AdminRoleSelectorWidgetState extends State<AdminRoleSelectorWidget> {
  // ValueNotifiers for reactive state management
  final ValueNotifier<Map<int, bool>> _selectedRolesNotifier = ValueNotifier(
    {},
  );
  final ValueNotifier<Map<int, bool>> _originalRolesNotifier = ValueNotifier(
    {},
  );
  final ValueNotifier<bool> _hasChangesNotifier = ValueNotifier(false);
  final ValueNotifier<bool> _isUpdatingNotifier = ValueNotifier(false);

  // Blocs - only initialize what we need
  late final GetAdminUserRoleBloc _adminUserRoleBloc;
  late final GetAdminRoleListBloc _adminRoleListBloc;
  late final ManageUserListBloc _manageUserListBloc;

  @override
  void initState() {
    super.initState();
    _initializeBlocs();
    _loadInitialData();
  }

  void _initializeBlocs() {
    _adminUserRoleBloc = sl<GetAdminUserRoleBloc>();
    _adminRoleListBloc = sl<GetAdminRoleListBloc>();
    _manageUserListBloc = sl<ManageUserListBloc>();
  }

  void _loadInitialData() {
    _adminUserRoleBloc.add(GetAdminUserRoleEvent.get(widget.userId));
    _adminRoleListBloc.add(GetAdminRoleListEvent.get());
  }

  void _handleRoleTap(int roleId) {
    final currentRoles = Map<int, bool>.from(_selectedRolesNotifier.value);
    currentRoles[roleId] = !(currentRoles[roleId] ?? false);
    _selectedRolesNotifier.value = currentRoles;
    _updateHasChanges();
  }

  void _updateHasChanges() {
    final selectedRoles = _selectedRolesNotifier.value;
    final originalRoles = _originalRolesNotifier.value;

    bool hasChanges = false;
    for (final entry in selectedRoles.entries) {
      if ((originalRoles[entry.key] ?? false) != entry.value) {
        hasChanges = true;
        break;
      }
    }
    _hasChangesNotifier.value = hasChanges;
  }

  void _handleUpdateUserRoles() {
    if (!_hasChangesNotifier.value) return;

    _isUpdatingNotifier.value = true;

    // Get the list of selected role IDs
    final selectedRoleIds =
        _selectedRolesNotifier.value.entries
            .where((entry) => entry.value)
            .map((entry) => entry.key)
            .toList();

    _manageUserListBloc.add(
      ManageUserListEvent.updateRoles(
        userId: widget.userId,
        roles:
            selectedRoleIds
                .map((id) => AdminRoleUpdateModel(roleId: id, selected: true))
                .toList(),
      ),
    );
  }

  void _handleUpdateResult(ManageUserListState state) {
    state.maybeWhen(
      loaded: (message) {
        _isUpdatingNotifier.value = false;
        // Update original roles to current selection
        _originalRolesNotifier.value = Map<int, bool>.from(
          _selectedRolesNotifier.value,
        );
        _hasChangesNotifier.value = false;
        CustomToast.showSuccess(message);
      },
      failure: (error) {
        _isUpdatingNotifier.value = false;
        CustomToast.showError(error.message);
      },
      orElse: () {},
    );
  }

  void _initializeUserRoles(List<dynamic> userRoles) {
    if (_originalRolesNotifier.value.isEmpty) {
      final originalRoles = <int, bool>{};
      final selectedRoles = <int, bool>{};

      for (final role in userRoles) {
        originalRoles[role.roleId] = true;
        selectedRoles[role.roleId] = true;
      }

      _originalRolesNotifier.value = originalRoles;
      _selectedRolesNotifier.value = selectedRoles;
      _hasChangesNotifier.value = false;
    }
  }

  @override
  void dispose() {
    _selectedRolesNotifier.dispose();
    _originalRolesNotifier.dispose();
    _hasChangesNotifier.dispose();
    _isUpdatingNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = T.c(context);
    final textTheme = T.t(context);

    return BlocListener<ManageUserListBloc, ManageUserListState>(
      bloc: _manageUserListBloc,
      listener: (context, state) => _handleUpdateResult(state),
      child: BlocBuilder<GetAdminUserRoleBloc, GetAdminUserRoleState>(
        bloc: _adminUserRoleBloc,
        builder: (context, userRoleState) {
          userRoleState.maybeWhen(
            loaded: (data) => _initializeUserRoles(data),
            orElse: () {},
          );

          return BlocBuilder<GetAdminRoleListBloc, GetAdminRoleListState>(
            bloc: _adminRoleListBloc,
            builder: (context, roleListState) {
              return roleListState.maybeWhen(
                failure:
                    (f) => _buildErrorState(f.message, colorScheme, textTheme),
                loaded:
                    (data) => _buildLoadedState(data, colorScheme, textTheme),
                orElse: () => _buildLoadingState(colorScheme, textTheme),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildErrorState(
    String message,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.error.withAlpha(100), width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.error_outline, color: colorScheme.error, size: 48),
          const SizedBox(height: 12),
          Text(
            'Error Loading Roles',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onErrorContainer,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onErrorContainer.withAlpha(180),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(ColorScheme colorScheme, TextTheme textTheme) {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline.withAlpha(100), width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(color: colorScheme.primary),
          const SizedBox(height: 16),
          Text(
            'Loading roles...',
            style: textTheme.bodyLarge?.copyWith(
              color: colorScheme.onSurface.withAlpha(180),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadedState(
    List<dynamic> data,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline.withAlpha(100), width: 1),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withAlpha(25),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(colorScheme, textTheme),
          const SizedBox(height: 20),
          _buildRoleGrid(data, colorScheme, textTheme),
          const SizedBox(height: 24),
          _buildUpdateButton(colorScheme, textTheme),
        ],
      ),
    );
  }

  Widget _buildHeader(ColorScheme colorScheme, TextTheme textTheme) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.admin_panel_settings,
            color: colorScheme.onPrimaryContainer,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Admin Role Management',
              style: textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                color: colorScheme.onSurface,
              ),
            ),
            Text(
              'Select roles for this user',
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withAlpha(180),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRoleGrid(
    List<dynamic> data,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return ValueListenableBuilder<Map<int, bool>>(
      valueListenable: _selectedRolesNotifier,
      builder: (context, selectedRoles, _) {
        return Wrap(
          spacing: 12,
          runSpacing: 12,
          children:
              data.map((role) {
                final isSelected = selectedRoles[role.roleId] ?? false;
                return _buildRoleChip(role, isSelected, colorScheme, textTheme);
              }).toList(),
        );
      },
    );
  }

  Widget _buildRoleChip(
    dynamic role,
    bool isSelected,
    ColorScheme colorScheme,
    TextTheme textTheme,
  ) {
    return Material(
      elevation: isSelected ? 4 : 1,
      borderRadius: BorderRadius.circular(12),
      shadowColor: colorScheme.shadow.withAlpha(50),
      child: InkWell(
        onTap: () => _handleRoleTap(role.roleId),
        borderRadius: BorderRadius.circular(12),
        splashColor: colorScheme.primary.withAlpha(50),
        highlightColor: colorScheme.primary.withAlpha(25),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            gradient:
                isSelected
                    ? LinearGradient(
                      colors: [
                        colorScheme.primaryContainer,
                        colorScheme.primaryContainer.withAlpha(200),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                    : null,
            color: isSelected ? null : colorScheme.surfaceContainerHigh,
            border: Border.all(
              color:
                  isSelected
                      ? colorScheme.primary
                      : colorScheme.outline.withAlpha(100),
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: isSelected ? colorScheme.primary : colorScheme.surface,
                  border: Border.all(
                    color:
                        isSelected ? colorScheme.primary : colorScheme.outline,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                child:
                    isSelected
                        ? Icon(
                          Icons.check,
                          size: 14,
                          color: colorScheme.onPrimary,
                        )
                        : null,
              ),
              const SizedBox(width: 10),
              Text(
                role.roleName,
                style: textTheme.bodyMedium?.copyWith(
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  color:
                      isSelected
                          ? colorScheme.onPrimaryContainer
                          : colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUpdateButton(ColorScheme colorScheme, TextTheme textTheme) {
    return ValueListenableBuilder<bool>(
      valueListenable: _hasChangesNotifier,
      builder: (context, hasChanges, _) {
        return ValueListenableBuilder<bool>(
          valueListenable: _isUpdatingNotifier,
          builder: (context, isUpdating, _) {
            return SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed:
                    hasChanges && !isUpdating ? _handleUpdateUserRoles : null,
                child:
                    isUpdating
                        ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  colorScheme.onPrimary,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Updating...',
                              style: textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: colorScheme.onPrimary,
                              ),
                            ),
                          ],
                        )
                        : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              hasChanges
                                  ? Icons.update
                                  : Icons.check_circle_outline,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              hasChanges ? 'Save Changes' : 'No Changes Made',
                              style: textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
              ),
            );
          },
        );
      },
    );
  }
}
