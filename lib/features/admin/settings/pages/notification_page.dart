import 'package:flutter/material.dart';

import '../../../../core/widget/custom_platform_scaffold.dart';

class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  State<NotificationSettingsPage> createState() =>
      _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  bool pushNotifications = true;
  bool emailAlerts = true;
  bool smsAlerts = false;
  bool darkMode = false;

  @override
  Widget build(BuildContext context) {
    return PlatformScaffold(
      appBar: AppBar(title: const Text('Notification Settings')),
      body: Center(
        child: Padding(
          padding: EdgeInsetsGeometry.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildSettingTile(
                title: 'Push Notifications',
                subtitle: 'Receive push notifications for new orders',
                value: pushNotifications,
                onChanged: (val) => setState(() => pushNotifications = val),
              ),
              buildSettingTile(
                title: 'Email Alerts',
                subtitle: 'Get email notifications for important events',
                value: emailAlerts,
                onChanged: (val) => setState(() => emailAlerts = val),
              ),
              buildSettingTile(
                title: 'SMS Alerts',
                subtitle: 'Receive SMS for urgent notifications',
                value: smsAlerts,
                onChanged: (val) => setState(() => smsAlerts = val),
              ),
              buildSettingTile(
                title: 'Dark Mode',
                subtitle: 'Use dark theme for the interface',
                value: darkMode,
                onChanged: (val) => setState(() => darkMode = val),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildSettingTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 13),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.white,
            activeTrackColor: Colors.blueAccent,
            inactiveThumbColor: Colors.grey.shade400,
            inactiveTrackColor: Colors.grey.shade300,
          ),
        ],
      ),
    );
  }
}
