import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_office/get_office_bloc.dart';
import '../../../../core/utils/currency_formattor.dart';
import '../../../../core/widget/custom_platform_scaffold.dart';
import '../../core/models/office_model.dart';
import 'change_office_password.dart';

class OfficeManagementPage extends StatefulWidget {
  const OfficeManagementPage({super.key});

  @override
  State<OfficeManagementPage> createState() => _OfficeManagementPageState();
}

class _OfficeManagementPageState extends State<OfficeManagementPage> {
  void _showChangePasswordDialog(BuildContext context) {
    showDialog(context: context, builder: (_) => const ChangePasswordDialog());
  }

  void _onPressEditSetting(OfficeModel model) {
    context.pushNamed(AppRoutesName.adminOfficeSettingsEdit, extra: model);
  }

  OfficeModel? _officeModel;

  @override
  Widget build(BuildContext context) {
    final bool isLargeScreen = MediaQuery.of(context).size.width > 800;

    return BlocBuilder<GetOfficeBloc, GetOfficeState>(
      builder: (context, state) {
        state.whenOrNull(
          loaded: (data) {
            _officeModel = data;
          },
        );
        return PlatformScaffold(
          appBar: AppBar(title: Text('Office Management')),
          body:
              _officeModel == null
                  ? Center(child: CircularProgressIndicator())
                  : SingleChildScrollView(
                    padding: EdgeInsets.all(10),
                    child: Column(
                      children: [
                        // Action Buttons Section
                        Align(
                          alignment:
                              isLargeScreen
                                  ? Alignment.centerLeft
                                  : Alignment.center,
                          child: Wrap(
                            spacing: 16.0,
                            runSpacing: 16.0,
                            alignment: WrapAlignment.start,
                            crossAxisAlignment: WrapCrossAlignment.center,
                            children: [
                              ElevatedButton.icon(
                                onPressed:
                                    () => _showChangePasswordDialog(context),
                                icon: const Icon(Icons.lock_reset),
                                label: const Text('Change Password'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      Theme.of(context).colorScheme.primary,
                                  foregroundColor:
                                      Theme.of(context).colorScheme.onPrimary,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 12,
                                  ),
                                  elevation: 4, // Added elevation
                                ),
                              ),
                              ElevatedButton.icon(
                                onPressed:
                                    () => _onPressEditSetting(_officeModel!),
                                icon: const Icon(Icons.edit),
                                label: const Text('Edit Settings'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      Theme.of(context).colorScheme.secondary,
                                  foregroundColor:
                                      Theme.of(context).colorScheme.onSecondary,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 12,
                                  ),
                                  elevation: 4, // Added elevation
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 30),

                        // Office Information Card
                        _buildSettingsCard(
                          context,
                          title: 'Office Information',
                          children: [
                            _buildInfoRow(
                              context,
                              label: 'Office Name',
                              value: _officeModel?.officeName,
                            ),
                            _buildInfoRow(
                              context,
                              label: 'Short Name',
                              value: _officeModel?.officeShortName,
                            ),
                            _buildInfoRow(
                              context,
                              label: 'Address',
                              value: _officeModel?.officeAddress,
                            ),
                            _buildInfoRow(
                              context,
                              label: 'Phone',
                              value: _officeModel?.officePhone,
                            ),
                            _buildInfoRow(
                              context,
                              label: 'Email',
                              value: _officeModel?.officeEmail,
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),

                        // System Settings Card
                        _buildSettingsCard(
                          context,
                          title: 'System Settings',
                          children: [
                            _buildInfoRow(
                              context,
                              label: 'OTP Limited Per Day',
                              value: _officeModel?.otpLimitPerDay.toString(),
                            ),
                            _buildInfoRow(
                              context,
                              label: 'OTP Life (seconds)',
                              value: _officeModel?.otpLifeInSecond.toString(),
                            ),

                            _buildInfoRow(
                              context,
                              label: 'Passenger Cart Life (Minute)',
                              value:
                                  _officeModel?.passengerCartLifeInMinute
                                      .toString(),
                            ),
                            _buildInfoRow(
                              context,
                              label: 'Fiscal Year',
                              value: _officeModel?.fiscalYear,
                            ),
                            _buildInfoRow(
                              context,
                              label: 'Auto Login After OTP',
                              value:
                                  _officeModel!.autoLoginAfterOtpVerification
                                      ? 'Yes'
                                      : 'No',
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),

                        // Rider Settings Card
                        _buildSettingsCard(
                          context,
                          title: 'Rider Settings',
                          children: [
                            _buildInfoRow(
                              context,
                              label: 'Renewal Charge',
                              value: CurrencyFormatter.format(
                                _officeModel!.riderRenewalCharge,
                                locale: 'ne_Ne_EN',
                              ),
                            ),
                            _buildInfoRow(
                              context,
                              label: 'Renewal Frequency',
                              value:
                                  '${_officeModel?.riderRenewalFrequencyInDays} days',
                            ),
                            _buildInfoRow(
                              context,
                              label: 'Rider Point Rate',
                              value:
                                  '${_officeModel?.riderPointRateInPercent}%',
                            ),
                            _buildInfoRow(
                              context,
                              label: 'Passenger Point Rate',
                              value:
                                  '${_officeModel?.passengerPointRateInPercent}%',
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
        );
      },
    );
  }
}

// Helper method to build a consistent card style
Widget _buildSettingsCard(
  BuildContext context, {
  required String title,
  required List<Widget> children,
}) {
  return Card(
    elevation: 6, // Increased elevation for a more prominent shadow
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(18), // Slightly more rounded corners
    ),
    margin: EdgeInsets.zero, // Remove default card margin
    child: Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const Divider(height: 24, thickness: 1), // Added a subtle divider
          ...children,
        ],
      ),
    ),
  );
}

Widget _buildInfoRow(
  BuildContext context, {
  required String label,
  required String? value,
}) {
  return Padding(
    padding: const EdgeInsets.symmetric(
      vertical: 10.0,
    ), // Increased vertical padding
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 180, // Consistent width for labels
          child: Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.w600, // Slightly bolder
              color:
                  Theme.of(
                    context,
                  ).colorScheme.onSurfaceVariant, // Use theme color
            ),
          ),
        ),
        Expanded(
          child: Text(
            value ?? "No data",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w500,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ),
      ],
    ),
  );
}
