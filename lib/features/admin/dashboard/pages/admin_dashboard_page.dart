import 'package:flutter/material.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';

import '../../../../core/utils/theme_utils.dart';

class AdminDashboardPage extends StatelessWidget {
  const AdminDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(
        horizontal: AppStyles.space12,
        vertical: AppStyles.space12,
      ),
      child: Column(
        spacing: 10,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Dashboard Overview', style: T.t(context).titleLarge),

          // Responsive grid layout for stat cards
          LayoutBuilder(
            builder: (context, constraints) {
              // Adjust grid based on screen size
              int crossAxisCount =
                  constraints.maxWidth > 1200
                      ? 4 // Large desktop
                      : constraints.maxWidth > 800
                      ? 2 // Medium desktop/tablet
                      : 1; // Mobile

              double childAspectRatio =
                  constraints.maxWidth > 1200
                      ? 1.5
                      : constraints.maxWidth > 800
                      ? 1.8
                      : 2.5;

              return GridView.count(
                shrinkWrap: true,
                physics:
                    const NeverScrollableScrollPhysics(), // Disable GridView's own scrolling
                crossAxisCount: crossAxisCount,
                childAspectRatio: childAspectRatio,
                crossAxisSpacing: 10,
                mainAxisSpacing: 10,
                children: const [
                  DashboardStatCard(
                    title: 'Total Bookings',
                    value: '1,234',
                    icon: Icons.book_online,
                    color: Colors.blueAccent,
                  ),
                  DashboardStatCard(
                    title: 'Total Revenue',
                    value: '\$56,789',
                    icon: Icons.attach_money,
                    color: Colors.greenAccent,
                  ),
                  DashboardStatCard(
                    title: 'Active Users',
                    value: '876',
                    icon: Icons.person,
                    color: Colors.orangeAccent,
                  ),
                  DashboardStatCard(
                    title: 'Active Riders',
                    value: '123',
                    icon: Icons.directions_car,
                    color: Colors.purpleAccent,
                  ),
                ],
              );
            },
          ),

          // Recent Activity Section
          Text(
            'Recent Activity',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          Card(
            margin: EdgeInsets.all(0),
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Example activity items (replace with dynamic data)
                  ActivityItem(
                    icon: Icons.person_add,
                    title: 'New User Registration',
                    description: 'John Doe registered as a passenger',
                    time: '10 minutes ago',
                    color: Colors.green,
                  ),
                  const Divider(),
                  ActivityItem(
                    icon: Icons.directions_car,
                    title: 'New Rider Application',
                    description: 'Jane Smith applied to be a rider',
                    time: '1 hour ago',
                    color: Colors.blue,
                  ),
                  const Divider(),
                  ActivityItem(
                    icon: Icons.payment,
                    title: 'Payment Received',
                    description: 'Payment of \$25 received for booking #12345',
                    time: '3 hours ago',
                    color: Colors.purple,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class DashboardStatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const DashboardStatCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: T.c(context).surfaceContainerHigh,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: T
                    .t(context)
                    .displaySmall
                    ?.copyWith(color: T.c(context).onSecondaryContainer),
              ),
              Icon(icon, color: color, size: 28),
            ],
          ),
          const SizedBox(height: 10),
          Text(value, style: T.t(context).titleLarge),
        ],
      ),
    );
  }
}

// Widget for activity items
class ActivityItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;
  final String time;
  final Color color;

  const ActivityItem({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    required this.time,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CircleAvatar(
          backgroundColor: color.withAlpha((255 * 0.2).toInt()),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
              ),
              const SizedBox(height: 4),
              Text(
                time,
                style: TextStyle(color: Colors.grey.shade500, fontSize: 12),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
