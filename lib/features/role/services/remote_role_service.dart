import 'package:shared_preferences/shared_preferences.dart';

///yesle chaii locally user ko remote/server role k ho
///store garney kaam garxa
class RemoteRoleService {
  static RemoteRoleService? _instance;
  static const String roleKey = 'remote_user_role';
  static const String riderRequestPendingKey = 'ride_request_pending';

  final SharedPreferences _prefs;

  RemoteRoleService._(this._prefs);

  static Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    _instance ??= RemoteRoleService._(prefs);
  }

  static RemoteRoleService get instance {
    if (_instance == null) {
      throw Exception(
        "RemoteRoleService not initialized. Call RemoteRoleService.init() first.",
      );
    }
    return _instance!;
  }

  Future<void> setUserRole(String role) async {
    await _prefs.setString(roleKey, role);
  }

  String? getUserRole() {
    return _prefs.getString(roleKey);
  }

  ///[Yo role le chaii hamilaii user ko role k ho ta passenger , rider, admin plus Pending Rider ho haina ]
  Future<void> setRiderProfileRole(String role) async {
    await _prefs.setString(riderRequestPendingKey, role);
  }

  ///yaha chaii isPending panii aauxa if they requested for driver so
  String? getPassengerRoleFromProfile() {
    final String? role = _prefs.getString(riderRequestPendingKey);
    return role;
  }

  Future<void> clearUserRole() async {
    await _prefs.remove(roleKey);
    await _prefs.remove(riderRequestPendingKey);
  }
}
