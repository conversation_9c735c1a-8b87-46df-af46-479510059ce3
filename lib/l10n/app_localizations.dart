import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_ne.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('ne')
  ];

  /// No description provided for @appname.
  ///
  /// In en, this message translates to:
  /// **'Safari Yatri'**
  String get appname;

  /// No description provided for @welcomeback.
  ///
  /// In en, this message translates to:
  /// **'Welcome Back'**
  String get welcomeback;

  /// No description provided for @signintocontinue.
  ///
  /// In en, this message translates to:
  /// **'Sign in to continue'**
  String get signintocontinue;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @signin.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signin;

  /// No description provided for @signup.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signup;

  /// No description provided for @donthaveaccount.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account? Sign Up'**
  String get donthaveaccount;

  /// No description provided for @selectedLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectedLanguage;

  /// No description provided for @chooseLater.
  ///
  /// In en, this message translates to:
  /// **'You can change language later from settings'**
  String get chooseLater;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @skip.
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @loginScreenRememberMeLabel.
  ///
  /// In en, this message translates to:
  /// **'Remember Me'**
  String get loginScreenRememberMeLabel;

  /// No description provided for @onBoardingfirstScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'One app for all services.'**
  String get onBoardingfirstScreenTitle;

  /// No description provided for @onBoardingSecondScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Get there, on time'**
  String get onBoardingSecondScreenTitle;

  /// No description provided for @onBoardingThirdScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Pay, as you want.'**
  String get onBoardingThirdScreenTitle;

  /// No description provided for @onBoardingfirstScreenDesc.
  ///
  /// In en, this message translates to:
  /// **'Get a ride with your finertips.'**
  String get onBoardingfirstScreenDesc;

  /// No description provided for @onBoardingSecondScreenDesc.
  ///
  /// In en, this message translates to:
  /// **'Best the traffic and reach your destination fast, every time.'**
  String get onBoardingSecondScreenDesc;

  /// No description provided for @onBoardingThirdScreenDesc.
  ///
  /// In en, this message translates to:
  /// **'Cash? Card? Wallet? We accept it all. Let\'s get started.'**
  String get onBoardingThirdScreenDesc;

  /// No description provided for @welcomeText.
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get welcomeText;

  /// No description provided for @welcomeDescription.
  ///
  /// In en, this message translates to:
  /// **'Have a better sharing exprience'**
  String get welcomeDescription;

  /// No description provided for @locationPermissionTitle.
  ///
  /// In en, this message translates to:
  /// **'Enable Location Permission'**
  String get locationPermissionTitle;

  /// No description provided for @locationPermissionDescription.
  ///
  /// In en, this message translates to:
  /// **'To get great service you need to provide location permission. You Can always change permission from settings.'**
  String get locationPermissionDescription;

  /// No description provided for @enableLocation.
  ///
  /// In en, this message translates to:
  /// **'Enable Location'**
  String get enableLocation;

  /// No description provided for @locationEnableAccess.
  ///
  /// In en, this message translates to:
  /// **'Enable Location Access'**
  String get locationEnableAccess;

  /// No description provided for @locationRequestingPermission.
  ///
  /// In en, this message translates to:
  /// **'Requesting Permission...'**
  String get locationRequestingPermission;

  /// No description provided for @locationAccessGranted.
  ///
  /// In en, this message translates to:
  /// **'Location Access Granted'**
  String get locationAccessGranted;

  /// No description provided for @locationNeeded.
  ///
  /// In en, this message translates to:
  /// **'Location Needed'**
  String get locationNeeded;

  /// No description provided for @locationDescription.
  ///
  /// In en, this message translates to:
  /// **'This app needs your location to connect riders with drivers and help passengers track trips in real time.'**
  String get locationDescription;

  /// No description provided for @locationWaitingConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Waiting for your confirmation in the permission dialog.'**
  String get locationWaitingConfirmation;

  /// No description provided for @locationRedirecting.
  ///
  /// In en, this message translates to:
  /// **'Great! We\'ve got your location. Redirecting you now…'**
  String get locationRedirecting;

  /// No description provided for @locationDenied.
  ///
  /// In en, this message translates to:
  /// **'You denied location access. Tap below to try again.'**
  String get locationDenied;

  /// No description provided for @locationPermanentlyDenied.
  ///
  /// In en, this message translates to:
  /// **'Location permanently denied. Please open settings to allow access.'**
  String get locationPermanentlyDenied;

  /// No description provided for @locationRequired.
  ///
  /// In en, this message translates to:
  /// **'Location permission is required to continue.'**
  String get locationRequired;

  /// No description provided for @locationEnableButton.
  ///
  /// In en, this message translates to:
  /// **'Enable Location'**
  String get locationEnableButton;

  /// No description provided for @locationOpenSettings.
  ///
  /// In en, this message translates to:
  /// **'Open App Settings'**
  String get locationOpenSettings;

  /// No description provided for @locationTryAgain.
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get locationTryAgain;

  /// No description provided for @locationGoToSettings.
  ///
  /// In en, this message translates to:
  /// **'Go to Settings'**
  String get locationGoToSettings;

  /// No description provided for @locationAllowAccess.
  ///
  /// In en, this message translates to:
  /// **'Allow Location Access'**
  String get locationAllowAccess;

  /// No description provided for @locationPermissionHeader.
  ///
  /// In en, this message translates to:
  /// **'Location Permission Required'**
  String get locationPermissionHeader;

  /// No description provided for @locationServiceDisabled.
  ///
  /// In en, this message translates to:
  /// **'Location Services Disabled'**
  String get locationServiceDisabled;

  /// No description provided for @locationServiceDisabledMessage.
  ///
  /// In en, this message translates to:
  /// **'Location services are turned off on your device. Please enable them to continue.'**
  String get locationServiceDisabledMessage;

  /// No description provided for @openLocationSettings.
  ///
  /// In en, this message translates to:
  /// **'Open Location Settings'**
  String get openLocationSettings;

  /// No description provided for @showCaseDrawerDescription.
  ///
  /// In en, this message translates to:
  /// **'View your profile and settings'**
  String get showCaseDrawerDescription;

  /// No description provided for @showCasePassengerCoutDescription.
  ///
  /// In en, this message translates to:
  /// **'Select the number of passengers'**
  String get showCasePassengerCoutDescription;

  /// No description provided for @showCasePickUpLocationDescription.
  ///
  /// In en, this message translates to:
  /// **'Select your pickup location'**
  String get showCasePickUpLocationDescription;

  /// No description provided for @showCaseDestinationLocationDescription.
  ///
  /// In en, this message translates to:
  /// **'Select your destination location'**
  String get showCaseDestinationLocationDescription;

  /// No description provided for @showCaseFindRiderDescription.
  ///
  /// In en, this message translates to:
  /// **'Find available riders'**
  String get showCaseFindRiderDescription;

  /// No description provided for @numberOfPassenger.
  ///
  /// In en, this message translates to:
  /// **'Select the number of passengers for your trip'**
  String get numberOfPassenger;

  /// No description provided for @buttonTitle.
  ///
  /// In en, this message translates to:
  /// **'Find Rider'**
  String get buttonTitle;

  /// No description provided for @passengerHomeBottomSheetDestination.
  ///
  /// In en, this message translates to:
  /// **'Your Destinations'**
  String get passengerHomeBottomSheetDestination;

  /// No description provided for @passengerHomeDirectionErrorText.
  ///
  /// In en, this message translates to:
  /// **'Direction Route is null'**
  String get passengerHomeDirectionErrorText;

  /// No description provided for @loginScreenAppHeaderTitle.
  ///
  /// In en, this message translates to:
  /// **'LogIn your Account'**
  String get loginScreenAppHeaderTitle;

  /// No description provided for @loginScreenAppHeaderSubTitle.
  ///
  /// In en, this message translates to:
  /// **'Please enter your details to continue'**
  String get loginScreenAppHeaderSubTitle;

  /// No description provided for @loginScreenPhoneNumberFormLabel.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get loginScreenPhoneNumberFormLabel;

  /// No description provided for @loginScreenPasswordFormLabel.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get loginScreenPasswordFormLabel;

  /// No description provided for @loginScreenForgetPasswordBtnTextLabel.
  ///
  /// In en, this message translates to:
  /// **'Forget Password?'**
  String get loginScreenForgetPasswordBtnTextLabel;

  /// No description provided for @loginScreenSucessToastLabel.
  ///
  /// In en, this message translates to:
  /// **'Login Successfull'**
  String get loginScreenSucessToastLabel;

  /// No description provided for @loginScreenErrorToastLoginNotActiveLabel.
  ///
  /// In en, this message translates to:
  /// **'Please verify your number.Login not active yet.'**
  String get loginScreenErrorToastLoginNotActiveLabel;

  /// No description provided for @loginScreenButtonTitle.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get loginScreenButtonTitle;

  /// No description provided for @loginScreenDontHaveAnAccount.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get loginScreenDontHaveAnAccount;

  /// No description provided for @loginScreenSignUpButtonTitle.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get loginScreenSignUpButtonTitle;

  /// No description provided for @footerWidgetTextSpanByContinue.
  ///
  /// In en, this message translates to:
  /// **'By continuing, you agree to our '**
  String get footerWidgetTextSpanByContinue;

  /// No description provided for @footerWidgetTextSpanTermsOfServices.
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get footerWidgetTextSpanTermsOfServices;

  /// No description provided for @footerWidgetTextSpanAnd.
  ///
  /// In en, this message translates to:
  /// **' and '**
  String get footerWidgetTextSpanAnd;

  /// No description provided for @footerWidgetTextSpanPrivacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get footerWidgetTextSpanPrivacyPolicy;

  /// No description provided for @signUpScreenAppHeaderTitle.
  ///
  /// In en, this message translates to:
  /// **'Create your Account'**
  String get signUpScreenAppHeaderTitle;

  /// No description provided for @signUpScreenAppHeaderSubTitle.
  ///
  /// In en, this message translates to:
  /// **'Please enter your details to sign up'**
  String get signUpScreenAppHeaderSubTitle;

  /// No description provided for @signUpScreenFullNameLabel.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get signUpScreenFullNameLabel;

  /// No description provided for @signUpScreenPhoneNumberLabel.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get signUpScreenPhoneNumberLabel;

  /// No description provided for @signUpScreenEmailAddressLabel.
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get signUpScreenEmailAddressLabel;

  /// No description provided for @signUpScreenCurrentAddressLabel.
  ///
  /// In en, this message translates to:
  /// **'Current Address'**
  String get signUpScreenCurrentAddressLabel;

  /// No description provided for @signUpScreenGenderLabel.
  ///
  /// In en, this message translates to:
  /// **'Gender'**
  String get signUpScreenGenderLabel;

  /// No description provided for @signUpScreenSignUpButtonTitle.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUpScreenSignUpButtonTitle;

  /// No description provided for @signUpScreenAlreadyHaveAccountText.
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get signUpScreenAlreadyHaveAccountText;

  /// No description provided for @signUpScreenSignInText.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signUpScreenSignInText;

  /// No description provided for @verifyOtpScreenAppHeaderTitle.
  ///
  /// In en, this message translates to:
  /// **'Verify OTP'**
  String get verifyOtpScreenAppHeaderTitle;

  /// No description provided for @verifyOtpScreenAppHeaderSubTitle.
  ///
  /// In en, this message translates to:
  /// **'Please enter the 4-digit OTP sent to your number.'**
  String get verifyOtpScreenAppHeaderSubTitle;

  /// No description provided for @verifyOtpScreenResendButtonText.
  ///
  /// In en, this message translates to:
  /// **'Resend OTP'**
  String get verifyOtpScreenResendButtonText;

  /// No description provided for @verifyOtpScreenResendCountdownText.
  ///
  /// In en, this message translates to:
  /// **'Resend available in {_secondsRemaining} seconds'**
  String verifyOtpScreenResendCountdownText(Object _secondsRemaining);

  /// No description provided for @verifyOtpScreenResendSuccessToast.
  ///
  /// In en, this message translates to:
  /// **'OTP resent successfully'**
  String get verifyOtpScreenResendSuccessToast;

  /// No description provided for @verifyOtpScreenResendFailedToast.
  ///
  /// In en, this message translates to:
  /// **'Failed to resend OTP'**
  String get verifyOtpScreenResendFailedToast;

  /// No description provided for @verifyOtpScreenOtpVerifySuccessToast.
  ///
  /// In en, this message translates to:
  /// **'OTP verification successful'**
  String get verifyOtpScreenOtpVerifySuccessToast;

  /// No description provided for @verifyOtpScreenOtpVerifyFailedToast.
  ///
  /// In en, this message translates to:
  /// **'Incorrect OTP. Please try again.'**
  String get verifyOtpScreenOtpVerifyFailedToast;

  /// No description provided for @verifyOtpScreenPleaseLoginToast.
  ///
  /// In en, this message translates to:
  /// **'Please, login with your credentials.'**
  String get verifyOtpScreenPleaseLoginToast;

  /// No description provided for @driverDrawerScreenHomeTitle.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get driverDrawerScreenHomeTitle;

  /// No description provided for @driverDrawerScreenMyTripsTitle.
  ///
  /// In en, this message translates to:
  /// **'My Trips'**
  String get driverDrawerScreenMyTripsTitle;

  /// No description provided for @driverDrawerScreenProfileTitle.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get driverDrawerScreenProfileTitle;

  /// No description provided for @drawerScreenSettingsTitle.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get drawerScreenSettingsTitle;

  /// No description provided for @drawerScreenSettingsTheme.
  ///
  /// In en, this message translates to:
  /// **'Theme Mode'**
  String get drawerScreenSettingsTheme;

  /// No description provided for @drawerScreenSettingsThemeSystem.
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get drawerScreenSettingsThemeSystem;

  /// No description provided for @drawerScreenSettingsThemeDark.
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get drawerScreenSettingsThemeDark;

  /// No description provided for @drawerScreenSettingsThemeLight.
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get drawerScreenSettingsThemeLight;

  /// No description provided for @drawerScreenSettingsSelectLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get drawerScreenSettingsSelectLanguage;

  /// No description provided for @drawerScreenSettingsChangePasswordAppBarTitle.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get drawerScreenSettingsChangePasswordAppBarTitle;

  /// No description provided for @drawerScreenSettingsChangePasswordOld.
  ///
  /// In en, this message translates to:
  /// **'Old Password'**
  String get drawerScreenSettingsChangePasswordOld;

  /// No description provided for @drawerScreenSettingsChangePasswordNew.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get drawerScreenSettingsChangePasswordNew;

  /// No description provided for @drawerScreenSettingsChangePasswordConfirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get drawerScreenSettingsChangePasswordConfirm;

  /// No description provided for @drawerScreenSettingsChangePasswordButton.
  ///
  /// In en, this message translates to:
  /// **'Update Password'**
  String get drawerScreenSettingsChangePasswordButton;

  /// No description provided for @drawerScreenSettingsChangePasswordValidationRequired.
  ///
  /// In en, this message translates to:
  /// **'This field is required'**
  String get drawerScreenSettingsChangePasswordValidationRequired;

  /// No description provided for @drawerScreenSettingsChangePasswordValidationNewRequired.
  ///
  /// In en, this message translates to:
  /// **'New password is required'**
  String get drawerScreenSettingsChangePasswordValidationNewRequired;

  /// No description provided for @drawerScreenSettingsChangePasswordValidationMinLength.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters long'**
  String get drawerScreenSettingsChangePasswordValidationMinLength;

  /// No description provided for @drawerScreenSettingsChangePasswordValidationConfirmRequired.
  ///
  /// In en, this message translates to:
  /// **'Confirm password is required'**
  String get drawerScreenSettingsChangePasswordValidationConfirmRequired;

  /// No description provided for @drawerScreenSettingsChangePasswordValidationNotMatch.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get drawerScreenSettingsChangePasswordValidationNotMatch;

  /// No description provided for @drawerScreenEmergencySafetyTitle.
  ///
  /// In en, this message translates to:
  /// **'Emergency & Safety'**
  String get drawerScreenEmergencySafetyTitle;

  /// No description provided for @driverDrawerScreenPassengerModeButton.
  ///
  /// In en, this message translates to:
  /// **'Passenger Mode'**
  String get driverDrawerScreenPassengerModeButton;

  /// No description provided for @emergencyScreenAppBarTitle.
  ///
  /// In en, this message translates to:
  /// **'Emergency & Safety'**
  String get emergencyScreenAppBarTitle;

  /// No description provided for @emergencyScreenSupportButton.
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get emergencyScreenSupportButton;

  /// No description provided for @emergencyScreenEmergencyContactsButton.
  ///
  /// In en, this message translates to:
  /// **'Emergency contacts'**
  String get emergencyScreenEmergencyContactsButton;

  /// No description provided for @emergencyScreenCall100Button.
  ///
  /// In en, this message translates to:
  /// **'Call 100'**
  String get emergencyScreenCall100Button;

  /// No description provided for @emergencyScreenHowYoureProtectedTitle.
  ///
  /// In en, this message translates to:
  /// **'How you\'re protected'**
  String get emergencyScreenHowYoureProtectedTitle;

  /// No description provided for @emergencyScreenFeatureProactiveSafetySupport.
  ///
  /// In en, this message translates to:
  /// **'Proactive safety support'**
  String get emergencyScreenFeatureProactiveSafetySupport;

  /// No description provided for @emergencyScreenFeatureDriversVerification.
  ///
  /// In en, this message translates to:
  /// **'Drivers verification'**
  String get emergencyScreenFeatureDriversVerification;

  /// No description provided for @emergencyScreenFeatureProtectingPrivacy.
  ///
  /// In en, this message translates to:
  /// **'Protecting your privacy'**
  String get emergencyScreenFeatureProtectingPrivacy;

  /// No description provided for @emergencyScreenFeatureStayingSafe.
  ///
  /// In en, this message translates to:
  /// **'Staying safe on every ride'**
  String get emergencyScreenFeatureStayingSafe;

  /// No description provided for @emergencyScreenFeatureAccidentsSteps.
  ///
  /// In en, this message translates to:
  /// **'Accidents: Steps to take'**
  String get emergencyScreenFeatureAccidentsSteps;

  /// No description provided for @notificationScreenAppBarTitle.
  ///
  /// In en, this message translates to:
  /// **'Notification Preferences'**
  String get notificationScreenAppBarTitle;

  /// No description provided for @notificationScreenRideRequestsTitle.
  ///
  /// In en, this message translates to:
  /// **'Ride Requests'**
  String get notificationScreenRideRequestsTitle;

  /// No description provided for @notificationScreenRideRequestsSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Get notified about new ride requests'**
  String get notificationScreenRideRequestsSubtitle;

  /// No description provided for @notificationScreenPromotionsTitle.
  ///
  /// In en, this message translates to:
  /// **'Promotions'**
  String get notificationScreenPromotionsTitle;

  /// No description provided for @notificationScreenPromotionsSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Receive promotional offers and bonuses'**
  String get notificationScreenPromotionsSubtitle;

  /// No description provided for @notificationScreenEarningsTitle.
  ///
  /// In en, this message translates to:
  /// **'Earnings'**
  String get notificationScreenEarningsTitle;

  /// No description provided for @notificationScreenEarningsSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Daily and weekly earnings summaries'**
  String get notificationScreenEarningsSubtitle;

  /// No description provided for @notificationScreenSafetyTitle.
  ///
  /// In en, this message translates to:
  /// **'Safety'**
  String get notificationScreenSafetyTitle;

  /// No description provided for @notificationScreenSafetySubtitle.
  ///
  /// In en, this message translates to:
  /// **'Safety alerts and emergency notifications'**
  String get notificationScreenSafetySubtitle;

  /// No description provided for @passengerDrawerScreenHomeTitle.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get passengerDrawerScreenHomeTitle;

  /// No description provided for @passengerDrawerScreenYourTripsTitle.
  ///
  /// In en, this message translates to:
  /// **'Your Trips'**
  String get passengerDrawerScreenYourTripsTitle;

  /// No description provided for @passengerDrawerScreenProfileTitle.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get passengerDrawerScreenProfileTitle;

  /// No description provided for @passengerDrawerScreenSettingsTitle.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get passengerDrawerScreenSettingsTitle;

  /// No description provided for @passengerDrawerScreenEmergencySafetyTitle.
  ///
  /// In en, this message translates to:
  /// **'Emergency & Safety'**
  String get passengerDrawerScreenEmergencySafetyTitle;

  /// No description provided for @passengerDrawerScreenDriverModeButton.
  ///
  /// In en, this message translates to:
  /// **'Driver Mode'**
  String get passengerDrawerScreenDriverModeButton;

  /// No description provided for @settingScreenAppBarTitle.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settingScreenAppBarTitle;

  /// No description provided for @settingScreenLanguageTitle.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get settingScreenLanguageTitle;

  /// No description provided for @settingScreenLanguageSubtitle.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get settingScreenLanguageSubtitle;

  /// No description provided for @settingScreenChangePasswordTitle.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get settingScreenChangePasswordTitle;

  /// No description provided for @settingScreenNotificationTitle.
  ///
  /// In en, this message translates to:
  /// **'Notification'**
  String get settingScreenNotificationTitle;

  /// No description provided for @settingScreenRulesAndTermsTitle.
  ///
  /// In en, this message translates to:
  /// **'Rules and terms'**
  String get settingScreenRulesAndTermsTitle;

  /// No description provided for @settingScreenLogoutTitle.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get settingScreenLogoutTitle;

  /// No description provided for @settingScreenDeleteAccountTitle.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get settingScreenDeleteAccountTitle;

  /// No description provided for @settingScreenDeleteDialogTitle.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get settingScreenDeleteDialogTitle;

  /// No description provided for @settingScreenDeleteDialogMessage.
  ///
  /// In en, this message translates to:
  /// **'This action will permanently delete your account and all data associated with it. Are you sure you want to proceed?'**
  String get settingScreenDeleteDialogMessage;

  /// No description provided for @settingScreenDeleteDialogConfirmText.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get settingScreenDeleteDialogConfirmText;

  /// No description provided for @settingScreenDeleteDialogCancelText.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get settingScreenDeleteDialogCancelText;

  /// No description provided for @appVersionWidgetLoading.
  ///
  /// In en, this message translates to:
  /// **'Loading version...'**
  String get appVersionWidgetLoading;

  /// No description provided for @appVersionWidgetUnavailable.
  ///
  /// In en, this message translates to:
  /// **'Version unavailable'**
  String get appVersionWidgetUnavailable;

  /// No description provided for @appVersionWidgetFormat.
  ///
  /// In en, this message translates to:
  /// **'Version {version}+{buildNumber}'**
  String appVersionWidgetFormat(Object buildNumber, Object version);

  /// No description provided for @locationPermissionScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Enable Location Permission'**
  String get locationPermissionScreenTitle;

  /// No description provided for @locationPermissionScreenDescription.
  ///
  /// In en, this message translates to:
  /// **'To get great service you need to provide location permission. You can always change permission from settings.'**
  String get locationPermissionScreenDescription;

  /// No description provided for @locationPermissionScreenGivePermissionButton.
  ///
  /// In en, this message translates to:
  /// **'Give Permissions'**
  String get locationPermissionScreenGivePermissionButton;

  /// No description provided for @locationPermissionScreenDialogMessage.
  ///
  /// In en, this message translates to:
  /// **'We require your precise location to seamlessly connect you to nearby services providers.\n\nPlease turn on device location.'**
  String get locationPermissionScreenDialogMessage;

  /// No description provided for @locationPermissionScreenDialogGivePermission.
  ///
  /// In en, this message translates to:
  /// **'Give Location Permission'**
  String get locationPermissionScreenDialogGivePermission;

  /// No description provided for @locationPermissionScreenDialogOpenSettings.
  ///
  /// In en, this message translates to:
  /// **'Open Settings'**
  String get locationPermissionScreenDialogOpenSettings;

  /// No description provided for @fareOfferScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Offer your fare'**
  String get fareOfferScreenTitle;

  /// No description provided for @fareOfferScreenSuggestedFare.
  ///
  /// In en, this message translates to:
  /// **'Suggested Fare:'**
  String get fareOfferScreenSuggestedFare;

  /// No description provided for @fareOfferScreenDistance.
  ///
  /// In en, this message translates to:
  /// **'Total distance:'**
  String get fareOfferScreenDistance;

  /// No description provided for @fareOfferMaximumFareNotice.
  ///
  /// In en, this message translates to:
  /// **'Maximum fare is {fare}'**
  String fareOfferMaximumFareNotice(Object fare);

  /// No description provided for @fareOfferScreenScheduleTime.
  ///
  /// In en, this message translates to:
  /// **'Schedule Time'**
  String get fareOfferScreenScheduleTime;

  /// No description provided for @fareOfferScreenDone.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get fareOfferScreenDone;

  /// No description provided for @fareOfferScreenEnterFareError.
  ///
  /// In en, this message translates to:
  /// **'Please enter a fare'**
  String get fareOfferScreenEnterFareError;

  /// No description provided for @fareOfferScreenInvalidFareError.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid number'**
  String get fareOfferScreenInvalidFareError;

  /// No description provided for @fareOfferScreenNegativeFareError.
  ///
  /// In en, this message translates to:
  /// **'Fare cannot be negative'**
  String get fareOfferScreenNegativeFareError;

  /// No description provided for @fareOfferScreenMinimumFareError.
  ///
  /// In en, this message translates to:
  /// **'Minimum Fare rate is {minFare}'**
  String fareOfferScreenMinimumFareError(Object minFare);

  /// No description provided for @shareBookingModeTitle.
  ///
  /// In en, this message translates to:
  /// **'Share Booking Mode'**
  String get shareBookingModeTitle;

  /// No description provided for @scheduleTripTitle.
  ///
  /// In en, this message translates to:
  /// **'Schedule your trip'**
  String get scheduleTripTitle;

  /// No description provided for @schedulePickupDate.
  ///
  /// In en, this message translates to:
  /// **'Pickup Date'**
  String get schedulePickupDate;

  /// No description provided for @schedulePickupTime.
  ///
  /// In en, this message translates to:
  /// **'Pickup Time'**
  String get schedulePickupTime;

  /// No description provided for @selectPickupDate.
  ///
  /// In en, this message translates to:
  /// **'Select Pickup Date'**
  String get selectPickupDate;

  /// No description provided for @selectPickupTime.
  ///
  /// In en, this message translates to:
  /// **'Select Pickup Time'**
  String get selectPickupTime;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @continueBtn.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueBtn;

  /// No description provided for @rideRequest_raiseFare.
  ///
  /// In en, this message translates to:
  /// **'Raise Fare'**
  String get rideRequest_raiseFare;

  /// No description provided for @rideRequest_raisedFareToast.
  ///
  /// In en, this message translates to:
  /// **'You raised the fare to NPR {fare}'**
  String rideRequest_raisedFareToast(Object fare);

  /// No description provided for @rideRequest_payment.
  ///
  /// In en, this message translates to:
  /// **'Payment'**
  String get rideRequest_payment;

  /// No description provided for @rideRequest_yourRide.
  ///
  /// In en, this message translates to:
  /// **'Your Current Ride'**
  String get rideRequest_yourRide;

  /// No description provided for @rideRequest_pickup.
  ///
  /// In en, this message translates to:
  /// **'Pickup'**
  String get rideRequest_pickup;

  /// No description provided for @rideRequest_destination.
  ///
  /// In en, this message translates to:
  /// **'Destination'**
  String get rideRequest_destination;

  /// No description provided for @rideRequest_cancelRequest.
  ///
  /// In en, this message translates to:
  /// **'Cancel Request'**
  String get rideRequest_cancelRequest;

  /// No description provided for @rideRequest_cancelSuccess.
  ///
  /// In en, this message translates to:
  /// **'Request Cancel successful!!'**
  String get rideRequest_cancelSuccess;

  /// No description provided for @rideRequest_searchingMessages.
  ///
  /// In en, this message translates to:
  /// **'Searching for the best nearby driver...'**
  String get rideRequest_searchingMessages;

  /// No description provided for @rideRequest_searchingMessages1.
  ///
  /// In en, this message translates to:
  /// **'Hang tight! We\'re finding your ride.'**
  String get rideRequest_searchingMessages1;

  /// No description provided for @rideRequest_searchingMessages2.
  ///
  /// In en, this message translates to:
  /// **'Almost there... getting a driver for you.'**
  String get rideRequest_searchingMessages2;

  /// No description provided for @rideRequest_searchingMessages3.
  ///
  /// In en, this message translates to:
  /// **'Your comfort ride is just a tap away 🚗💨'**
  String get rideRequest_searchingMessages3;

  /// No description provided for @rideRequest_searchingMessages4.
  ///
  /// In en, this message translates to:
  /// **'Making sure the driver gets your location📍'**
  String get rideRequest_searchingMessages4;

  /// No description provided for @documentReviewAppBarTitle.
  ///
  /// In en, this message translates to:
  /// **'Document Review'**
  String get documentReviewAppBarTitle;

  /// No description provided for @documentReviewLoadFailureTitle.
  ///
  /// In en, this message translates to:
  /// **'Failed to load profile'**
  String get documentReviewLoadFailureTitle;

  /// No description provided for @documentReviewRetryButton.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get documentReviewRetryButton;

  /// No description provided for @documentReviewBackToHome.
  ///
  /// In en, this message translates to:
  /// **'Back to Home'**
  String get documentReviewBackToHome;

  /// No description provided for @documentReviewStatusUnderReview.
  ///
  /// In en, this message translates to:
  /// **'Under Review'**
  String get documentReviewStatusUnderReview;

  /// No description provided for @documentReviewStatusCardTitle.
  ///
  /// In en, this message translates to:
  /// **'Current Status'**
  String get documentReviewStatusCardTitle;

  /// No description provided for @documentReviewStatusSubmittedTitle.
  ///
  /// In en, this message translates to:
  /// **'Documents Submitted'**
  String get documentReviewStatusSubmittedTitle;

  /// No description provided for @documentReviewStatusSubmittedSubtitle.
  ///
  /// In en, this message translates to:
  /// **'All required documents received'**
  String get documentReviewStatusSubmittedSubtitle;

  /// No description provided for @documentReviewStatusCheckTitle.
  ///
  /// In en, this message translates to:
  /// **'Background Check'**
  String get documentReviewStatusCheckTitle;

  /// No description provided for @documentReviewStatusCheckSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Currently in progress'**
  String get documentReviewStatusCheckSubtitle;

  /// No description provided for @documentReviewStatusFinalTitle.
  ///
  /// In en, this message translates to:
  /// **'Final Approval'**
  String get documentReviewStatusFinalTitle;

  /// No description provided for @documentReviewStatusFinalSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Pending background check completion'**
  String get documentReviewStatusFinalSubtitle;

  /// No description provided for @documentReviewReviewDetailsTitle.
  ///
  /// In en, this message translates to:
  /// **'Review Details'**
  String get documentReviewReviewDetailsTitle;

  /// No description provided for @documentReviewDetailEstimated.
  ///
  /// In en, this message translates to:
  /// **'Estimated completion'**
  String get documentReviewDetailEstimated;

  /// No description provided for @documentReviewDetailNotification.
  ///
  /// In en, this message translates to:
  /// **'Notification method'**
  String get documentReviewDetailNotification;

  /// No description provided for @documentReviewDetailNoteTitle.
  ///
  /// In en, this message translates to:
  /// **'What\'s next?'**
  String get documentReviewDetailNoteTitle;

  /// No description provided for @documentReviewDetailNoteContent.
  ///
  /// In en, this message translates to:
  /// **'You\'ll receive an email notification once the review is complete. Make sure to check your spam folder and keep your phone nearby for SMS updates.'**
  String get documentReviewDetailNoteContent;

  /// No description provided for @enterRouteTitle.
  ///
  /// In en, this message translates to:
  /// **'Enter your route'**
  String get enterRouteTitle;

  /// No description provided for @chooseOnMap.
  ///
  /// In en, this message translates to:
  /// **'Choose on map'**
  String get chooseOnMap;

  /// No description provided for @continueButton.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueButton;

  /// No description provided for @meters.
  ///
  /// In en, this message translates to:
  /// **'meter'**
  String get meters;

  /// No description provided for @kilometers.
  ///
  /// In en, this message translates to:
  /// **'km'**
  String get kilometers;

  /// No description provided for @ratingPageTitleDriver.
  ///
  /// In en, this message translates to:
  /// **'Rate Your Driver'**
  String get ratingPageTitleDriver;

  /// No description provided for @ratingPageTitlePassenger.
  ///
  /// In en, this message translates to:
  /// **'Rate Your Passenger'**
  String get ratingPageTitlePassenger;

  /// No description provided for @ratingPageQuestion.
  ///
  /// In en, this message translates to:
  /// **'How was your experience with {name}?'**
  String ratingPageQuestion(Object name);

  /// No description provided for @ratingPageCompletedTrip.
  ///
  /// In en, this message translates to:
  /// **'Trip completed'**
  String get ratingPageCompletedTrip;

  /// No description provided for @ratingPageRateLabelDriver.
  ///
  /// In en, this message translates to:
  /// **'Rate this driver'**
  String get ratingPageRateLabelDriver;

  /// No description provided for @ratingPageRateLabelPassenger.
  ///
  /// In en, this message translates to:
  /// **'Rate this passenger'**
  String get ratingPageRateLabelPassenger;

  /// No description provided for @ratingPageTapToRate.
  ///
  /// In en, this message translates to:
  /// **'Tap to rate'**
  String get ratingPageTapToRate;

  /// No description provided for @ratingPageQuickFeedback.
  ///
  /// In en, this message translates to:
  /// **'Quick Feedback (Optional)'**
  String get ratingPageQuickFeedback;

  /// No description provided for @ratingPageAdditionalComment.
  ///
  /// In en, this message translates to:
  /// **'Additional Comments (Optional)'**
  String get ratingPageAdditionalComment;

  /// No description provided for @ratingPageCommentHint.
  ///
  /// In en, this message translates to:
  /// **'Share your experience...'**
  String get ratingPageCommentHint;

  /// No description provided for @ratingPageButtonSkip.
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get ratingPageButtonSkip;

  /// No description provided for @ratingPageButtonSubmit.
  ///
  /// In en, this message translates to:
  /// **'Submit Rating'**
  String get ratingPageButtonSubmit;

  /// No description provided for @ratingPageSelectRatingError.
  ///
  /// In en, this message translates to:
  /// **'Please select a rating'**
  String get ratingPageSelectRatingError;

  /// No description provided for @ratingTextPoor.
  ///
  /// In en, this message translates to:
  /// **'Poor'**
  String get ratingTextPoor;

  /// No description provided for @ratingTextFair.
  ///
  /// In en, this message translates to:
  /// **'Fair'**
  String get ratingTextFair;

  /// No description provided for @ratingTextGood.
  ///
  /// In en, this message translates to:
  /// **'Good'**
  String get ratingTextGood;

  /// No description provided for @ratingTextVeryGood.
  ///
  /// In en, this message translates to:
  /// **'Very Good'**
  String get ratingTextVeryGood;

  /// No description provided for @ratingTextExcellent.
  ///
  /// In en, this message translates to:
  /// **'Excellent'**
  String get ratingTextExcellent;

  /// No description provided for @feedbackProfessional.
  ///
  /// In en, this message translates to:
  /// **'Professional'**
  String get feedbackProfessional;

  /// No description provided for @feedbackSafeDriving.
  ///
  /// In en, this message translates to:
  /// **'Safe Driving'**
  String get feedbackSafeDriving;

  /// No description provided for @feedbackOnTime.
  ///
  /// In en, this message translates to:
  /// **'On Time'**
  String get feedbackOnTime;

  /// No description provided for @feedbackFriendly.
  ///
  /// In en, this message translates to:
  /// **'Friendly'**
  String get feedbackFriendly;

  /// No description provided for @feedbackCleanVehicle.
  ///
  /// In en, this message translates to:
  /// **'Clean Vehicle'**
  String get feedbackCleanVehicle;

  /// No description provided for @feedbackIssue.
  ///
  /// In en, this message translates to:
  /// **'Issue'**
  String get feedbackIssue;

  /// No description provided for @feedbackPolite.
  ///
  /// In en, this message translates to:
  /// **'Polite'**
  String get feedbackPolite;

  /// No description provided for @feedbackEasyPickup.
  ///
  /// In en, this message translates to:
  /// **'Easy Pickup'**
  String get feedbackEasyPickup;

  /// No description provided for @feedbackRespectful.
  ///
  /// In en, this message translates to:
  /// **'Respectful'**
  String get feedbackRespectful;

  /// No description provided for @whatsYourNamePageTitle.
  ///
  /// In en, this message translates to:
  /// **'What is Your Name?'**
  String get whatsYourNamePageTitle;

  /// No description provided for @whatsYourNamePageSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Please enter your legal name so customers'**
  String get whatsYourNamePageSubtitle;

  /// No description provided for @fullNameLabel.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get fullNameLabel;

  /// No description provided for @fullNameValidation.
  ///
  /// In en, this message translates to:
  /// **'Please enter your full name'**
  String get fullNameValidation;

  /// No description provided for @nextButton.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get nextButton;

  /// No description provided for @contactInfoTitle.
  ///
  /// In en, this message translates to:
  /// **'Enter your contact information'**
  String get contactInfoTitle;

  /// No description provided for @emailLabel.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get emailLabel;

  /// No description provided for @phoneNumberLabel.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumberLabel;

  /// No description provided for @genderLabel.
  ///
  /// In en, this message translates to:
  /// **'Gender'**
  String get genderLabel;

  /// No description provided for @genderHint.
  ///
  /// In en, this message translates to:
  /// **'Select your gender'**
  String get genderHint;

  /// No description provided for @addressLabel.
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get addressLabel;

  /// No description provided for @addressValidation.
  ///
  /// In en, this message translates to:
  /// **'Please enter your address'**
  String get addressValidation;

  /// No description provided for @genderValidationToast.
  ///
  /// In en, this message translates to:
  /// **'Please select your gender.'**
  String get genderValidationToast;

  /// No description provided for @uploadYourDocuments.
  ///
  /// In en, this message translates to:
  /// **'Upload your Documents'**
  String get uploadYourDocuments;

  /// No description provided for @selectCitizenshipFront.
  ///
  /// In en, this message translates to:
  /// **'Select Citizenship Front'**
  String get selectCitizenshipFront;

  /// No description provided for @pleaseSelectFrontCitizenship.
  ///
  /// In en, this message translates to:
  /// **'Please select the front of your citizenship.'**
  String get pleaseSelectFrontCitizenship;

  /// No description provided for @selectCitizenshipBack.
  ///
  /// In en, this message translates to:
  /// **'Select Citizenship Back'**
  String get selectCitizenshipBack;

  /// No description provided for @pleaseSelectBackCitizenship.
  ///
  /// In en, this message translates to:
  /// **'Please select the back of your citizenship.'**
  String get pleaseSelectBackCitizenship;

  /// No description provided for @enterYourVehicleInformation.
  ///
  /// In en, this message translates to:
  /// **'Enter your Vehicle information'**
  String get enterYourVehicleInformation;

  /// No description provided for @vehicleNumber.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Number'**
  String get vehicleNumber;

  /// No description provided for @pleaseEnterVehicleNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter vehicle number'**
  String get pleaseEnterVehicleNumber;

  /// No description provided for @ownerName.
  ///
  /// In en, this message translates to:
  /// **'Owner Name'**
  String get ownerName;

  /// No description provided for @pleaseEnterOwnerName.
  ///
  /// In en, this message translates to:
  /// **'Please enter owner name'**
  String get pleaseEnterOwnerName;

  /// No description provided for @ownerPhone.
  ///
  /// In en, this message translates to:
  /// **'Owner Phone'**
  String get ownerPhone;

  /// No description provided for @pleaseEnterOwnerPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter owner phone number'**
  String get pleaseEnterOwnerPhoneNumber;

  /// No description provided for @uploadVehiclePhoto.
  ///
  /// In en, this message translates to:
  /// **'Upload Vehicle Photo'**
  String get uploadVehiclePhoto;

  /// No description provided for @uploadBillBookPhoto.
  ///
  /// In en, this message translates to:
  /// **'Upload Bill Book Photo'**
  String get uploadBillBookPhoto;

  /// No description provided for @pleaseSelectVehicleType.
  ///
  /// In en, this message translates to:
  /// **'Please select a vehicle type.'**
  String get pleaseSelectVehicleType;

  /// No description provided for @pleaseUploadVehiclePhoto.
  ///
  /// In en, this message translates to:
  /// **'Please upload a vehicle photo.'**
  String get pleaseUploadVehiclePhoto;

  /// No description provided for @pleaseUploadBlueBookPhoto.
  ///
  /// In en, this message translates to:
  /// **'Please upload a blue book photo.'**
  String get pleaseUploadBlueBookPhoto;

  /// No description provided for @uploadYourSelfie.
  ///
  /// In en, this message translates to:
  /// **'Upload your Selfie'**
  String get uploadYourSelfie;

  /// No description provided for @selectIdentityPhotoSelfie.
  ///
  /// In en, this message translates to:
  /// **'Select Identity Photo (Selfie)'**
  String get selectIdentityPhotoSelfie;

  /// No description provided for @pleaseUploadYourSelfieFirst.
  ///
  /// In en, this message translates to:
  /// **'Please upload your selfie first.'**
  String get pleaseUploadYourSelfieFirst;

  /// No description provided for @driverHomePageNoPassengers.
  ///
  /// In en, this message translates to:
  /// **'No Passengers'**
  String get driverHomePageNoPassengers;

  /// No description provided for @currentRides.
  ///
  /// In en, this message translates to:
  /// **'Current Rides'**
  String get currentRides;

  /// No description provided for @currentBooking.
  ///
  /// In en, this message translates to:
  /// **'Current Bookings'**
  String get currentBooking;

  /// No description provided for @simpleBookingCardDistance.
  ///
  /// In en, this message translates to:
  /// **'Distance'**
  String get simpleBookingCardDistance;

  /// No description provided for @simpleBookingCardFare.
  ///
  /// In en, this message translates to:
  /// **'Fare'**
  String get simpleBookingCardFare;

  /// No description provided for @statusToggleButtonOnline.
  ///
  /// In en, this message translates to:
  /// **'Online'**
  String get statusToggleButtonOnline;

  /// No description provided for @statusToggleButtonOffline.
  ///
  /// In en, this message translates to:
  /// **'Offline'**
  String get statusToggleButtonOffline;

  /// No description provided for @rideTrackingPageForDriverBookingCancelled.
  ///
  /// In en, this message translates to:
  /// **'Booking cancelled'**
  String get rideTrackingPageForDriverBookingCancelled;

  /// No description provided for @rideTrackingPageForDriverReason.
  ///
  /// In en, this message translates to:
  /// **'Reason'**
  String get rideTrackingPageForDriverReason;

  /// No description provided for @rideTrackingPageForDriverNavigate.
  ///
  /// In en, this message translates to:
  /// **'Navigate'**
  String get rideTrackingPageForDriverNavigate;

  /// No description provided for @rideTrackingPageForDriverNoPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'No phone number available'**
  String get rideTrackingPageForDriverNoPhoneNumber;

  /// No description provided for @rideTrackingPageForDriverImHere.
  ///
  /// In en, this message translates to:
  /// **'I\'m here'**
  String get rideTrackingPageForDriverImHere;

  /// No description provided for @rideTrackingPageForDriverStartRide.
  ///
  /// In en, this message translates to:
  /// **'Start Ride'**
  String get rideTrackingPageForDriverStartRide;

  /// No description provided for @rideTrackingPageForDriverCompleteRide.
  ///
  /// In en, this message translates to:
  /// **'Complete Ride'**
  String get rideTrackingPageForDriverCompleteRide;

  /// No description provided for @rideTrackingPageForDriverRideCompleted.
  ///
  /// In en, this message translates to:
  /// **'Ride Completed'**
  String get rideTrackingPageForDriverRideCompleted;

  /// No description provided for @confirmationCodeDialogEnterConfirmationCode.
  ///
  /// In en, this message translates to:
  /// **'Enter Confirmation Code'**
  String get confirmationCodeDialogEnterConfirmationCode;

  /// No description provided for @confirmationCodeDialogSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Please enter the 6-digit code provided by the passenger'**
  String get confirmationCodeDialogSubtitle;

  /// No description provided for @confirmationCodeDialogHint.
  ///
  /// In en, this message translates to:
  /// **'123456'**
  String get confirmationCodeDialogHint;

  /// No description provided for @confirmationCodeDialogCancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get confirmationCodeDialogCancel;

  /// No description provided for @confirmationCodeDialogStartRide.
  ///
  /// In en, this message translates to:
  /// **'Start Ride'**
  String get confirmationCodeDialogStartRide;

  /// No description provided for @confirmationCodeDialogEmptyCodeError.
  ///
  /// In en, this message translates to:
  /// **'Please enter a confirmation code.'**
  String get confirmationCodeDialogEmptyCodeError;

  /// No description provided for @tripTabBarCurrentBooking.
  ///
  /// In en, this message translates to:
  /// **'Current Booking'**
  String get tripTabBarCurrentBooking;

  /// No description provided for @tripTabBarActiveBooking.
  ///
  /// In en, this message translates to:
  /// **'Active Booking'**
  String get tripTabBarActiveBooking;

  /// No description provided for @tripBookingCartDate.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get tripBookingCartDate;

  /// No description provided for @tripBookingCartDistance.
  ///
  /// In en, this message translates to:
  /// **'Distance'**
  String get tripBookingCartDistance;

  /// No description provided for @tripBookingCartPassengers.
  ///
  /// In en, this message translates to:
  /// **'Passengers'**
  String get tripBookingCartPassengers;

  /// No description provided for @tripBookingCartFareAmount.
  ///
  /// In en, this message translates to:
  /// **'Fare Amount'**
  String get tripBookingCartFareAmount;

  /// No description provided for @tripBookingCartRider.
  ///
  /// In en, this message translates to:
  /// **'Rider'**
  String get tripBookingCartRider;

  /// No description provided for @tripBookingCartPassenger.
  ///
  /// In en, this message translates to:
  /// **'Passenger'**
  String get tripBookingCartPassenger;

  /// No description provided for @tripBookingCartPayment.
  ///
  /// In en, this message translates to:
  /// **'Payment'**
  String get tripBookingCartPayment;

  /// No description provided for @tripBookingCartBookingDetails.
  ///
  /// In en, this message translates to:
  /// **'Booking Details'**
  String get tripBookingCartBookingDetails;

  /// No description provided for @tripBookingCartPickUpLocation.
  ///
  /// In en, this message translates to:
  /// **'Pick up location'**
  String get tripBookingCartPickUpLocation;

  /// No description provided for @tripBookingCartDropOffLocation.
  ///
  /// In en, this message translates to:
  /// **'Drop off location'**
  String get tripBookingCartDropOffLocation;

  /// No description provided for @tripBookingCartTripInformation.
  ///
  /// In en, this message translates to:
  /// **'Trip Information'**
  String get tripBookingCartTripInformation;

  /// No description provided for @tripBookingCartBookingDate.
  ///
  /// In en, this message translates to:
  /// **'Booking Date'**
  String get tripBookingCartBookingDate;

  /// No description provided for @tripBookingCartStartDate.
  ///
  /// In en, this message translates to:
  /// **'Start Date'**
  String get tripBookingCartStartDate;

  /// No description provided for @tripBookingCartTripType.
  ///
  /// In en, this message translates to:
  /// **'Trip Type'**
  String get tripBookingCartTripType;

  /// No description provided for @tripBookingCartSharedRide.
  ///
  /// In en, this message translates to:
  /// **'Shared Ride'**
  String get tripBookingCartSharedRide;

  /// No description provided for @tripBookingCartPrivateRide.
  ///
  /// In en, this message translates to:
  /// **'Private Ride'**
  String get tripBookingCartPrivateRide;

  /// No description provided for @tripBookingCartPeople.
  ///
  /// In en, this message translates to:
  /// **'People'**
  String get tripBookingCartPeople;

  /// No description provided for @tripBookingCartTotalFare.
  ///
  /// In en, this message translates to:
  /// **'Total Fare'**
  String get tripBookingCartTotalFare;

  /// No description provided for @tripBookingCartAcceptedFare.
  ///
  /// In en, this message translates to:
  /// **'Accepted Fare'**
  String get tripBookingCartAcceptedFare;

  /// No description provided for @tripBookingCartPaymentStatus.
  ///
  /// In en, this message translates to:
  /// **'Payment Status'**
  String get tripBookingCartPaymentStatus;

  /// No description provided for @tripBookingCartCancellation.
  ///
  /// In en, this message translates to:
  /// **'Cancellation'**
  String get tripBookingCartCancellation;

  /// No description provided for @tripBookingCartCancelledDate.
  ///
  /// In en, this message translates to:
  /// **'Cancelled Date'**
  String get tripBookingCartCancelledDate;

  /// No description provided for @tripBookingCartCancelledBy.
  ///
  /// In en, this message translates to:
  /// **'Cancelled By'**
  String get tripBookingCartCancelledBy;

  /// No description provided for @tripBookingCartReason.
  ///
  /// In en, this message translates to:
  /// **'Reason'**
  String get tripBookingCartReason;

  /// No description provided for @tripBookingCartRouteDetails.
  ///
  /// In en, this message translates to:
  /// **'Route Details'**
  String get tripBookingCartRouteDetails;

  /// No description provided for @tripBookingCartPickUp.
  ///
  /// In en, this message translates to:
  /// **'Pick Up'**
  String get tripBookingCartPickUp;

  /// No description provided for @tripBookingCartDropOff.
  ///
  /// In en, this message translates to:
  /// **'Drop Off'**
  String get tripBookingCartDropOff;

  /// No description provided for @tripBookingCartMyTrips.
  ///
  /// In en, this message translates to:
  /// **'My Trips'**
  String get tripBookingCartMyTrips;

  /// No description provided for @profilePageSaveChanges.
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get profilePageSaveChanges;

  /// No description provided for @profilePageUploadingImage.
  ///
  /// In en, this message translates to:
  /// **'Uploading image...'**
  String get profilePageUploadingImage;

  /// No description provided for @profilePageProfilePictureUpdated.
  ///
  /// In en, this message translates to:
  /// **'Profile picture updated'**
  String get profilePageProfilePictureUpdated;

  /// No description provided for @profilePageProfileUpdated.
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully'**
  String get profilePageProfileUpdated;

  /// No description provided for @profilePageSetAddress.
  ///
  /// In en, this message translates to:
  /// **'Set Address'**
  String get profilePageSetAddress;

  /// No description provided for @profilePagePersonalInformation.
  ///
  /// In en, this message translates to:
  /// **'Personal Information'**
  String get profilePagePersonalInformation;

  /// No description provided for @profilePageFullName.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get profilePageFullName;

  /// No description provided for @profilePageEmailAddress.
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get profilePageEmailAddress;

  /// No description provided for @profilePageGender.
  ///
  /// In en, this message translates to:
  /// **'Gender'**
  String get profilePageGender;

  /// No description provided for @profilePageCurrentAddress.
  ///
  /// In en, this message translates to:
  /// **'Current Address'**
  String get profilePageCurrentAddress;

  /// No description provided for @profilePageAccountStatus.
  ///
  /// In en, this message translates to:
  /// **'Account Status'**
  String get profilePageAccountStatus;

  /// No description provided for @profilePageUserType.
  ///
  /// In en, this message translates to:
  /// **'User Type'**
  String get profilePageUserType;

  /// No description provided for @profilePageLoginStatus.
  ///
  /// In en, this message translates to:
  /// **'Login Status'**
  String get profilePageLoginStatus;

  /// No description provided for @profilePageMemberSince.
  ///
  /// In en, this message translates to:
  /// **'Member Since'**
  String get profilePageMemberSince;

  /// No description provided for @profilePageAccountActivated.
  ///
  /// In en, this message translates to:
  /// **'Account Activated'**
  String get profilePageAccountActivated;

  /// No description provided for @profilePageLocationSettings.
  ///
  /// In en, this message translates to:
  /// **'Location Settings'**
  String get profilePageLocationSettings;

  /// No description provided for @profilePageHome.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get profilePageHome;

  /// No description provided for @profilePageWork.
  ///
  /// In en, this message translates to:
  /// **'Work'**
  String get profilePageWork;

  /// No description provided for @profilePageHomeLocation.
  ///
  /// In en, this message translates to:
  /// **'Home Location'**
  String get profilePageHomeLocation;

  /// No description provided for @profilePageCurrentLocation.
  ///
  /// In en, this message translates to:
  /// **'Current Location'**
  String get profilePageCurrentLocation;

  /// No description provided for @profilePageThisFeatureNotAvailable.
  ///
  /// In en, this message translates to:
  /// **'This feature is currently not available'**
  String get profilePageThisFeatureNotAvailable;

  /// No description provided for @viewProfileDialogEditProfile.
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get viewProfileDialogEditProfile;

  /// No description provided for @viewProfileDialogUpdateInfo.
  ///
  /// In en, this message translates to:
  /// **'Update your personal information'**
  String get viewProfileDialogUpdateInfo;

  /// No description provided for @viewProfileDialogLogout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get viewProfileDialogLogout;

  /// No description provided for @viewProfileDialogLogoutSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Sign out of your account'**
  String get viewProfileDialogLogoutSubtitle;

  /// No description provided for @viewProfileDialogCancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get viewProfileDialogCancel;

  /// No description provided for @pickupHint.
  ///
  /// In en, this message translates to:
  /// **'Pickup location'**
  String get pickupHint;

  /// No description provided for @destinationHint.
  ///
  /// In en, this message translates to:
  /// **'To'**
  String get destinationHint;

  /// No description provided for @serviceStatusBooked.
  ///
  /// In en, this message translates to:
  /// **'Booked'**
  String get serviceStatusBooked;

  /// No description provided for @serviceStatusStarted.
  ///
  /// In en, this message translates to:
  /// **'Started'**
  String get serviceStatusStarted;

  /// No description provided for @serviceStatusCompleted.
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get serviceStatusCompleted;

  /// No description provided for @serviceStatusCancelled.
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get serviceStatusCancelled;

  /// No description provided for @searchRadiusTitle.
  ///
  /// In en, this message translates to:
  /// **'Search Radius'**
  String get searchRadiusTitle;

  /// No description provided for @searchRadiusSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Select how far you want to find drivers nearby'**
  String get searchRadiusSubtitle;

  /// No description provided for @fareTooLowMessage.
  ///
  /// In en, this message translates to:
  /// **'Your offer (NRP {fare}) may be too low. Try increasing the fare to attract drivers.'**
  String fareTooLowMessage(Object fare);

  /// No description provided for @noDriversFoundMessage.
  ///
  /// In en, this message translates to:
  /// **'No nearby drivers found yet. We’re expanding your request to reach more drivers.'**
  String get noDriversFoundMessage;

  /// No description provided for @statusLookingForDrivers.
  ///
  /// In en, this message translates to:
  /// **'Looking for nearby drivers'**
  String get statusLookingForDrivers;

  /// No description provided for @statusSendingOffer.
  ///
  /// In en, this message translates to:
  /// **'Sending your offer to drivers'**
  String get statusSendingOffer;

  /// No description provided for @statusWaitingForResponse.
  ///
  /// In en, this message translates to:
  /// **'Waiting for drivers to respond'**
  String get statusWaitingForResponse;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'ne'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'ne': return AppLocalizationsNe();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
