// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appname => 'Safari Yatri';

  @override
  String get welcomeback => 'Welcome Back';

  @override
  String get signintocontinue => 'Sign in to continue';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get signin => 'Sign In';

  @override
  String get signup => 'Sign Up';

  @override
  String get donthaveaccount => 'Don\'t have an account? Sign Up';

  @override
  String get selectedLanguage => 'Select Language';

  @override
  String get chooseLater => 'You can change language later from settings';

  @override
  String get next => 'Next';

  @override
  String get skip => 'Skip';

  @override
  String get done => 'Done';

  @override
  String get loginScreenRememberMeLabel => 'Remember Me';

  @override
  String get onBoardingfirstScreenTitle => 'One app for all services.';

  @override
  String get onBoardingSecondScreenTitle => 'Get there, on time';

  @override
  String get onBoardingThirdScreenTitle => 'Pay, as you want.';

  @override
  String get onBoardingfirstScreenDesc => 'Get a ride with your finertips.';

  @override
  String get onBoardingSecondScreenDesc => 'Best the traffic and reach your destination fast, every time.';

  @override
  String get onBoardingThirdScreenDesc => 'Cash? Card? Wallet? We accept it all. Let\'s get started.';

  @override
  String get welcomeText => 'Welcome';

  @override
  String get welcomeDescription => 'Have a better sharing exprience';

  @override
  String get locationPermissionTitle => 'Enable Location Permission';

  @override
  String get locationPermissionDescription => 'To get great service you need to provide location permission. You Can always change permission from settings.';

  @override
  String get enableLocation => 'Enable Location';

  @override
  String get locationEnableAccess => 'Enable Location Access';

  @override
  String get locationRequestingPermission => 'Requesting Permission...';

  @override
  String get locationAccessGranted => 'Location Access Granted';

  @override
  String get locationNeeded => 'Location Needed';

  @override
  String get locationDescription => 'This app needs your location to connect riders with drivers and help passengers track trips in real time.';

  @override
  String get locationWaitingConfirmation => 'Waiting for your confirmation in the permission dialog.';

  @override
  String get locationRedirecting => 'Great! We\'ve got your location. Redirecting you now…';

  @override
  String get locationDenied => 'You denied location access. Tap below to try again.';

  @override
  String get locationPermanentlyDenied => 'Location permanently denied. Please open settings to allow access.';

  @override
  String get locationRequired => 'Location permission is required to continue.';

  @override
  String get locationEnableButton => 'Enable Location';

  @override
  String get locationOpenSettings => 'Open App Settings';

  @override
  String get locationTryAgain => 'Try Again';

  @override
  String get locationGoToSettings => 'Go to Settings';

  @override
  String get locationAllowAccess => 'Allow Location Access';

  @override
  String get locationPermissionHeader => 'Location Permission Required';

  @override
  String get locationServiceDisabled => 'Location Services Disabled';

  @override
  String get locationServiceDisabledMessage => 'Location services are turned off on your device. Please enable them to continue.';

  @override
  String get openLocationSettings => 'Open Location Settings';

  @override
  String get showCaseDrawerDescription => 'View your profile and settings';

  @override
  String get showCasePassengerCoutDescription => 'Select the number of passengers';

  @override
  String get showCasePickUpLocationDescription => 'Select your pickup location';

  @override
  String get showCaseDestinationLocationDescription => 'Select your destination location';

  @override
  String get showCaseFindRiderDescription => 'Find available riders';

  @override
  String get numberOfPassenger => 'Select the number of passengers for your trip';

  @override
  String get buttonTitle => 'Find Rider';

  @override
  String get passengerHomeBottomSheetDestination => 'Your Destinations';

  @override
  String get passengerHomeDirectionErrorText => 'Direction Route is null';

  @override
  String get loginScreenAppHeaderTitle => 'LogIn your Account';

  @override
  String get loginScreenAppHeaderSubTitle => 'Please enter your details to continue';

  @override
  String get loginScreenPhoneNumberFormLabel => 'Phone Number';

  @override
  String get loginScreenPasswordFormLabel => 'Password';

  @override
  String get loginScreenForgetPasswordBtnTextLabel => 'Forget Password?';

  @override
  String get loginScreenSucessToastLabel => 'Login Successfull';

  @override
  String get loginScreenErrorToastLoginNotActiveLabel => 'Please verify your number.Login not active yet.';

  @override
  String get loginScreenButtonTitle => 'Login';

  @override
  String get loginScreenDontHaveAnAccount => 'Don\'t have an account?';

  @override
  String get loginScreenSignUpButtonTitle => 'Sign Up';

  @override
  String get footerWidgetTextSpanByContinue => 'By continuing, you agree to our ';

  @override
  String get footerWidgetTextSpanTermsOfServices => 'Terms of Service';

  @override
  String get footerWidgetTextSpanAnd => ' and ';

  @override
  String get footerWidgetTextSpanPrivacyPolicy => 'Privacy Policy';

  @override
  String get signUpScreenAppHeaderTitle => 'Create your Account';

  @override
  String get signUpScreenAppHeaderSubTitle => 'Please enter your details to sign up';

  @override
  String get signUpScreenFullNameLabel => 'Full Name';

  @override
  String get signUpScreenPhoneNumberLabel => 'Phone Number';

  @override
  String get signUpScreenEmailAddressLabel => 'Email Address';

  @override
  String get signUpScreenCurrentAddressLabel => 'Current Address';

  @override
  String get signUpScreenGenderLabel => 'Gender';

  @override
  String get signUpScreenSignUpButtonTitle => 'Sign Up';

  @override
  String get signUpScreenAlreadyHaveAccountText => 'Already have an account?';

  @override
  String get signUpScreenSignInText => 'Sign In';

  @override
  String get verifyOtpScreenAppHeaderTitle => 'Verify OTP';

  @override
  String get verifyOtpScreenAppHeaderSubTitle => 'Please enter the 4-digit OTP sent to your number.';

  @override
  String get verifyOtpScreenResendButtonText => 'Resend OTP';

  @override
  String verifyOtpScreenResendCountdownText(Object _secondsRemaining) {
    return 'Resend available in $_secondsRemaining seconds';
  }

  @override
  String get verifyOtpScreenResendSuccessToast => 'OTP resent successfully';

  @override
  String get verifyOtpScreenResendFailedToast => 'Failed to resend OTP';

  @override
  String get verifyOtpScreenOtpVerifySuccessToast => 'OTP verification successful';

  @override
  String get verifyOtpScreenOtpVerifyFailedToast => 'Incorrect OTP. Please try again.';

  @override
  String get verifyOtpScreenPleaseLoginToast => 'Please, login with your credentials.';

  @override
  String get driverDrawerScreenHomeTitle => 'Home';

  @override
  String get driverDrawerScreenMyTripsTitle => 'My Trips';

  @override
  String get driverDrawerScreenProfileTitle => 'Profile';

  @override
  String get drawerScreenSettingsTitle => 'Settings';

  @override
  String get drawerScreenSettingsTheme => 'Theme Mode';

  @override
  String get drawerScreenSettingsThemeSystem => 'System';

  @override
  String get drawerScreenSettingsThemeDark => 'Dark';

  @override
  String get drawerScreenSettingsThemeLight => 'Light';

  @override
  String get drawerScreenSettingsSelectLanguage => 'Select Language';

  @override
  String get drawerScreenSettingsChangePasswordAppBarTitle => 'Change Password';

  @override
  String get drawerScreenSettingsChangePasswordOld => 'Old Password';

  @override
  String get drawerScreenSettingsChangePasswordNew => 'New Password';

  @override
  String get drawerScreenSettingsChangePasswordConfirm => 'Confirm Password';

  @override
  String get drawerScreenSettingsChangePasswordButton => 'Update Password';

  @override
  String get drawerScreenSettingsChangePasswordValidationRequired => 'This field is required';

  @override
  String get drawerScreenSettingsChangePasswordValidationNewRequired => 'New password is required';

  @override
  String get drawerScreenSettingsChangePasswordValidationMinLength => 'Password must be at least 6 characters long';

  @override
  String get drawerScreenSettingsChangePasswordValidationConfirmRequired => 'Confirm password is required';

  @override
  String get drawerScreenSettingsChangePasswordValidationNotMatch => 'Passwords do not match';

  @override
  String get drawerScreenEmergencySafetyTitle => 'Emergency & Safety';

  @override
  String get driverDrawerScreenPassengerModeButton => 'Passenger Mode';

  @override
  String get emergencyScreenAppBarTitle => 'Emergency & Safety';

  @override
  String get emergencyScreenSupportButton => 'Support';

  @override
  String get emergencyScreenEmergencyContactsButton => 'Emergency contacts';

  @override
  String get emergencyScreenCall100Button => 'Call 100';

  @override
  String get emergencyScreenHowYoureProtectedTitle => 'How you\'re protected';

  @override
  String get emergencyScreenFeatureProactiveSafetySupport => 'Proactive safety support';

  @override
  String get emergencyScreenFeatureDriversVerification => 'Drivers verification';

  @override
  String get emergencyScreenFeatureProtectingPrivacy => 'Protecting your privacy';

  @override
  String get emergencyScreenFeatureStayingSafe => 'Staying safe on every ride';

  @override
  String get emergencyScreenFeatureAccidentsSteps => 'Accidents: Steps to take';

  @override
  String get notificationScreenAppBarTitle => 'Notification Preferences';

  @override
  String get notificationScreenRideRequestsTitle => 'Ride Requests';

  @override
  String get notificationScreenRideRequestsSubtitle => 'Get notified about new ride requests';

  @override
  String get notificationScreenPromotionsTitle => 'Promotions';

  @override
  String get notificationScreenPromotionsSubtitle => 'Receive promotional offers and bonuses';

  @override
  String get notificationScreenEarningsTitle => 'Earnings';

  @override
  String get notificationScreenEarningsSubtitle => 'Daily and weekly earnings summaries';

  @override
  String get notificationScreenSafetyTitle => 'Safety';

  @override
  String get notificationScreenSafetySubtitle => 'Safety alerts and emergency notifications';

  @override
  String get passengerDrawerScreenHomeTitle => 'Home';

  @override
  String get passengerDrawerScreenYourTripsTitle => 'Your Trips';

  @override
  String get passengerDrawerScreenProfileTitle => 'Profile';

  @override
  String get passengerDrawerScreenSettingsTitle => 'Settings';

  @override
  String get passengerDrawerScreenEmergencySafetyTitle => 'Emergency & Safety';

  @override
  String get passengerDrawerScreenDriverModeButton => 'Driver Mode';

  @override
  String get settingScreenAppBarTitle => 'Settings';

  @override
  String get settingScreenLanguageTitle => 'Language';

  @override
  String get settingScreenLanguageSubtitle => 'English';

  @override
  String get settingScreenChangePasswordTitle => 'Change Password';

  @override
  String get settingScreenNotificationTitle => 'Notification';

  @override
  String get settingScreenRulesAndTermsTitle => 'Rules and terms';

  @override
  String get settingScreenLogoutTitle => 'Logout';

  @override
  String get settingScreenDeleteAccountTitle => 'Delete Account';

  @override
  String get settingScreenDeleteDialogTitle => 'Delete Account';

  @override
  String get settingScreenDeleteDialogMessage => 'This action will permanently delete your account and all data associated with it. Are you sure you want to proceed?';

  @override
  String get settingScreenDeleteDialogConfirmText => 'Delete Account';

  @override
  String get settingScreenDeleteDialogCancelText => 'Cancel';

  @override
  String get appVersionWidgetLoading => 'Loading version...';

  @override
  String get appVersionWidgetUnavailable => 'Version unavailable';

  @override
  String appVersionWidgetFormat(Object buildNumber, Object version) {
    return 'Version $version+$buildNumber';
  }

  @override
  String get locationPermissionScreenTitle => 'Enable Location Permission';

  @override
  String get locationPermissionScreenDescription => 'To get great service you need to provide location permission. You can always change permission from settings.';

  @override
  String get locationPermissionScreenGivePermissionButton => 'Give Permissions';

  @override
  String get locationPermissionScreenDialogMessage => 'We require your precise location to seamlessly connect you to nearby services providers.\n\nPlease turn on device location.';

  @override
  String get locationPermissionScreenDialogGivePermission => 'Give Location Permission';

  @override
  String get locationPermissionScreenDialogOpenSettings => 'Open Settings';

  @override
  String get fareOfferScreenTitle => 'Offer your fare';

  @override
  String get fareOfferScreenSuggestedFare => 'Suggested Fare:';

  @override
  String get fareOfferScreenDistance => 'Total distance:';

  @override
  String fareOfferMaximumFareNotice(Object fare) {
    return 'Maximum fare is $fare';
  }

  @override
  String get fareOfferScreenScheduleTime => 'Schedule Time';

  @override
  String get fareOfferScreenDone => 'Done';

  @override
  String get fareOfferScreenEnterFareError => 'Please enter a fare';

  @override
  String get fareOfferScreenInvalidFareError => 'Please enter a valid number';

  @override
  String get fareOfferScreenNegativeFareError => 'Fare cannot be negative';

  @override
  String fareOfferScreenMinimumFareError(Object minFare) {
    return 'Minimum Fare rate is $minFare';
  }

  @override
  String get shareBookingModeTitle => 'Share Booking Mode';

  @override
  String get scheduleTripTitle => 'Schedule your trip';

  @override
  String get schedulePickupDate => 'Pickup Date';

  @override
  String get schedulePickupTime => 'Pickup Time';

  @override
  String get selectPickupDate => 'Select Pickup Date';

  @override
  String get selectPickupTime => 'Select Pickup Time';

  @override
  String get confirm => 'Confirm';

  @override
  String get continueBtn => 'Continue';

  @override
  String get rideRequest_raiseFare => 'Raise Fare';

  @override
  String rideRequest_raisedFareToast(Object fare) {
    return 'You raised the fare to NPR $fare';
  }

  @override
  String get rideRequest_payment => 'Payment';

  @override
  String get rideRequest_yourRide => 'Your Current Ride';

  @override
  String get rideRequest_pickup => 'Pickup';

  @override
  String get rideRequest_destination => 'Destination';

  @override
  String get rideRequest_cancelRequest => 'Cancel Request';

  @override
  String get rideRequest_cancelSuccess => 'Request Cancel successful!!';

  @override
  String get rideRequest_searchingMessages => 'Searching for the best nearby driver...';

  @override
  String get rideRequest_searchingMessages1 => 'Hang tight! We\'re finding your ride.';

  @override
  String get rideRequest_searchingMessages2 => 'Almost there... getting a driver for you.';

  @override
  String get rideRequest_searchingMessages3 => 'Your comfort ride is just a tap away 🚗💨';

  @override
  String get rideRequest_searchingMessages4 => 'Making sure the driver gets your location📍';

  @override
  String get documentReviewAppBarTitle => 'Document Review';

  @override
  String get documentReviewLoadFailureTitle => 'Failed to load profile';

  @override
  String get documentReviewRetryButton => 'Retry';

  @override
  String get documentReviewBackToHome => 'Back to Home';

  @override
  String get documentReviewStatusUnderReview => 'Under Review';

  @override
  String get documentReviewStatusCardTitle => 'Current Status';

  @override
  String get documentReviewStatusSubmittedTitle => 'Documents Submitted';

  @override
  String get documentReviewStatusSubmittedSubtitle => 'All required documents received';

  @override
  String get documentReviewStatusCheckTitle => 'Background Check';

  @override
  String get documentReviewStatusCheckSubtitle => 'Currently in progress';

  @override
  String get documentReviewStatusFinalTitle => 'Final Approval';

  @override
  String get documentReviewStatusFinalSubtitle => 'Pending background check completion';

  @override
  String get documentReviewReviewDetailsTitle => 'Review Details';

  @override
  String get documentReviewDetailEstimated => 'Estimated completion';

  @override
  String get documentReviewDetailNotification => 'Notification method';

  @override
  String get documentReviewDetailNoteTitle => 'What\'s next?';

  @override
  String get documentReviewDetailNoteContent => 'You\'ll receive an email notification once the review is complete. Make sure to check your spam folder and keep your phone nearby for SMS updates.';

  @override
  String get enterRouteTitle => 'Enter your route';

  @override
  String get chooseOnMap => 'Choose on map';

  @override
  String get continueButton => 'Continue';

  @override
  String get meters => 'meter';

  @override
  String get kilometers => 'km';

  @override
  String get ratingPageTitleDriver => 'Rate Your Driver';

  @override
  String get ratingPageTitlePassenger => 'Rate Your Passenger';

  @override
  String ratingPageQuestion(Object name) {
    return 'How was your experience with $name?';
  }

  @override
  String get ratingPageCompletedTrip => 'Trip completed';

  @override
  String get ratingPageRateLabelDriver => 'Rate this driver';

  @override
  String get ratingPageRateLabelPassenger => 'Rate this passenger';

  @override
  String get ratingPageTapToRate => 'Tap to rate';

  @override
  String get ratingPageQuickFeedback => 'Quick Feedback (Optional)';

  @override
  String get ratingPageAdditionalComment => 'Additional Comments (Optional)';

  @override
  String get ratingPageCommentHint => 'Share your experience...';

  @override
  String get ratingPageButtonSkip => 'Skip';

  @override
  String get ratingPageButtonSubmit => 'Submit Rating';

  @override
  String get ratingPageSelectRatingError => 'Please select a rating';

  @override
  String get ratingTextPoor => 'Poor';

  @override
  String get ratingTextFair => 'Fair';

  @override
  String get ratingTextGood => 'Good';

  @override
  String get ratingTextVeryGood => 'Very Good';

  @override
  String get ratingTextExcellent => 'Excellent';

  @override
  String get feedbackProfessional => 'Professional';

  @override
  String get feedbackSafeDriving => 'Safe Driving';

  @override
  String get feedbackOnTime => 'On Time';

  @override
  String get feedbackFriendly => 'Friendly';

  @override
  String get feedbackCleanVehicle => 'Clean Vehicle';

  @override
  String get feedbackIssue => 'Issue';

  @override
  String get feedbackPolite => 'Polite';

  @override
  String get feedbackEasyPickup => 'Easy Pickup';

  @override
  String get feedbackRespectful => 'Respectful';

  @override
  String get whatsYourNamePageTitle => 'What is Your Name?';

  @override
  String get whatsYourNamePageSubtitle => 'Please enter your legal name so customers';

  @override
  String get fullNameLabel => 'Full Name';

  @override
  String get fullNameValidation => 'Please enter your full name';

  @override
  String get nextButton => 'Next';

  @override
  String get contactInfoTitle => 'Enter your contact information';

  @override
  String get emailLabel => 'Email';

  @override
  String get phoneNumberLabel => 'Phone Number';

  @override
  String get genderLabel => 'Gender';

  @override
  String get genderHint => 'Select your gender';

  @override
  String get addressLabel => 'Address';

  @override
  String get addressValidation => 'Please enter your address';

  @override
  String get genderValidationToast => 'Please select your gender.';

  @override
  String get uploadYourDocuments => 'Upload your Documents';

  @override
  String get selectCitizenshipFront => 'Select Citizenship Front';

  @override
  String get pleaseSelectFrontCitizenship => 'Please select the front of your citizenship.';

  @override
  String get selectCitizenshipBack => 'Select Citizenship Back';

  @override
  String get pleaseSelectBackCitizenship => 'Please select the back of your citizenship.';

  @override
  String get enterYourVehicleInformation => 'Enter your Vehicle information';

  @override
  String get vehicleNumber => 'Vehicle Number';

  @override
  String get pleaseEnterVehicleNumber => 'Please enter vehicle number';

  @override
  String get ownerName => 'Owner Name';

  @override
  String get pleaseEnterOwnerName => 'Please enter owner name';

  @override
  String get ownerPhone => 'Owner Phone';

  @override
  String get pleaseEnterOwnerPhoneNumber => 'Please enter owner phone number';

  @override
  String get uploadVehiclePhoto => 'Upload Vehicle Photo';

  @override
  String get uploadBillBookPhoto => 'Upload Bill Book Photo';

  @override
  String get pleaseSelectVehicleType => 'Please select a vehicle type.';

  @override
  String get pleaseUploadVehiclePhoto => 'Please upload a vehicle photo.';

  @override
  String get pleaseUploadBlueBookPhoto => 'Please upload a blue book photo.';

  @override
  String get uploadYourSelfie => 'Upload your Selfie';

  @override
  String get selectIdentityPhotoSelfie => 'Select Identity Photo (Selfie)';

  @override
  String get pleaseUploadYourSelfieFirst => 'Please upload your selfie first.';

  @override
  String get driverHomePageNoPassengers => 'No Passengers';

  @override
  String get currentRides => 'Current Rides';

  @override
  String get currentBooking => 'Current Bookings';

  @override
  String get simpleBookingCardDistance => 'Distance';

  @override
  String get simpleBookingCardFare => 'Fare';

  @override
  String get statusToggleButtonOnline => 'Online';

  @override
  String get statusToggleButtonOffline => 'Offline';

  @override
  String get rideTrackingPageForDriverBookingCancelled => 'Booking cancelled';

  @override
  String get rideTrackingPageForDriverReason => 'Reason';

  @override
  String get rideTrackingPageForDriverNavigate => 'Navigate';

  @override
  String get rideTrackingPageForDriverNoPhoneNumber => 'No phone number available';

  @override
  String get rideTrackingPageForDriverImHere => 'I\'m here';

  @override
  String get rideTrackingPageForDriverStartRide => 'Start Ride';

  @override
  String get rideTrackingPageForDriverCompleteRide => 'Complete Ride';

  @override
  String get rideTrackingPageForDriverRideCompleted => 'Ride Completed';

  @override
  String get confirmationCodeDialogEnterConfirmationCode => 'Enter Confirmation Code';

  @override
  String get confirmationCodeDialogSubtitle => 'Please enter the 6-digit code provided by the passenger';

  @override
  String get confirmationCodeDialogHint => '123456';

  @override
  String get confirmationCodeDialogCancel => 'Cancel';

  @override
  String get confirmationCodeDialogStartRide => 'Start Ride';

  @override
  String get confirmationCodeDialogEmptyCodeError => 'Please enter a confirmation code.';

  @override
  String get tripTabBarCurrentBooking => 'Current Booking';

  @override
  String get tripTabBarActiveBooking => 'Active Booking';

  @override
  String get tripBookingCartDate => 'Date';

  @override
  String get tripBookingCartDistance => 'Distance';

  @override
  String get tripBookingCartPassengers => 'Passengers';

  @override
  String get tripBookingCartFareAmount => 'Fare Amount';

  @override
  String get tripBookingCartRider => 'Rider';

  @override
  String get tripBookingCartPassenger => 'Passenger';

  @override
  String get tripBookingCartPayment => 'Payment';

  @override
  String get tripBookingCartBookingDetails => 'Booking Details';

  @override
  String get tripBookingCartPickUpLocation => 'Pick up location';

  @override
  String get tripBookingCartDropOffLocation => 'Drop off location';

  @override
  String get tripBookingCartTripInformation => 'Trip Information';

  @override
  String get tripBookingCartBookingDate => 'Booking Date';

  @override
  String get tripBookingCartStartDate => 'Start Date';

  @override
  String get tripBookingCartTripType => 'Trip Type';

  @override
  String get tripBookingCartSharedRide => 'Shared Ride';

  @override
  String get tripBookingCartPrivateRide => 'Private Ride';

  @override
  String get tripBookingCartPeople => 'People';

  @override
  String get tripBookingCartTotalFare => 'Total Fare';

  @override
  String get tripBookingCartAcceptedFare => 'Accepted Fare';

  @override
  String get tripBookingCartPaymentStatus => 'Payment Status';

  @override
  String get tripBookingCartCancellation => 'Cancellation';

  @override
  String get tripBookingCartCancelledDate => 'Cancelled Date';

  @override
  String get tripBookingCartCancelledBy => 'Cancelled By';

  @override
  String get tripBookingCartReason => 'Reason';

  @override
  String get tripBookingCartRouteDetails => 'Route Details';

  @override
  String get tripBookingCartPickUp => 'Pick Up';

  @override
  String get tripBookingCartDropOff => 'Drop Off';

  @override
  String get tripBookingCartMyTrips => 'My Trips';

  @override
  String get profilePageSaveChanges => 'Save Changes';

  @override
  String get profilePageUploadingImage => 'Uploading image...';

  @override
  String get profilePageProfilePictureUpdated => 'Profile picture updated';

  @override
  String get profilePageProfileUpdated => 'Profile updated successfully';

  @override
  String get profilePageSetAddress => 'Set Address';

  @override
  String get profilePagePersonalInformation => 'Personal Information';

  @override
  String get profilePageFullName => 'Full Name';

  @override
  String get profilePageEmailAddress => 'Email Address';

  @override
  String get profilePageGender => 'Gender';

  @override
  String get profilePageCurrentAddress => 'Current Address';

  @override
  String get profilePageAccountStatus => 'Account Status';

  @override
  String get profilePageUserType => 'User Type';

  @override
  String get profilePageLoginStatus => 'Login Status';

  @override
  String get profilePageMemberSince => 'Member Since';

  @override
  String get profilePageAccountActivated => 'Account Activated';

  @override
  String get profilePageLocationSettings => 'Location Settings';

  @override
  String get profilePageHome => 'Home';

  @override
  String get profilePageWork => 'Work';

  @override
  String get profilePageHomeLocation => 'Home Location';

  @override
  String get profilePageCurrentLocation => 'Current Location';

  @override
  String get profilePageThisFeatureNotAvailable => 'This feature is currently not available';

  @override
  String get viewProfileDialogEditProfile => 'Edit Profile';

  @override
  String get viewProfileDialogUpdateInfo => 'Update your personal information';

  @override
  String get viewProfileDialogLogout => 'Logout';

  @override
  String get viewProfileDialogLogoutSubtitle => 'Sign out of your account';

  @override
  String get viewProfileDialogCancel => 'Cancel';

  @override
  String get pickupHint => 'Pickup location';

  @override
  String get destinationHint => 'To';

  @override
  String get serviceStatusBooked => 'Booked';

  @override
  String get serviceStatusStarted => 'Started';

  @override
  String get serviceStatusCompleted => 'Completed';

  @override
  String get serviceStatusCancelled => 'Cancelled';

  @override
  String get searchRadiusTitle => 'Search Radius';

  @override
  String get searchRadiusSubtitle => 'Select how far you want to find drivers nearby';

  @override
  String fareTooLowMessage(Object fare) {
    return 'Your offer (NRP $fare) may be too low. Try increasing the fare to attract drivers.';
  }

  @override
  String get noDriversFoundMessage => 'No nearby drivers found yet. We’re expanding your request to reach more drivers.';

  @override
  String get statusLookingForDrivers => 'Looking for nearby drivers';

  @override
  String get statusSendingOffer => 'Sending your offer to drivers';

  @override
  String get statusWaitingForResponse => 'Waiting for drivers to respond';
}
