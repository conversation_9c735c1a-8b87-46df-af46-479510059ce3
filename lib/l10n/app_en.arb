{"@@locale": "en", "appname": "Safari Yatri", "welcomeback": "Welcome Back", "signintocontinue": "Sign in to continue", "email": "Email", "password": "Password", "signin": "Sign In", "signup": "Sign Up", "donthaveaccount": "Don't have an account? Sign Up", "selectedLanguage": "Select Language", "chooseLater": "You can change language later from settings", "next": "Next", "skip": "<PERSON><PERSON>", "done": "Done", "loginScreenRememberMeLabel": "Remember Me", "onBoardingfirstScreenTitle": "One app for all services.", "onBoardingSecondScreenTitle": "Get there, on time", "onBoardingThirdScreenTitle": "Pay, as you want.", "onBoardingfirstScreenDesc": "Get a ride with your finertips.", "onBoardingSecondScreenDesc": "Best the traffic and reach your destination fast, every time.", "onBoardingThirdScreenDesc": "Cash? Card? Wallet? We accept it all. Let's get started.", "welcomeText": "Welcome", "welcomeDescription": "Have a better sharing exprience", "locationPermissionTitle": "Enable Location Permission", "locationPermissionDescription": "To get great service you need to provide location permission. You Can always change permission from settings.", "enableLocation": "Enable Location", "locationEnableAccess": "Enable Location Access", "locationRequestingPermission": "Requesting Permission...", "locationAccessGranted": "Location Access Granted", "locationNeeded": "Location Needed", "locationDescription": "This app needs your location to connect riders with drivers and help passengers track trips in real time.", "locationWaitingConfirmation": "Waiting for your confirmation in the permission dialog.", "locationRedirecting": "Great! We've got your location. Redirecting you now…", "locationDenied": "You denied location access. Tap below to try again.", "locationPermanentlyDenied": "Location permanently denied. Please open settings to allow access.", "locationRequired": "Location permission is required to continue.", "locationEnableButton": "Enable Location", "locationOpenSettings": "Open App Settings", "locationTryAgain": "Try Again", "locationGoToSettings": "Go to Settings", "locationAllowAccess": "Allow Location Access", "locationPermissionHeader": "Location Permission Required", "locationServiceDisabled": "Location Services Disabled", "locationServiceDisabledMessage": "Location services are turned off on your device. Please enable them to continue.", "openLocationSettings": "Open Location Settings", "showCaseDrawerDescription": "View your profile and settings", "showCasePassengerCoutDescription": "Select the number of passengers", "showCasePickUpLocationDescription": "Select your pickup location", "showCaseDestinationLocationDescription": "Select your destination location", "showCaseFindRiderDescription": "Find available riders", "numberOfPassenger": "Select the number of passengers for your trip", "buttonTitle": "Find Rider", "passengerHomeBottomSheetDestination": "Your Destinations", "passengerHomeDirectionErrorText": "Direction Route is null", "loginScreenAppHeaderTitle": "LogIn your Account", "loginScreenAppHeaderSubTitle": "Please enter your details to continue", "loginScreenPhoneNumberFormLabel": "Phone Number", "loginScreenPasswordFormLabel": "Password", "loginScreenForgetPasswordBtnTextLabel": "Forget Password?", "loginScreenSucessToastLabel": "<PERSON><PERSON>l", "loginScreenErrorToastLoginNotActiveLabel": "Please verify your number.<PERSON>gin not active yet.", "loginScreenButtonTitle": "<PERSON><PERSON>", "loginScreenDontHaveAnAccount": "Don't have an account?", "loginScreenSignUpButtonTitle": "Sign Up", "footerWidgetTextSpanByContinue": "By continuing, you agree to our ", "footerWidgetTextSpanTermsOfServices": "Terms of Service", "footerWidgetTextSpanAnd": " and ", "footerWidgetTextSpanPrivacyPolicy": "Privacy Policy", "signUpScreenAppHeaderTitle": "Create your Account", "signUpScreenAppHeaderSubTitle": "Please enter your details to sign up", "signUpScreenFullNameLabel": "Full Name", "signUpScreenPhoneNumberLabel": "Phone Number", "signUpScreenEmailAddressLabel": "Email Address", "signUpScreenCurrentAddressLabel": "Current Address", "signUpScreenGenderLabel": "Gender", "signUpScreenSignUpButtonTitle": "Sign Up", "signUpScreenAlreadyHaveAccountText": "Already have an account?", "signUpScreenSignInText": "Sign In", "verifyOtpScreenAppHeaderTitle": "Verify OTP", "verifyOtpScreenAppHeaderSubTitle": "Please enter the 4-digit OTP sent to your number.", "verifyOtpScreenResendButtonText": "Resend OTP", "verifyOtpScreenResendCountdownText": "Resend available in {_secondsRemaining} seconds", "verifyOtpScreenResendSuccessToast": "OTP resent successfully", "verifyOtpScreenResendFailedToast": "Failed to resend OTP", "verifyOtpScreenOtpVerifySuccessToast": "OTP verification successful", "verifyOtpScreenOtpVerifyFailedToast": "Incorrect OTP. Please try again.", "verifyOtpScreenPleaseLoginToast": "Please, login with your credentials.", "driverDrawerScreenHomeTitle": "Home", "driverDrawerScreenMyTripsTitle": "My Trips", "driverDrawerScreenProfileTitle": "Profile", "drawerScreenSettingsTitle": "Settings", "drawerScreenSettingsTheme": "Theme Mode", "drawerScreenSettingsThemeSystem": "System", "drawerScreenSettingsThemeDark": "Dark", "drawerScreenSettingsThemeLight": "Light", "drawerScreenSettingsSelectLanguage": "Select Language", "drawerScreenSettingsChangePasswordAppBarTitle": "Change Password", "drawerScreenSettingsChangePasswordOld": "Old Password", "drawerScreenSettingsChangePasswordNew": "New Password", "drawerScreenSettingsChangePasswordConfirm": "Confirm Password", "drawerScreenSettingsChangePasswordButton": "Update Password", "drawerScreenSettingsChangePasswordValidationRequired": "This field is required", "drawerScreenSettingsChangePasswordValidationNewRequired": "New password is required", "drawerScreenSettingsChangePasswordValidationMinLength": "Password must be at least 6 characters long", "drawerScreenSettingsChangePasswordValidationConfirmRequired": "Confirm password is required", "drawerScreenSettingsChangePasswordValidationNotMatch": "Passwords do not match", "drawerScreenEmergencySafetyTitle": "Emergency & Safety", "driverDrawerScreenPassengerModeButton": "Passenger Mode", "emergencyScreenAppBarTitle": "Emergency & Safety", "emergencyScreenSupportButton": "Support", "emergencyScreenEmergencyContactsButton": "Emergency contacts", "emergencyScreenCall100Button": "Call 100", "emergencyScreenHowYoureProtectedTitle": "How you're protected", "emergencyScreenFeatureProactiveSafetySupport": "Proactive safety support", "emergencyScreenFeatureDriversVerification": "Drivers verification", "emergencyScreenFeatureProtectingPrivacy": "Protecting your privacy", "emergencyScreenFeatureStayingSafe": "Staying safe on every ride", "emergencyScreenFeatureAccidentsSteps": "Accidents: Steps to take", "notificationScreenAppBarTitle": "Notification Preferences", "notificationScreenRideRequestsTitle": "Ride Requests", "notificationScreenRideRequestsSubtitle": "Get notified about new ride requests", "notificationScreenPromotionsTitle": "Promotions", "notificationScreenPromotionsSubtitle": "Receive promotional offers and bonuses", "notificationScreenEarningsTitle": "Earnings", "notificationScreenEarningsSubtitle": "Daily and weekly earnings summaries", "notificationScreenSafetyTitle": "Safety", "notificationScreenSafetySubtitle": "Safety alerts and emergency notifications", "passengerDrawerScreenHomeTitle": "Home", "passengerDrawerScreenYourTripsTitle": "Your Trips", "passengerDrawerScreenProfileTitle": "Profile", "passengerDrawerScreenSettingsTitle": "Settings", "passengerDrawerScreenEmergencySafetyTitle": "Emergency & Safety", "passengerDrawerScreenDriverModeButton": "Driver Mode", "settingScreenAppBarTitle": "Settings", "settingScreenLanguageTitle": "Language", "settingScreenLanguageSubtitle": "English", "settingScreenChangePasswordTitle": "Change Password", "settingScreenNotificationTitle": "Notification", "settingScreenRulesAndTermsTitle": "Rules and terms", "settingScreenLogoutTitle": "Logout", "settingScreenDeleteAccountTitle": "Delete Account", "settingScreenDeleteDialogTitle": "Delete Account", "settingScreenDeleteDialogMessage": "This action will permanently delete your account and all data associated with it. Are you sure you want to proceed?", "settingScreenDeleteDialogConfirmText": "Delete Account", "settingScreenDeleteDialogCancelText": "Cancel", "appVersionWidgetLoading": "Loading version...", "appVersionWidgetUnavailable": "Version unavailable", "appVersionWidgetFormat": "Version {version}+{buildNumber}", "locationPermissionScreenTitle": "Enable Location Permission", "locationPermissionScreenDescription": "To get great service you need to provide location permission. You can always change permission from settings.", "locationPermissionScreenGivePermissionButton": "Give Permissions", "locationPermissionScreenDialogMessage": "We require your precise location to seamlessly connect you to nearby services providers.\n\nPlease turn on device location.", "locationPermissionScreenDialogGivePermission": "Give Location Permission", "locationPermissionScreenDialogOpenSettings": "Open Settings", "fareOfferScreenTitle": "Offer your fare", "fareOfferScreenSuggestedFare": "Suggested Fare:", "fareOfferScreenDistance": "Total distance:", "fareOfferMaximumFareNotice": "Maximum fare is {fare}", "fareOfferScreenScheduleTime": "Schedule Time", "fareOfferScreenDone": "Done", "fareOfferScreenEnterFareError": "Please enter a fare", "fareOfferScreenInvalidFareError": "Please enter a valid number", "fareOfferScreenNegativeFareError": "Fare cannot be negative", "fareOfferScreenMinimumFareError": "Minimum Fare rate is {minFare}", "shareBookingModeTitle": "Share Booking Mode", "scheduleTripTitle": "Schedule your trip", "schedulePickupDate": "Pickup Date", "schedulePickupTime": "Pickup Time", "selectPickupDate": "Select Pickup Date", "selectPickupTime": "Select Pickup Time", "confirm": "Confirm", "continueBtn": "Continue", "rideRequest_raiseFare": "<PERSON><PERSON>", "rideRequest_raisedFareToast": "You raised the fare to NPR {fare}", "rideRequest_payment": "Payment", "rideRequest_yourRide": "Your Current Ride", "rideRequest_pickup": "Pickup", "rideRequest_destination": "Destination", "rideRequest_cancelRequest": "Cancel Request", "rideRequest_cancelSuccess": "Request Cancel successful!!", "rideRequest_searchingMessages": "Searching for the best nearby driver...", "rideRequest_searchingMessages1": "Hang tight! We're finding your ride.", "rideRequest_searchingMessages2": "Almost there... getting a driver for you.", "rideRequest_searchingMessages3": "Your comfort ride is just a tap away 🚗💨", "rideRequest_searchingMessages4": "Making sure the driver gets your location📍", "documentReviewAppBarTitle": "Document Review", "documentReviewLoadFailureTitle": "Failed to load profile", "documentReviewRetryButton": "Retry", "documentReviewBackToHome": "Back to Home", "documentReviewStatusUnderReview": "Under Review", "documentReviewStatusCardTitle": "Current Status", "documentReviewStatusSubmittedTitle": "Documents Submitted", "documentReviewStatusSubmittedSubtitle": "All required documents received", "documentReviewStatusCheckTitle": "Background Check", "documentReviewStatusCheckSubtitle": "Currently in progress", "documentReviewStatusFinalTitle": "Final Approval", "documentReviewStatusFinalSubtitle": "Pending background check completion", "documentReviewReviewDetailsTitle": "Review Details", "documentReviewDetailEstimated": "Estimated completion", "documentReviewDetailNotification": "Notification method", "documentReviewDetailNoteTitle": "What's next?", "documentReviewDetailNoteContent": "You'll receive an email notification once the review is complete. Make sure to check your spam folder and keep your phone nearby for SMS updates.", "enterRouteTitle": "Enter your route", "chooseOnMap": "Choose on map", "continueButton": "Continue", "meters": "meter", "kilometers": "km", "ratingPageTitleDriver": "Rate Your Driver", "ratingPageTitlePassenger": "Rate Your Passenger", "ratingPageQuestion": "How was your experience with {name}?", "ratingPageCompletedTrip": "Trip completed", "ratingPageRateLabelDriver": "Rate this driver", "ratingPageRateLabelPassenger": "Rate this passenger", "ratingPageTapToRate": "Tap to rate", "ratingPageQuickFeedback": "Quick Feedback (Optional)", "ratingPageAdditionalComment": "Additional Comments (Optional)", "ratingPageCommentHint": "Share your experience...", "ratingPageButtonSkip": "<PERSON><PERSON>", "ratingPageButtonSubmit": "Submit Rating", "ratingPageSelectRatingError": "Please select a rating", "ratingTextPoor": "Poor", "ratingTextFair": "Fair", "ratingTextGood": "Good", "ratingTextVeryGood": "Very Good", "ratingTextExcellent": "Excellent", "feedbackProfessional": "Professional", "feedbackSafeDriving": "Safe Driving", "feedbackOnTime": "On Time", "feedbackFriendly": "Friendly", "feedbackCleanVehicle": "Clean Vehicle", "feedbackIssue": "Issue", "feedbackPolite": "Polite", "feedbackEasyPickup": "Easy Pickup", "feedbackRespectful": "Respectful", "whatsYourNamePageTitle": "What is Your Name?", "whatsYourNamePageSubtitle": "Please enter your legal name so customers", "fullNameLabel": "Full Name", "fullNameValidation": "Please enter your full name", "nextButton": "Next", "contactInfoTitle": "Enter your contact information", "emailLabel": "Email", "phoneNumberLabel": "Phone Number", "genderLabel": "Gender", "genderHint": "Select your gender", "addressLabel": "Address", "addressValidation": "Please enter your address", "genderValidationToast": "Please select your gender.", "uploadYourDocuments": "Upload your Documents", "selectCitizenshipFront": "Select Citizenship Front", "pleaseSelectFrontCitizenship": "Please select the front of your citizenship.", "selectCitizenshipBack": "Select Citizenship Back", "pleaseSelectBackCitizenship": "Please select the back of your citizenship.", "enterYourVehicleInformation": "Enter your Vehicle information", "vehicleNumber": "Vehicle Number", "pleaseEnterVehicleNumber": "Please enter vehicle number", "ownerName": "Owner Name", "pleaseEnterOwnerName": "Please enter owner name", "ownerPhone": "Owner Phone", "pleaseEnterOwnerPhoneNumber": "Please enter owner phone number", "uploadVehiclePhoto": "Upload Vehicle Photo", "uploadBillBookPhoto": "Upload Bill Book Photo", "pleaseSelectVehicleType": "Please select a vehicle type.", "pleaseUploadVehiclePhoto": "Please upload a vehicle photo.", "pleaseUploadBlueBookPhoto": "Please upload a blue book photo.", "uploadYourSelfie": "Upload your Selfie", "selectIdentityPhotoSelfie": "Select Identity Photo (<PERSON>ie)", "pleaseUploadYourSelfieFirst": "Please upload your selfie first.", "driverHomePageNoPassengers": "No Passengers", "currentRides": "Current Rides", "currentBooking": "Current Bookings", "simpleBookingCardDistance": "Distance", "simpleBookingCardFare": "Fare", "statusToggleButtonOnline": "Online", "statusToggleButtonOffline": "Offline", "rideTrackingPageForDriverBookingCancelled": "Booking cancelled", "rideTrackingPageForDriverReason": "Reason", "rideTrackingPageForDriverNavigate": "Navigate", "rideTrackingPageForDriverNoPhoneNumber": "No phone number available", "rideTrackingPageForDriverImHere": "I'm here", "rideTrackingPageForDriverStartRide": "Start Ride", "rideTrackingPageForDriverCompleteRide": "Complete Ride", "rideTrackingPageForDriverRideCompleted": "Ride Completed", "confirmationCodeDialogEnterConfirmationCode": "Enter Confirmation Code", "confirmationCodeDialogSubtitle": "Please enter the 6-digit code provided by the passenger", "confirmationCodeDialogHint": "123456", "confirmationCodeDialogCancel": "Cancel", "confirmationCodeDialogStartRide": "Start Ride", "confirmationCodeDialogEmptyCodeError": "Please enter a confirmation code.", "tripTabBarCurrentBooking": "Current Booking", "tripTabBarActiveBooking": "Active Booking", "tripBookingCartDate": "Date", "tripBookingCartDistance": "Distance", "tripBookingCartPassengers": "Passengers", "tripBookingCartFareAmount": "<PERSON><PERSON> Amount", "tripBookingCartRider": "Rider", "tripBookingCartPassenger": "Passenger", "tripBookingCartPayment": "Payment", "tripBookingCartBookingDetails": "Booking Details", "tripBookingCartPickUpLocation": "Pick up location", "tripBookingCartDropOffLocation": "Drop off location", "tripBookingCartTripInformation": "Trip Information", "tripBookingCartBookingDate": "Booking Date", "tripBookingCartStartDate": "Start Date", "tripBookingCartTripType": "Trip Type", "tripBookingCartSharedRide": "Shared Ride", "tripBookingCartPrivateRide": "Private Ride", "tripBookingCartPeople": "People", "tripBookingCartTotalFare": "Total Fare", "tripBookingCartAcceptedFare": "Accepted <PERSON><PERSON>", "tripBookingCartPaymentStatus": "Payment Status", "tripBookingCartCancellation": "Cancellation", "tripBookingCartCancelledDate": "Cancelled Date", "tripBookingCartCancelledBy": "Cancelled By", "tripBookingCartReason": "Reason", "tripBookingCartRouteDetails": "Route Details", "tripBookingCartPickUp": "Pick Up", "tripBookingCartDropOff": "Drop Off", "tripBookingCartMyTrips": "My Trips", "profilePageSaveChanges": "Save Changes", "profilePageUploadingImage": "Uploading image...", "profilePageProfilePictureUpdated": "Profile picture updated", "profilePageProfileUpdated": "Profile updated successfully", "profilePageSetAddress": "Set Address", "profilePagePersonalInformation": "Personal Information", "profilePageFullName": "Full Name", "profilePageEmailAddress": "Email Address", "profilePageGender": "Gender", "profilePageCurrentAddress": "Current Address", "profilePageAccountStatus": "Account Status", "profilePageUserType": "User Type", "profilePageLoginStatus": "Login Status", "profilePageMemberSince": "Member Since", "profilePageAccountActivated": "Account Activated", "profilePageLocationSettings": "Location Settings", "profilePageHome": "Home", "profilePageWork": "Work", "profilePageHomeLocation": "Home Location", "profilePageCurrentLocation": "Current Location", "profilePageThisFeatureNotAvailable": "This feature is currently not available", "viewProfileDialogEditProfile": "Edit Profile", "viewProfileDialogUpdateInfo": "Update your personal information", "viewProfileDialogLogout": "Logout", "viewProfileDialogLogoutSubtitle": "Sign out of your account", "viewProfileDialogCancel": "Cancel", "pickupHint": "Pickup location", "destinationHint": "To", "serviceStatusBooked": "Booked", "serviceStatusStarted": "Started", "serviceStatusCompleted": "Completed", "serviceStatusCancelled": "Cancelled", "searchRadiusTitle": "Search Radius", "searchRadiusSubtitle": "Select how far you want to find drivers nearby", "fareTooLowMessage": "Your offer (NRP {fare}) may be too low. Try increasing the fare to attract drivers.", "noDriversFoundMessage": "No nearby drivers found yet. We’re expanding your request to reach more drivers.", "statusLookingForDrivers": "Looking for nearby drivers", "statusSendingOffer": "Sending your offer to drivers", "statusWaitingForResponse": "Waiting for drivers to respond"}