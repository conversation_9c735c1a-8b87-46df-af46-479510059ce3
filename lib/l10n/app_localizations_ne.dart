// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Nepali (`ne`).
class AppLocalizationsNe extends AppLocalizations {
  AppLocalizationsNe([String locale = 'ne']) : super(locale);

  @override
  String get appname => 'सफारी यात्री';

  @override
  String get welcomeback => 'फेरि स्वागत छ';

  @override
  String get signintocontinue => 'जारी राख्न साइन इन गर्नुहोस्';

  @override
  String get email => 'इमेल';

  @override
  String get password => 'पासवर्ड';

  @override
  String get signin => 'साइन इन';

  @override
  String get signup => 'साइन अप';

  @override
  String get donthaveaccount => 'खाता छैन? साइन अप गर्नुहोस्';

  @override
  String get selectedLanguage => 'भाषा छान्नुहोस्';

  @override
  String get chooseLater => 'तपाईं सेटिङहरूबाट पछि भाषा परिवर्तन गर्न सक्नुहुन्छ।';

  @override
  String get next => 'अर्को';

  @override
  String get skip => 'छोड्नुहोस्';

  @override
  String get done => 'सकियो';

  @override
  String get loginScreenRememberMeLabel => 'मलाई सम्झनुहोस्';

  @override
  String get onBoardingfirstScreenTitle => 'सबै सेवाहरूको लागि एउटा एप।';

  @override
  String get onBoardingSecondScreenTitle => 'समयमै त्यहाँ पुग्नुहोस्';

  @override
  String get onBoardingThirdScreenTitle => 'तिर्नुहोस, जस्तो चाहनुहुन्छ।';

  @override
  String get onBoardingfirstScreenDesc => 'आफ्नो औंलाको छेउमा सवारी गर्नुहोस्।';

  @override
  String get onBoardingSecondScreenDesc => 'ट्राफिकलाई कम गर्नुहोस् र हरेक पटक छिटो आफ्नो गन्तव्यमा पुग्नुहोस्।';

  @override
  String get onBoardingThirdScreenDesc => 'नगद? कार्ड? वालेट? हामी यो सबै स्वीकार गर्छौं। सुरु गरौं।';

  @override
  String get welcomeText => 'स्वागत छ';

  @override
  String get welcomeDescription => 'राइड सेयरिङ अनुभव अझ राम्रो बनाउनुहोस्';

  @override
  String get locationPermissionTitle => 'स्थान सुरक्षा';

  @override
  String get locationPermissionDescription => 'यो अप्लिकेशन उपयोग गर्नको लागि स्थान सुरक्षा गर्नुहोस्।';

  @override
  String get enableLocation => 'स्थान सुरक्षा गर्नुहोस्';

  @override
  String get locationEnableAccess => 'स्थान पहुँच सक्षम गर्नुहोस्';

  @override
  String get locationRequestingPermission => 'अनुमति अनुरोध गर्दै...';

  @override
  String get locationAccessGranted => 'स्थान पहुँच प्रदान गरियो';

  @override
  String get locationNeeded => 'स्थान आवश्यक छ';

  @override
  String get locationDescription => 'यो एपलाई राइडरहरूलाई ड्राइभरहरूसँग जोड्न र यात्रीहरूलाई यात्राहरू ट्र्याक गर्न मद्दत गर्न तपाईंको स्थान चाहिन्छ।';

  @override
  String get locationWaitingConfirmation => 'अनुमति संवादमा तपाईंको पुष्टिको लागि प्रतीक्षा गर्दै।';

  @override
  String get locationRedirecting => 'राम्रो! हामीले तपाईंको स्थान प्राप्त गर्यौं। अब तपाईंलाई पुनर्निर्देशित गर्दै...';

  @override
  String get locationDenied => 'तपाईंले स्थान पहुँच अस्वीकार गर्नुभयो। फेरि प्रयास गर्न तल ट्याप गर्नुहोस्।';

  @override
  String get locationPermanentlyDenied => 'स्थान स्थायी रूपमा अस्वीकार गरियो। कृपया पहुँच अनुमति दिन सेटिङहरू खोल्नुहोस्।';

  @override
  String get locationRequired => 'जारी राख्न स्थान अनुमति आवश्यक छ।';

  @override
  String get locationEnableButton => 'स्थान सक्षम गर्नुहोस्';

  @override
  String get locationOpenSettings => 'एप सेटिङहरू खोल्नुहोस्';

  @override
  String get locationTryAgain => 'पुन: प्रयास गर्नुहोस्';

  @override
  String get locationGoToSettings => 'सेटिङहरू खोल्नुहोस्';

  @override
  String get locationAllowAccess => 'स्थान पहुँच अनुमति दिनुहोस्';

  @override
  String get locationPermissionHeader => 'स्थान अनुमति आवश्यक छ';

  @override
  String get locationServiceDisabled => 'स्थान सेवाहरू अक्षम';

  @override
  String get locationServiceDisabledMessage => 'तपाईंको उपकरणमा स्थान सेवाहरू बन्द छन्। कृपया जारी राख्न तिनीहरूलाई सक्षम गर्नुहोस्।';

  @override
  String get openLocationSettings => 'स्थान सेटिङहरू खोल्नुहोस्';

  @override
  String get showCaseDrawerDescription => 'आफ्नो प्रोफाइल र सेटिङहरू हेर्नुहोस्';

  @override
  String get showCasePassengerCoutDescription => 'यात्रुको संख्या चयन गर्नुहोस्';

  @override
  String get showCasePickUpLocationDescription => 'आफ्नो पिकअप स्थान चयन गर्नुहोस्';

  @override
  String get showCaseDestinationLocationDescription => 'गन्तव्य स्थान चयन गर्नुहोस्';

  @override
  String get showCaseFindRiderDescription => 'उपलब्ध राइडरहरू खोज्नुहोस्';

  @override
  String get numberOfPassenger => 'तपाईंको यात्राका लागि यात्रुको संख्या छान्नुहोस्';

  @override
  String get buttonTitle => 'राइडर खोज्नुहोस्';

  @override
  String get passengerHomeBottomSheetDestination => 'तपाईंको गन्तव्यहरू';

  @override
  String get passengerHomeDirectionErrorText => 'दिशा मार्ग फेला परेन';

  @override
  String get loginScreenAppHeaderTitle => 'आफ्नो खाता लगइन गर्नुहोस्';

  @override
  String get loginScreenAppHeaderSubTitle => 'कृपया जारी राख्न आफ्नो विवरणहरू प्रविष्ट गर्नुहोस्';

  @override
  String get loginScreenPhoneNumberFormLabel => 'फोन नम्बर';

  @override
  String get loginScreenPasswordFormLabel => 'पासवर्ड';

  @override
  String get loginScreenForgetPasswordBtnTextLabel => 'पासवर्ड बिर्सनुभयो?';

  @override
  String get loginScreenSucessToastLabel => 'लगइन सफल भयो';

  @override
  String get loginScreenErrorToastLoginNotActiveLabel => 'कृपया आफ्नो नम्बर प्रमाणिकरण गर्नुहोस्। लगइन अझै सक्रिय छैन।';

  @override
  String get loginScreenButtonTitle => 'लगइन';

  @override
  String get loginScreenDontHaveAnAccount => 'खाता छैन?';

  @override
  String get loginScreenSignUpButtonTitle => 'साइन अप गर्नुहोस्';

  @override
  String get footerWidgetTextSpanByContinue => 'जारी राखेर, तपाईं हाम्रो सँग सहमत हुनुहुन्छ ';

  @override
  String get footerWidgetTextSpanTermsOfServices => 'सेवाका सर्तहरू';

  @override
  String get footerWidgetTextSpanAnd => ' र ';

  @override
  String get footerWidgetTextSpanPrivacyPolicy => 'गोपनीयता नीति';

  @override
  String get signUpScreenAppHeaderTitle => 'आफ्नो खाता बनाउनुहोस्';

  @override
  String get signUpScreenAppHeaderSubTitle => 'साइन अप गर्न कृपया आफ्नो विवरणहरू प्रविष्ट गर्नुहोस्';

  @override
  String get signUpScreenFullNameLabel => 'पूरा नाम';

  @override
  String get signUpScreenPhoneNumberLabel => 'फोन नम्बर';

  @override
  String get signUpScreenEmailAddressLabel => 'इमेल ठेगाना';

  @override
  String get signUpScreenCurrentAddressLabel => 'हालको ठेगाना';

  @override
  String get signUpScreenGenderLabel => 'लिङ्ग';

  @override
  String get signUpScreenSignUpButtonTitle => 'साइन अप गर्नुहोस्';

  @override
  String get signUpScreenAlreadyHaveAccountText => 'पहिले नै खाता छ?';

  @override
  String get signUpScreenSignInText => 'साइन इन गर्नुहोस्';

  @override
  String get verifyOtpScreenAppHeaderTitle => 'ओटीपी प्रमाणिकरण गर्नुहोस्';

  @override
  String get verifyOtpScreenAppHeaderSubTitle => 'कृपया तपाईंको नम्बरमा पठाइएको ४-अंकीय ओटीपी प्रविष्ट गर्नुहोस्।';

  @override
  String get verifyOtpScreenResendButtonText => 'ओटीपी पुन: पठाउनुहोस्';

  @override
  String verifyOtpScreenResendCountdownText(Object _secondsRemaining) {
    return 'पुन: पठाउन $_secondsRemaining सेकेन्ड बाँकी';
  }

  @override
  String get verifyOtpScreenResendSuccessToast => 'ओटीपी सफलतापूर्वक पुन: पठाइयो';

  @override
  String get verifyOtpScreenResendFailedToast => 'ओटीपी पुन: पठाउन असफल भयो';

  @override
  String get verifyOtpScreenOtpVerifySuccessToast => 'ओटीपी प्रमाणिकरण सफल भयो';

  @override
  String get verifyOtpScreenOtpVerifyFailedToast => 'ओटीपी गलत छ, कृपया फेरि प्रयास गर्नुहोस्';

  @override
  String get verifyOtpScreenPleaseLoginToast => 'कृपया आफ्नो लगइन विवरणहरू प्रयोग गरी लगइन गर्नुहोस्।';

  @override
  String get driverDrawerScreenHomeTitle => 'गृहपृष्ठ';

  @override
  String get driverDrawerScreenMyTripsTitle => 'मेरो यात्राहरू';

  @override
  String get driverDrawerScreenProfileTitle => 'प्रोफाइल';

  @override
  String get drawerScreenSettingsTitle => 'सेटिङ्स';

  @override
  String get drawerScreenSettingsTheme => 'थिम मोड';

  @override
  String get drawerScreenSettingsThemeSystem => 'प्रणाली';

  @override
  String get drawerScreenSettingsThemeDark => 'अँध्यारो';

  @override
  String get drawerScreenSettingsThemeLight => 'उज्यालो';

  @override
  String get drawerScreenSettingsSelectLanguage => 'भाषा छान्नुहोस्';

  @override
  String get drawerScreenSettingsChangePasswordAppBarTitle => 'पासवर्ड परिवर्तन गर्नुहोस्';

  @override
  String get drawerScreenSettingsChangePasswordOld => 'पुरानो पासवर्ड';

  @override
  String get drawerScreenSettingsChangePasswordNew => 'नयाँ पासवर्ड';

  @override
  String get drawerScreenSettingsChangePasswordConfirm => 'पासवर्ड पुष्टि गर्नुहोस्';

  @override
  String get drawerScreenSettingsChangePasswordButton => 'पासवर्ड अपडेट गर्नुहोस्';

  @override
  String get drawerScreenSettingsChangePasswordValidationRequired => 'यो फिल्ड आवश्यक छ';

  @override
  String get drawerScreenSettingsChangePasswordValidationNewRequired => 'नयाँ पासवर्ड आवश्यक छ';

  @override
  String get drawerScreenSettingsChangePasswordValidationMinLength => 'पासवर्ड कम्तिमा ६ अक्षर लामो हुनुपर्छ';

  @override
  String get drawerScreenSettingsChangePasswordValidationConfirmRequired => 'पासवर्ड पुष्टि आवश्यक छ';

  @override
  String get drawerScreenSettingsChangePasswordValidationNotMatch => 'पासवर्डहरू मिलेनन्';

  @override
  String get drawerScreenEmergencySafetyTitle => 'आपतकालीन र सुरक्षाको सुविधा';

  @override
  String get driverDrawerScreenPassengerModeButton => 'यात्री मोड';

  @override
  String get emergencyScreenAppBarTitle => 'आपतकालीन र सुरक्षा';

  @override
  String get emergencyScreenSupportButton => 'सहयोग';

  @override
  String get emergencyScreenEmergencyContactsButton => 'आपतकालीन सम्पर्कहरू';

  @override
  String get emergencyScreenCall100Button => '१०० मा फोन गर्नुहोस्';

  @override
  String get emergencyScreenHowYoureProtectedTitle => 'तपाईं कसरी सुरक्षित हुनुहुन्छ';

  @override
  String get emergencyScreenFeatureProactiveSafetySupport => 'पूर्व सक्रिय सुरक्षा समर्थन';

  @override
  String get emergencyScreenFeatureDriversVerification => 'चालकहरूको प्रमाणीकरण';

  @override
  String get emergencyScreenFeatureProtectingPrivacy => 'तपाईंको गोपनीयताको सुरक्षा';

  @override
  String get emergencyScreenFeatureStayingSafe => 'हरेक यात्रामा सुरक्षित रहनुहोस्';

  @override
  String get emergencyScreenFeatureAccidentsSteps => 'दुर्घटना भएमा गर्ने कदमहरू';

  @override
  String get notificationScreenAppBarTitle => 'सूचना प्राथमिकताहरू';

  @override
  String get notificationScreenRideRequestsTitle => 'राइड अनुरोधहरू';

  @override
  String get notificationScreenRideRequestsSubtitle => 'नयाँ राइड अनुरोधको सूचना प्राप्त गर्नुहोस्';

  @override
  String get notificationScreenPromotionsTitle => 'प्रमोशनहरू';

  @override
  String get notificationScreenPromotionsSubtitle => 'प्रोमोशनल अफर र बोनसहरू प्राप्त गर्नुहोस्';

  @override
  String get notificationScreenEarningsTitle => 'कमाइ';

  @override
  String get notificationScreenEarningsSubtitle => 'दैनिक र साप्ताहिक आम्दानीको सारांश';

  @override
  String get notificationScreenSafetyTitle => 'सुरक्षा';

  @override
  String get notificationScreenSafetySubtitle => 'सुरक्षा सचेतना र आपतकालीन सूचना';

  @override
  String get passengerDrawerScreenHomeTitle => 'गृहपृष्ठ';

  @override
  String get passengerDrawerScreenYourTripsTitle => 'तपाईंका यात्राहरू';

  @override
  String get passengerDrawerScreenProfileTitle => 'प्रोफाइल';

  @override
  String get passengerDrawerScreenSettingsTitle => 'सेटिङ्स';

  @override
  String get passengerDrawerScreenEmergencySafetyTitle => 'आपतकालीन र सुरक्षा';

  @override
  String get passengerDrawerScreenDriverModeButton => 'ड्राइभर मोड';

  @override
  String get settingScreenAppBarTitle => 'सेटिङ्स';

  @override
  String get settingScreenLanguageTitle => 'भाषा';

  @override
  String get settingScreenLanguageSubtitle => 'अंग्रेजी';

  @override
  String get settingScreenChangePasswordTitle => 'पासवर्ड परिवर्तन गर्नुहोस्';

  @override
  String get settingScreenNotificationTitle => 'सूचना';

  @override
  String get settingScreenRulesAndTermsTitle => 'नियमहरू र सर्तहरू';

  @override
  String get settingScreenLogoutTitle => 'लगआउट';

  @override
  String get settingScreenDeleteAccountTitle => 'खाता हटाउनुहोस्';

  @override
  String get settingScreenDeleteDialogTitle => 'खाता हटाउनुहोस्';

  @override
  String get settingScreenDeleteDialogMessage => 'यो कार्यले तपाईंको खाता र त्यससँग सम्बन्धित सबै डाटा स्थायी रूपमा हटाउनेछ। के तपाईं पक्का हुनुहुन्छ?';

  @override
  String get settingScreenDeleteDialogConfirmText => 'खाता हटाउनुहोस्';

  @override
  String get settingScreenDeleteDialogCancelText => 'रद्द गर्नुहोस्';

  @override
  String get appVersionWidgetLoading => 'संस्करण लोड हुँदैछ...';

  @override
  String get appVersionWidgetUnavailable => 'संस्करण उपलब्ध छैन';

  @override
  String appVersionWidgetFormat(Object buildNumber, Object version) {
    return 'संस्करण $version+$buildNumber';
  }

  @override
  String get locationPermissionScreenTitle => 'स्थान अनुमति सक्षम गर्नुहोस्';

  @override
  String get locationPermissionScreenDescription => 'उत्तम सेवा प्राप्त गर्न तपाईंले स्थान अनुमति दिन आवश्यक छ। तपाईं सधैं सेटिङहरूबाट अनुमति परिवर्तन गर्न सक्नुहुन्छ।';

  @override
  String get locationPermissionScreenGivePermissionButton => 'अनुमति दिनुहोस्';

  @override
  String get locationPermissionScreenDialogMessage => 'हामी तपाईंलाई नजिकैको सेवा प्रदायकसँग जडान गर्न तपाईंको ठ्याक्कै स्थान आवश्यक हुन्छ।\n\nकृपया उपकरणको स्थान सेवा अन गर्नुहोस्।';

  @override
  String get locationPermissionScreenDialogGivePermission => 'स्थान अनुमति दिनुहोस्';

  @override
  String get locationPermissionScreenDialogOpenSettings => 'सेटिङ्स खोल्नुहोस्';

  @override
  String get fareOfferScreenTitle => 'आफ्नो भाडा प्रस्ताव गर्नुहोस्';

  @override
  String get fareOfferScreenSuggestedFare => 'सुझाव गरिएको भाडा:';

  @override
  String get fareOfferScreenDistance => 'कुल दुरी:';

  @override
  String fareOfferMaximumFareNotice(Object fare) {
    return 'अधिकतम भाडा $fare हो';
  }

  @override
  String get fareOfferScreenScheduleTime => 'समय निर्धारण गर्नुहोस्';

  @override
  String get fareOfferScreenDone => 'पूर्ण भयो';

  @override
  String get fareOfferScreenEnterFareError => 'कृपया भाडा प्रविष्ट गर्नुहोस्';

  @override
  String get fareOfferScreenInvalidFareError => 'कृपया मान्य संख्या प्रविष्ट गर्नुहोस्';

  @override
  String get fareOfferScreenNegativeFareError => 'भाडा नकारात्मक हुन सक्दैन';

  @override
  String fareOfferScreenMinimumFareError(Object minFare) {
    return 'न्यूनतम भाडा दर $minFare हुनुपर्छ';
  }

  @override
  String get shareBookingModeTitle => 'साझा बुकिङ मोड';

  @override
  String get scheduleTripTitle => 'आफ्नो यात्रा तालिका बनाउनुहोस्';

  @override
  String get schedulePickupDate => 'पिकअप मिति';

  @override
  String get schedulePickupTime => 'पिकअप समय';

  @override
  String get selectPickupDate => 'पिकअप मिति छान्नुहोस्';

  @override
  String get selectPickupTime => 'पिकअप समय छान्नुहोस्';

  @override
  String get confirm => 'पुष्टि गर्नुहोस्';

  @override
  String get continueBtn => 'जारी राख्नुहोस्';

  @override
  String get rideRequest_raiseFare => 'भाडा बढाउनुहोस्';

  @override
  String rideRequest_raisedFareToast(Object fare) {
    return 'तपाईंले भाडा NPR $fare मा बढाउनुभयो';
  }

  @override
  String get rideRequest_payment => 'भुक्तानी';

  @override
  String get rideRequest_yourRide => 'तपाईंको हालको यात्रा';

  @override
  String get rideRequest_pickup => 'पिकअप';

  @override
  String get rideRequest_destination => 'गन्तव्य';

  @override
  String get rideRequest_cancelRequest => 'अनुरोध रद्द गर्नुहोस्';

  @override
  String get rideRequest_cancelSuccess => 'अनुरोध सफलतापूर्वक रद्द गरियो!!';

  @override
  String get rideRequest_searchingMessages => 'नजिकको उपयुक्त चालक खोज्दैछौं...';

  @override
  String get rideRequest_searchingMessages1 => 'कृपया पर्खनुहोस्! तपाईंको सवारी खोज्दैछौं।';

  @override
  String get rideRequest_searchingMessages2 => 'झण्डै पुग्यौं... चालकको खोजी हुँदैछ।';

  @override
  String get rideRequest_searchingMessages3 => 'तपाईंको सान्ति सवारी अब आउने क्रममा छ 🚗💨';

  @override
  String get rideRequest_searchingMessages4 => 'चालकलाई तपाईंको स्थान सुनिश्चित गर्दैछौं📍';

  @override
  String get documentReviewAppBarTitle => 'कागजात समीक्षा';

  @override
  String get documentReviewLoadFailureTitle => 'प्रोफाइल लोड गर्न असफल';

  @override
  String get documentReviewRetryButton => 'पुन: प्रयास गर्नुहोस्';

  @override
  String get documentReviewBackToHome => 'होममा फर्कनुहोस्';

  @override
  String get documentReviewStatusUnderReview => 'समीक्षणमा';

  @override
  String get documentReviewStatusCardTitle => 'हालको स्थिति';

  @override
  String get documentReviewStatusSubmittedTitle => 'कागजात पेश गरियो';

  @override
  String get documentReviewStatusSubmittedSubtitle => 'सबै आवश्यक कागजातहरू प्राप्त भइसकेका छन्';

  @override
  String get documentReviewStatusCheckTitle => 'पृष्ठभूमि जाँच';

  @override
  String get documentReviewStatusCheckSubtitle => 'हाल जाँच प्रक्रिया चलिरहेको छ';

  @override
  String get documentReviewStatusFinalTitle => 'अन्तिम स्वीकृति';

  @override
  String get documentReviewStatusFinalSubtitle => 'पृष्ठभूमि जाँच पूरा भएपछि स्वीकृति हुनेछ';

  @override
  String get documentReviewReviewDetailsTitle => 'समीक्षा विवरण';

  @override
  String get documentReviewDetailEstimated => 'अनुमानित समयावधि';

  @override
  String get documentReviewDetailNotification => 'सूचना माध्यम';

  @override
  String get documentReviewDetailNoteTitle => 'अब के?';

  @override
  String get documentReviewDetailNoteContent => 'समीक्षा सम्पन्न भएपछि तपाईंले इमेल सूचना प्राप्त गर्नुहुनेछ। कृपया आफ्नो स्प्याम फोल्डर जाँच गर्नुहोस् र SMS अपडेटको लागि आफ्नो फोन नजिकै राख्नुहोस्।';

  @override
  String get enterRouteTitle => 'तपाईंको यात्रा मार्ग प्रविष्ट गर्नुहोस्';

  @override
  String get chooseOnMap => 'नक्सामा छनौट गर्नुहोस्';

  @override
  String get continueButton => 'जारी राख्नुहोस्';

  @override
  String get meters => 'मिटर';

  @override
  String get kilometers => 'कि.मी.';

  @override
  String get ratingPageTitleDriver => 'आफ्नो चालकलाई मूल्याङ्कन गर्नुहोस्';

  @override
  String get ratingPageTitlePassenger => 'आफ्नो यात्रुलाई मूल्याङ्कन गर्नुहोस्';

  @override
  String ratingPageQuestion(Object name) {
    return '$name सँगको अनुभव कस्तो रह्यो?';
  }

  @override
  String get ratingPageCompletedTrip => 'यात्रा सम्पन्न भयो';

  @override
  String get ratingPageRateLabelDriver => 'चालकको मूल्याङ्कन गर्नुहोस्';

  @override
  String get ratingPageRateLabelPassenger => 'यात्रुको मूल्याङ्कन गर्नुहोस्';

  @override
  String get ratingPageTapToRate => 'मूल्याङ्कन गर्न ट्याप गर्नुहोस्';

  @override
  String get ratingPageQuickFeedback => 'छिटो प्रतिक्रिया (वैकल्पिक)';

  @override
  String get ratingPageAdditionalComment => 'थप टिप्पणी (वैकल्पिक)';

  @override
  String get ratingPageCommentHint => 'आफ्नो अनुभव साझा गर्नुहोस्...';

  @override
  String get ratingPageButtonSkip => 'छोड्नुहोस्';

  @override
  String get ratingPageButtonSubmit => 'मूल्याङ्कन पठाउनुहोस्';

  @override
  String get ratingPageSelectRatingError => 'कृपया मूल्याङ्कन चयन गर्नुहोस्';

  @override
  String get ratingTextPoor => 'कमजोर';

  @override
  String get ratingTextFair => 'ठिकै';

  @override
  String get ratingTextGood => 'राम्रो';

  @override
  String get ratingTextVeryGood => 'धेरै राम्रो';

  @override
  String get ratingTextExcellent => 'उत्कृष्ट';

  @override
  String get feedbackProfessional => 'व्यावसायिक';

  @override
  String get feedbackSafeDriving => 'सुरक्षित ड्राइभिङ';

  @override
  String get feedbackOnTime => 'समयमै';

  @override
  String get feedbackFriendly => 'मिलनसार';

  @override
  String get feedbackCleanVehicle => 'सफा सवारी';

  @override
  String get feedbackIssue => 'समस्या';

  @override
  String get feedbackPolite => 'नम्र';

  @override
  String get feedbackEasyPickup => 'सहज पिकअप';

  @override
  String get feedbackRespectful => 'आदरभाव';

  @override
  String get whatsYourNamePageTitle => 'तपाईंको नाम के हो?';

  @override
  String get whatsYourNamePageSubtitle => 'कृपया आफ्नो कानुनी नाम प्रविष्ट गर्नुहोस् ताकि ग्राहकहरूले चिनून्';

  @override
  String get fullNameLabel => 'पूरा नाम';

  @override
  String get fullNameValidation => 'कृपया आफ्नो पूरा नाम प्रविष्ट गर्नुहोस्';

  @override
  String get nextButton => 'अर्को';

  @override
  String get contactInfoTitle => 'तपाईंको सम्पर्क जानकारी प्रविष्ट गर्नुहोस्';

  @override
  String get emailLabel => 'इमेल';

  @override
  String get phoneNumberLabel => 'फोन नम्बर';

  @override
  String get genderLabel => 'लिङ्ग';

  @override
  String get genderHint => 'आफ्नो लिङ्ग चयन गर्नुहोस्';

  @override
  String get addressLabel => 'ठेगाना';

  @override
  String get addressValidation => 'कृपया आफ्नो ठेगाना प्रविष्ट गर्नुहोस्';

  @override
  String get genderValidationToast => 'कृपया आफ्नो लिङ्ग चयन गर्नुहोस्।';

  @override
  String get uploadYourDocuments => 'तपाईंका कागजातहरू अपलोड गर्नुहोस्';

  @override
  String get selectCitizenshipFront => 'नागरिकता अगाडिको फोटो छान्नुहोस्';

  @override
  String get pleaseSelectFrontCitizenship => 'कृपया नागरिकता अगाडिको फोटो छान्नुहोस्।';

  @override
  String get selectCitizenshipBack => 'नागरिकता पछाडिको फोटो छान्नुहोस्';

  @override
  String get pleaseSelectBackCitizenship => 'कृपया नागरिकता पछाडिको फोटो छान्नुहोस्।';

  @override
  String get enterYourVehicleInformation => 'तपाईंको सवारीसाधन जानकारी प्रविष्ट गर्नुहोस्';

  @override
  String get vehicleNumber => 'सवारी नम्बर';

  @override
  String get pleaseEnterVehicleNumber => 'कृपया सवारी नम्बर प्रविष्ट गर्नुहोस्';

  @override
  String get ownerName => 'मालिकको नाम';

  @override
  String get pleaseEnterOwnerName => 'कृपया मालिकको नाम प्रविष्ट गर्नुहोस्';

  @override
  String get ownerPhone => 'मालिकको फोन';

  @override
  String get pleaseEnterOwnerPhoneNumber => 'कृपया मालिकको फोन नम्बर प्रविष्ट गर्नुहोस्';

  @override
  String get uploadVehiclePhoto => 'सवारी फोटो अपलोड गर्नुहोस्';

  @override
  String get uploadBillBookPhoto => 'बिल बुक फोटो अपलोड गर्नुहोस्';

  @override
  String get pleaseSelectVehicleType => 'कृपया सवारी प्रकार छान्नुहोस्।';

  @override
  String get pleaseUploadVehiclePhoto => 'कृपया सवारी फोटो अपलोड गर्नुहोस्।';

  @override
  String get pleaseUploadBlueBookPhoto => 'कृपया बिल बुक फोटो अपलोड गर्नुहोस्।';

  @override
  String get uploadYourSelfie => 'तपाईंको सेल्फी अपलोड गर्नुहोस्';

  @override
  String get selectIdentityPhotoSelfie => 'पहिचान फोटो (सेल्फी) छान्नुहोस्';

  @override
  String get pleaseUploadYourSelfieFirst => 'कृपया पहिले आफ्नो सेल्फी अपलोड गर्नुहोस्।';

  @override
  String get driverHomePageNoPassengers => 'यात्रीहरू छैनन्';

  @override
  String get currentRides => 'हालको यात्रा';

  @override
  String get currentBooking => 'हालको बुकिङहरू';

  @override
  String get simpleBookingCardDistance => 'दूरी';

  @override
  String get simpleBookingCardFare => 'भाडा';

  @override
  String get statusToggleButtonOnline => 'अनलाइन';

  @override
  String get statusToggleButtonOffline => 'अफलाइन';

  @override
  String get rideTrackingPageForDriverBookingCancelled => 'बुकिंग रद्द गरियो';

  @override
  String get rideTrackingPageForDriverReason => 'कारण';

  @override
  String get rideTrackingPageForDriverNavigate => 'नेभिगेट गर्नुहोस्';

  @override
  String get rideTrackingPageForDriverNoPhoneNumber => 'फोन नम्बर उपलब्ध छैन';

  @override
  String get rideTrackingPageForDriverImHere => 'म यहाँ छु';

  @override
  String get rideTrackingPageForDriverStartRide => 'यात्रा सुरु गर्नुहोस्';

  @override
  String get rideTrackingPageForDriverCompleteRide => 'यात्रा समाप्त गर्नुहोस्';

  @override
  String get rideTrackingPageForDriverRideCompleted => 'यात्रा समाप्त भइसकेको छ';

  @override
  String get confirmationCodeDialogEnterConfirmationCode => 'पुष्टिकरण कोड प्रविष्ट गर्नुहोस्';

  @override
  String get confirmationCodeDialogSubtitle => 'यात्रीले दिएको ६-अंकीय कोड प्रविष्ट गर्नुहोस्';

  @override
  String get confirmationCodeDialogHint => '१२३४५६';

  @override
  String get confirmationCodeDialogCancel => 'रद्द गर्नुहोस्';

  @override
  String get confirmationCodeDialogStartRide => 'यात्रा सुरु गर्नुहोस्';

  @override
  String get confirmationCodeDialogEmptyCodeError => 'कृपया पुष्टिकरण कोड प्रविष्ट गर्नुहोस्।';

  @override
  String get tripTabBarCurrentBooking => 'हालको बुकिङ';

  @override
  String get tripTabBarActiveBooking => 'सक्रिय बुकिङ';

  @override
  String get tripBookingCartDate => 'मिति';

  @override
  String get tripBookingCartDistance => 'दूरी';

  @override
  String get tripBookingCartPassengers => 'यात्रुहरू';

  @override
  String get tripBookingCartFareAmount => 'भाडा';

  @override
  String get tripBookingCartRider => 'राइडर';

  @override
  String get tripBookingCartPassenger => 'यात्रु';

  @override
  String get tripBookingCartPayment => 'भुक्तानी';

  @override
  String get tripBookingCartBookingDetails => 'बुकिङ विवरण';

  @override
  String get tripBookingCartPickUpLocation => 'पिकअप स्थान';

  @override
  String get tripBookingCartDropOffLocation => 'ड्रप अफ स्थान';

  @override
  String get tripBookingCartTripInformation => 'यात्रा जानकारी';

  @override
  String get tripBookingCartBookingDate => 'बुकिङ मिति';

  @override
  String get tripBookingCartStartDate => 'सुरु मिति';

  @override
  String get tripBookingCartTripType => 'यात्रा प्रकार';

  @override
  String get tripBookingCartSharedRide => 'साझा सवारी';

  @override
  String get tripBookingCartPrivateRide => 'निजी सवारी';

  @override
  String get tripBookingCartPeople => 'व्यक्ति';

  @override
  String get tripBookingCartTotalFare => 'कुल भाडा';

  @override
  String get tripBookingCartAcceptedFare => 'स्वीकृत भाडा';

  @override
  String get tripBookingCartPaymentStatus => 'भुक्तानी स्थिति';

  @override
  String get tripBookingCartCancellation => 'रद्द';

  @override
  String get tripBookingCartCancelledDate => 'रद्द मिति';

  @override
  String get tripBookingCartCancelledBy => 'रद्द गर्ने';

  @override
  String get tripBookingCartReason => 'कारण';

  @override
  String get tripBookingCartRouteDetails => 'रुट विवरण';

  @override
  String get tripBookingCartPickUp => 'पिक अप';

  @override
  String get tripBookingCartDropOff => 'ड्रप अफ';

  @override
  String get tripBookingCartMyTrips => 'मेरो यात्रा';

  @override
  String get profilePageSaveChanges => 'परिवर्तनहरू सुरक्षित गर्नुहोस्';

  @override
  String get profilePageUploadingImage => 'तस्बिर अपलोड हुँदैछ...';

  @override
  String get profilePageProfilePictureUpdated => 'प्रोफाइल तस्वीर अपडेट भयो';

  @override
  String get profilePageProfileUpdated => 'प्रोफाइल सफलतापूर्वक अपडेट भयो';

  @override
  String get profilePageSetAddress => 'ठेगाना सेट गर्नुहोस्';

  @override
  String get profilePagePersonalInformation => 'व्यक्तिगत जानकारी';

  @override
  String get profilePageFullName => 'पूरा नाम';

  @override
  String get profilePageEmailAddress => 'इमेल ठेगाना';

  @override
  String get profilePageGender => 'लिङ्ग';

  @override
  String get profilePageCurrentAddress => 'हालको ठेगाना';

  @override
  String get profilePageAccountStatus => 'खाता स्थिति';

  @override
  String get profilePageUserType => 'प्रयोगकर्ता प्रकार';

  @override
  String get profilePageLoginStatus => 'लगइन स्थिति';

  @override
  String get profilePageMemberSince => 'सदस्यता मिति';

  @override
  String get profilePageAccountActivated => 'खाता सक्रिय गरिएको मिति';

  @override
  String get profilePageLocationSettings => 'स्थान सेटिङ';

  @override
  String get profilePageHome => 'घर';

  @override
  String get profilePageWork => 'काम';

  @override
  String get profilePageHomeLocation => 'घरको स्थान';

  @override
  String get profilePageCurrentLocation => 'हालको स्थान';

  @override
  String get profilePageThisFeatureNotAvailable => 'यो सुविधा अहिले उपलब्ध छैन';

  @override
  String get viewProfileDialogEditProfile => 'प्रोफाइल सम्पादन गर्नुहोस्';

  @override
  String get viewProfileDialogUpdateInfo => 'तपाईंको व्यक्तिगत जानकारी अद्यावधिक गर्नुहोस्';

  @override
  String get viewProfileDialogLogout => 'लगआउट';

  @override
  String get viewProfileDialogLogoutSubtitle => 'आफ्नो खाता बाट बाहिर निस्कनुहोस्';

  @override
  String get viewProfileDialogCancel => 'रद्द गर्नुहोस्';

  @override
  String get pickupHint => 'लिन आाउने ठाउँ';

  @override
  String get destinationHint => 'जाने स्थान';

  @override
  String get serviceStatusBooked => 'बुक';

  @override
  String get serviceStatusStarted => 'सुरु गरिएको';

  @override
  String get serviceStatusCompleted => 'समाप्त';

  @override
  String get serviceStatusCancelled => 'रद्द';

  @override
  String get searchRadiusTitle => 'खोज दूरी';

  @override
  String get searchRadiusSubtitle => 'आफू वरिपरिको कति दूरीभित्र ड्राइभर खोज्ने छनोट गर्नुहोस्';

  @override
  String fareTooLowMessage(Object fare) {
    return 'तपाईंको भाडा (NRP $fare) कम हुन सक्छ। ड्राइभर आकर्षित गर्न भाडा बढाउने प्रयास गर्नुहोस्।';
  }

  @override
  String get noDriversFoundMessage => 'नजिकै ड्राइभर फेला परेनन्। हामी तपाईंको अनुरोध थप ड्राइभरहरूमा पठाउँदैछौं।';

  @override
  String get statusLookingForDrivers => 'नजिकैका ड्राइभरहरू खोज्दै';

  @override
  String get statusSendingOffer => 'तपाईंको भाडा ड्राइभरहरूलाई पठाउँदै';

  @override
  String get statusWaitingForResponse => 'ड्राइभरको प्रतिक्रियाको प्रतीक्षा गर्दै';
}
