extension FirstCharacterUppercaseExtenstion on String {
  String get firstCharacterUpperCase {
    return this[0].toUpperCase() + substring(1);
  }
}

extension RiderNameExtractor on String {
  String get riderNameOnly {
    final index = indexOf('(');
    if (index == -1) return this;
    return substring(0, index).trim();
  }
}

extension PhoneMasking on String {
  String hidePhone({int visibleDigits = 3, String maskChar = '*'}) {
    if (length <= visibleDigits) return this;
    final hiddenLength = length - visibleDigits;
    final masked = maskChar * hiddenLength;
    final visible = substring(length - visibleDigits);
    return '$masked$visible';
  }
}

extension WalletMasking on String {
  String hideWallet() {
    return 'XXXX.XX';
  }
}
