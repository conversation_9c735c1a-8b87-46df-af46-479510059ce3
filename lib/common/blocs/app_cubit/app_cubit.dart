import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:safari_yatri/core/constant/string_constant.dart';
import 'package:safari_yatri/core/services/cache_service.dart';
import 'package:safari_yatri/core/services/local_user_model_service.dart';
import 'package:safari_yatri/core/services/once_cache_service.dart';
import 'package:safari_yatri/features/role/services/remote_role_service.dart';

part 'app_state.dart';

///This cubit basically helps to manage app state
///like onboarding, login, remote role, local role, etc
///with go router refresh listenable
///and redirection mainly
class AppCubit extends Cubit<AppState> {
  AppCubit() : super(AppState.initial());

  Future<void> init() async {
    final token = await CacheService.instance.getAuthToken();
    final onboarding = await OnceCacheService.get();
    final localUserMode = await LocalUserModelService().getUserMode();
    final remoteRole = RemoteRoleService.instance.getUserRole();
    final isGranted = await Permission.location.isGranted;

    emit(
      AppState.loaded(
        isLocationGranted: isGranted,
        isOnBoardingCompleted: onboarding != null,
        isUserLoggedIn: token != null,
        localUserMode: localUserMode,
        userRemoteRole: remoteRole,
      ),
    );
  }

  Future<void> setOnBoardingCompleted() async {
    emit(state.copyWith(isOnBoardingCompleted: true));
  }

  ///we need to handle with explictly
  ///like ma chaii both verify and login cubit laii herna chahanna so I have to do this from here
  ///Externally control vairako xa
  Future<void> setUserLoggedIn(bool loggedIn) async {
    emit(state.copyWith(isUserLoggedIn: loggedIn));
    if (!loggedIn) {
      return reset();
    }
  }

  Future<void> switchToLocalUserMode(String role) async {
    await LocalUserModelService().saveUserMode(role);

    emit(state.copyWith(localUserMode: role));
  }

  Future<void> updateUserRemoteRole(String role) async {
    emit(state.copyWith(userRemoteRole: role));
  }

  void locationPermissionStatus(bool isGranted) {
    emit(state.copyWith(isLocationGranted: isGranted));
  }

  void reset() {
    emit(
      state.forceNullForRole(
        isUserLoggedIn: false,
        localUserMode: null,
        userRemoteRole: null,
      ),
    );
  }
}
