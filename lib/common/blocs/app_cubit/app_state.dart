part of 'app_cubit.dart';

class AppState {
  const AppState._({
    required this.isInitialized,
    this.isOnBoardingCompleted = false,
    this.isUserLoggedIn = false,
    required this.localUserMode,
    required this.userRemoteRole,
    required this.isLocationGranted,

    ///it will not do anything for now just for initialization purpose
  });

  factory AppState.initial() => const AppState._(
    isInitialized: false,
    isUserLoggedIn: false,
    localUserMode: kPassengerRole,
    userRemoteRole: kPassengerRole,
    isLocationGranted: false,
  );

  factory AppState.loaded({
    required bool isOnBoardingCompleted,
    required bool isUserLoggedIn,
    required String? localUserMode,
    required String? userRemoteRole,
    required bool isLocationGranted,
  }) => AppState._(
    isInitialized: true,
    isOnBoardingCompleted: isOnBoardingCompleted,
    isUserLoggedIn: isUserLoggedIn,
    localUserMode: localUserMode,
    userRemoteRole: userRemoteRole,
    isLocationGranted: isLocationGranted,
  );

  final bool isInitialized;
  final bool isOnBoardingCompleted;
  final bool isUserLoggedIn;
  final String? localUserMode;
  final String? userRemoteRole;
  final bool isLocationGranted;

  /// Create a new `AppState` based on the current one, changing only the provided fields
  AppState copyWith({
    bool? isInitialized,
    bool? isOnBoardingCompleted,
    bool? isUserLoggedIn,
    String? localUserMode,
    String? userRemoteRole,
    bool? isLocationGranted,
  }) {
    return AppState._(
      isInitialized: isInitialized ?? this.isInitialized,
      isLocationGranted: isLocationGranted ?? this.isLocationGranted,

      isOnBoardingCompleted:
          isOnBoardingCompleted ?? this.isOnBoardingCompleted,
      isUserLoggedIn: isUserLoggedIn ?? this.isUserLoggedIn,
      localUserMode: localUserMode ?? this.localUserMode,
      userRemoteRole: userRemoteRole ?? this.userRemoteRole,
    );
  }

  /// Create a new `AppState` based on the current one, changing only the provided fields
  AppState forceNullForRole({
    required bool isUserLoggedIn,
    required String? localUserMode,
    required String? userRemoteRole,
  }) {
    return AppState._(
      isInitialized: true,
      isLocationGranted: isLocationGranted,

      isOnBoardingCompleted: isOnBoardingCompleted,
      isUserLoggedIn: isUserLoggedIn,
      localUserMode: localUserMode,
      userRemoteRole: userRemoteRole,
    );
  }
}
