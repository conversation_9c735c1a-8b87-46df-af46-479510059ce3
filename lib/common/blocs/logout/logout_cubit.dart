import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/services/app_cached_keys.dart';
import 'package:safari_yatri/core/services/cache_service.dart';
import 'package:safari_yatri/core/services/hive_core_cached_service.dart';
import 'package:safari_yatri/core/services/local_user_model_service.dart';
import 'package:safari_yatri/core/services/polling/polling_task.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:safari_yatri/features/role/services/remote_role_service.dart';

part 'logout_state.dart';

class LogoutCubit extends Cubit<LogoutState> {
  final CacheService _cacheService = CacheService.instance;
  final CoreLocalDataSource _coreLocalDataSource = CoreLocalDataSource();
  final RemoteRoleService _remoteRoleService = RemoteRoleService.instance;

  LogoutCubit() : super(LogoutState.initial());

  Future<void> initialState() async {
    emit(LogoutState.initial());
  }

  Future<void> logout() async {
    emit(LogoutState.loading());
    try {
      await Future.wait(
        AppCachedKeys.allCachedKey.map(
          (key) => _coreLocalDataSource.clear(key),
        ),
      );

      await Future.wait([
        _cacheService.clearTokenData(),
        LocalUserModelService().clearUserMode(),
        PollingTask.clearAllPollingTimes(),
        _coreLocalDataSource.clearAllBox(),
        _remoteRoleService.clearUserRole(),
      ]);

      emit(LogoutState.loaded("Logout successfully"));
    } catch (e) {
      emit(LogoutState.failure(UnexpectedFailure(message: e.toString())));
    }
  }
}
