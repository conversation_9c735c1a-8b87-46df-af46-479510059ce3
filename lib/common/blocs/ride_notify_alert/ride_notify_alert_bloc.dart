import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:safari_yatri/core/errors/failure.dart';
import 'package:safari_yatri/core/state/bloc_base_state.dart';
import 'package:vibration/vibration.dart';
import 'package:audioplayers/audioplayers.dart';

part 'ride_notify_alert_event.dart';
part 'ride_notify_alert_state.dart';
part 'ride_notify_alert_bloc.freezed.dart';

class RideNotifyAlertBloc
    extends Bloc<RideNotifyAlertEvent, RideNotifyAlertState> {
  final AudioPlayer _audioPlayer = AudioPlayer();

  RideNotifyAlertBloc() : super(const BaseState.initial()) {
    on<_RideNotiyAlert>((event, emit) async {
      try {
        // Trigger vibration
        if (await Vibration.hasVibrator()) {
          Vibration.vibrate(duration: 500);
        }

        // Play sound from assets
        await _audioPlayer.play(AssetSource('audio/alert.mp3'));
        await Future.delayed(Duration(milliseconds: 2000));
        await _audioPlayer.play(AssetSource('audio/alert.mp3'));
        await Future.delayed(Duration(milliseconds: 2000));
        await _audioPlayer.play(AssetSource('audio/alert.mp3'));

        emit(const BaseState.loaded("Ride alert triggered"));
      } catch (e) {
        emit(
          BaseState.failure(
            AudioPlayerFailure(message: 'Failed to play alert: $e'),
          ),
        );
      }
    });
  }

  @override
  Future<void> close() {
    _audioPlayer.dispose();
    return super.close();
  }
}
