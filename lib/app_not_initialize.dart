import 'package:flutter/material.dart';

class AppNotInitialize extends StatelessWidget {
  const AppNotInitialize({super.key, required this.thememode});
  final ThemeMode thememode;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          thememode == ThemeMode.dark ? Colors.black : Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Your app logo here
            Image.asset('assets/logo/logo.png', width: 120, height: 120),
            const SizedBox(height: 24),
            Text(
              'Safari Yatri',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color:
                    thememode == ThemeMode.dark ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 32),
            CircularProgressIndicator.adaptive(),
          ],
        ),
      ),
    );
  }
}
