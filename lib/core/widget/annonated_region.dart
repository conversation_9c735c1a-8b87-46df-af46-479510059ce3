import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safari_yatri/common/blocs/theme_mode/theme_mode_cubit.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';

class AppAnnotatedRegion extends StatelessWidget {
  final Widget child;

  const AppAnnotatedRegion({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppThemeCubit, AppThemeState>(
      builder: (context, state) {
        final isDark = Theme.of(context).brightness == Brightness.dark;

        final brightness = isDark ? Brightness.light : Brightness.dark;

        final overlayStyle = SystemUiOverlayStyle(
          statusBarColor: T.c(context).surface,
          statusBarIconBrightness: brightness,
          statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
          systemNavigationBarColor: T.c(context).surface,
          systemNavigationBarIconBrightness: brightness,
          systemNavigationBarDividerColor: T.c(context).surface,
        );

        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: overlayStyle,
          child: child,
        );
      },
    );
  }
}
