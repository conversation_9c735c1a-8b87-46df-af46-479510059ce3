import 'package:flutter/material.dart';
import 'package:safari_yatri/core/theme/app_styles.dart';

Future<void> showCustomDialog({
  required BuildContext context,
  required String assetName,
  required String message,
  required List<Widget> actions,
  bool canPop = false,
  bool barrierDismissible = false,
}) async {
  return await showDialog(
    context: context,
    barrierDismissible: barrierDismissible,
    builder: (context) {
      return PopScope(
        canPop: canPop,
        child: AlertDialog(
          insetPadding: EdgeInsets.all(AppStyles.space12),
          actionsPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          contentPadding: EdgeInsets.symmetric(horizontal: AppStyles.space8),
          shape: RoundedRectangleBorder(borderRadius: AppStyles.radiusSm),
          title: Image.asset(assetName),
          content: Text(message),
          actions: actions,
        ),
      );
    },
  );
}
