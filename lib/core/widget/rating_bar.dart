import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

class StaticRatingBar extends StatelessWidget {
  final double rating;
  final double itemSize;
  final MainAxisAlignment alignment;
  final bool showOnlyOneStarWithValue; // New flag

  const StaticRatingBar({
    super.key,
    required this.rating,
    this.itemSize = 20.0,
    this.alignment = MainAxisAlignment.start,
    this.showOnlyOneStarWithValue = false, // Default false
  });

  @override
  Widget build(BuildContext context) {
    if (rating == 0) {
      return Icon(Icons.star, color: Colors.grey.shade300, size: itemSize);
    }

    if (showOnlyOneStarWithValue) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: alignment,
        children: [
          Icon(Icons.star, color: Colors.amber, size: itemSize),
          const SizedBox(width: 4),
          Text(
            rating.toStringAsFixed(1),
            style: TextStyle(
              fontSize: itemSize * 0.8,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    }

    return RatingBarIndicator(
      rating: rating,
      itemBuilder:
          (context, index) => const Icon(Icons.star, color: Colors.amber),
      itemCount: 5,
      itemSize: itemSize,
      direction: Axis.horizontal,
      unratedColor: Colors.grey.shade300,
      itemPadding: const EdgeInsets.symmetric(horizontal: 1.0),
    );
  }
}
