// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:safari_yatri/common/extensions/string_extension.dart';
import 'package:safari_yatri/core/utils/format_distance.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/features/booking/models/booking_details.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';

import '../utils/theme_utils.dart';

class TripBookingCart extends StatefulWidget {
  const TripBookingCart({
    super.key,
    required this.booking,
    this.isDisplayingInRiderSection = false,
    this.onBookingTapped,
  });
  final BookingModel booking;
  final bool isDisplayingInRiderSection;
  final Function(BookingModel booking)? onBookingTapped;

  @override
  State<TripBookingCart> createState() => _TripBookingCartState();
}

class _TripBookingCartState extends State<TripBookingCart> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: T.c(context).surfaceContainer,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap:
            () =>
                widget.onBookingTapped != null
                    ? widget.onBookingTapped!(widget.booking)
                    : _showBookingDetails(context, widget.booking),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with booking ID and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          '#${widget.booking.bookingId}',
                          style: TextStyle(
                            color: Colors.blue[700],
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(
                            widget.booking.serviceStatus,
                          ).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          widget.booking.serviceStatus.toUpperCase(),
                          style: TextStyle(
                            color: _getStatusColor(
                              widget.booking.serviceStatus,
                            ),
                            fontWeight: FontWeight.w600,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Icon(
                    widget.booking.isSharedBookingMode
                        ? Icons.people
                        : Icons.person,
                    color: Colors.grey[600],
                    size: 20,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Route information
              if (widget.booking.bookingDetailViews.isNotEmpty)
                _buildRouteInfo(
                  widget.booking.bookingDetailViews.first,
                  context,
                ),

              const SizedBox(height: 16),

              // Trip details row
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      Icons.calendar_today,
                      'Date',
                      _formatDate(widget.booking.bookingDate),
                    ),
                  ),
                  Expanded(
                    child: _buildDetailItem(
                      Icons.straighten,
                      'Distance',
                      formatDistance(widget.booking.totalDistanceInMeter),
                    ),
                  ),
                  Expanded(
                    child: _buildDetailItem(
                      Icons.people,
                      'Passengers',
                      widget.booking.passengerCount.toString(),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Fare and rider info
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Fare Amount',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Rs. ${widget.booking.acceptedFareAmount.toStringAsFixed(0)}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                  if (widget.booking.riderName.isNotEmpty &&
                      !widget.isDisplayingInRiderSection)
                    _buildPersonalInfor(
                      label: 'Rider',
                      name: widget.booking.riderName.riderNameOnly,
                    ),

                  if (widget.booking.passengerName.isNotEmpty &&
                      widget.isDisplayingInRiderSection)
                    _buildPersonalInfor(
                      label: 'Passenger',
                      name: widget.booking.passengerName,
                    ),
                ],
              ),

              // Payment status
              if (widget.booking.paymentStatus.isNotEmpty) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.payment,
                      size: 16,
                      color: _getPaymentStatusColor(
                        widget.booking.paymentStatus,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Payment: ${widget.booking.paymentStatus}',
                      style: TextStyle(
                        color: _getPaymentStatusColor(
                          widget.booking.paymentStatus,
                        ),
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Column _buildPersonalInfor({required String label, required String name}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(label, style: TextStyle(color: Colors.grey[600], fontSize: 12)),
        const SizedBox(height: 4),
        Row(
          children: [
            CircleAvatar(
              radius: 12,
              backgroundColor: Colors.blue[100],
              child: Icon(Icons.person, size: 16, color: Colors.blue[700]),
            ),
            const SizedBox(width: 8),
            Text(name, style: const TextStyle(fontWeight: FontWeight.w500)),
          ],
        ),
      ],
    );
  }

  Widget _buildRouteInfo(BookingDetailModel detail, BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: T.c(context).surface,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  detail.sourceAddress.isNotEmpty
                      ? detail.sourceAddress
                      : 'Pick up location',
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Container(
                width: 2,
                height: 20,
                color: Colors.grey[300],
                margin: const EdgeInsets.only(left: 3),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  detail.destinationAddress.isNotEmpty
                      ? detail.destinationAddress
                      : 'Drop off location',
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(height: 4),
        Text(label, style: TextStyle(color: Colors.grey[600], fontSize: 10)),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
        ),
      ],
    );
  }

  void _showBookingDetails(BuildContext context, BookingModel booking) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildBookingDetailsSheet(booking),
    );
  }

  Widget _buildBookingDetailsSheet(BookingModel booking) {
    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: T.c(context).surface,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      L.t.tripBookingCartBookingDetails,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),

              const Divider(),

              // Scrollable content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(20),
                  child: _buildDetailedBookingInfo(booking),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailedBookingInfo(BookingModel booking) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Status and booking info
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _getStatusColor(booking.serviceStatus).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _getStatusColor(booking.serviceStatus).withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                _getStatusIcon(booking.serviceStatus),
                color: _getStatusColor(booking.serviceStatus),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      booking.serviceStatus.toUpperCase(),
                      style: TextStyle(
                        color: _getStatusColor(booking.serviceStatus),
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      'Booking #${booking.bookingId}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // Detailed information sections
        _buildInfoSection(L.t.tripBookingCartTripInformation, [
          _buildInfoRow(
            L.t.tripBookingCartBookingDate,
            _formatDate(booking.bookingDate),
          ),
          _buildInfoRow(
            L.t.tripBookingCartStartDate,
            _formatDate(booking.bookingStartDate),
          ),
          _buildInfoRow(
            L.t.tripBookingCartTripType,
            booking.isSharedBookingMode
                ? L.t.tripBookingCartSharedRide
                : L.t.tripBookingCartPrivateRide,
          ),
          _buildInfoRow(
            L.t.tripBookingCartDistance,
            formatDistance(booking.totalDistanceInMeter),
          ),
          _buildInfoRow(
            L.t.tripBookingCartPassengers,
            booking.passengerCount.toString(),
          ),
        ]),

        const SizedBox(height: 20),

        // People information
        _buildInfoSection(L.t.tripBookingCartPeople, [
          _buildInfoRow(
            L.t.tripBookingCartPassenger,
            booking.passengerName.isNotEmpty ? booking.passengerName : 'N/A',
          ),
          _buildInfoRow(
            L.t.tripBookingCartRider,
            booking.riderName.isNotEmpty ? booking.riderName : 'N/A',
          ),
        ]),

        const SizedBox(height: 20),

        // Payment information
        _buildInfoSection(L.t.tripBookingCartPayment, [
          _buildInfoRow(
            L.t.tripBookingCartTotalFare,
            'Rs. ${booking.totalFareAmount.toStringAsFixed(2)}',
          ),
          _buildInfoRow(
            L.t.tripBookingCartAcceptedFare,
            'Rs. ${booking.acceptedFareAmount.toStringAsFixed(2)}',
          ),
          _buildInfoRow(
            L.t.tripBookingCartPaymentStatus,
            booking.paymentStatus.isNotEmpty ? booking.paymentStatus : 'N/A',
          ),
        ]),

        // Cancellation info if cancelled
        if (booking.bookingCancelDate.isNotEmpty) ...[
          const SizedBox(height: 20),
          _buildInfoSection(L.t.tripBookingCartCancellation, [
            _buildInfoRow(
              L.t.tripBookingCartCancelledDate,
              _formatDate(booking.bookingCancelDate),
            ),
            _buildInfoRow(
              L.t.tripBookingCartCancelledBy,
              booking.cancelUserName.isNotEmpty
                  ? booking.cancelUserName
                  : 'N/A',
            ),
            _buildInfoRow(
              L.t.tripBookingCartReason,
              booking.reasonForCancel.isNotEmpty
                  ? booking.reasonForCancel
                  : 'N/A',
            ),
          ]),
        ],

        // Route details
        if (booking.bookingDetailViews.isNotEmpty) ...[
          const SizedBox(height: 20),
          _buildRouteSection(booking.bookingDetailViews),
        ],
      ],
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: T.c(context).surfaceContainer,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildRouteSection(List<BookingDetailModel> routes) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          L.t.tripBookingCartRouteDetails,
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        ...routes.asMap().entries.map((entry) {
          int index = entry.key;
          BookingDetailModel route = entry.value;
          return _buildRouteItem(route, index);
        }),
      ],
    );
  }

  Widget _buildRouteItem(BookingDetailModel route, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: T.c(context).surfaceContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: index == 0 ? Colors.green : Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      index == 0
                          ? L.t.tripBookingCartPickUp
                          : L.t.tripBookingCartDropOff,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      index == 0
                          ? route.sourceAddress
                          : route.destinationAddress,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(color: Colors.grey[600], fontSize: 14),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'ongoing':
      case 'active':
        return Colors.blue;
      case 'pending':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      case 'ongoing':
      case 'active':
        return Icons.directions_car;
      case 'pending':
        return Icons.pending;
      default:
        return Icons.info;
    }
  }

  Color _getPaymentStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'paid':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(String dateString) {
    if (dateString.isEmpty) return 'N/A';
    try {
      DateTime date = DateTime.parse(dateString);
      return DateFormat('MMM dd, yyyy - hh:mm a').format(date);
    } catch (e) {
      return dateString;
    }
  }
}
