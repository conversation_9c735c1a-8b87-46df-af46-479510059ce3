import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:safari_yatri/core/config/env_config.dart';
import 'package:upgrader/upgrader.dart';
import '../utils/url_launcher.dart';

class CustomUpgradeAlertWidget extends StatelessWidget {
  final Widget child;

  const CustomUpgradeAlertWidget({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    if (true) return child;
    return EnvConfig.instance.environment == Environment.development
        ? child
        : UpgradeAlert(
          barrierDismissible: false,
          shouldPopScope: () => false,
          showReleaseNotes: true,
          showIgnore: true,
          showLater: true,
          dialogStyle:
              Platform.isAndroid
                  ? UpgradeDialogStyle.material
                  : UpgradeDialogStyle.cupertino,
          upgrader: Upgrader(
            debugDisplayAlways: EnvConfig.instance.enableLogging,
            debugLogging: EnvConfig.instance.enableLogging,
            durationUntilAlertAgain: const Duration(days: 1),
            willDisplayUpgrade: ({
              required bool display,
              String? installedVersion,
              UpgraderVersionInfo? versionInfo,
            }) {
              if (display) {
                log(
                  'Upgrade available: Installed=$installedVersion → Store=${versionInfo?.appStoreVersion}',
                );
              } else {
                log('No upgrade shown. Installed version: $installedVersion');
              }
            },
          ),
          onUpdate: () {
            urlLauncherWithFallback(context, playStoreUrl);
            return true;
          },
          onLater: () {
            log('User pressed later button');
            return true;
          },
          onIgnore: () {
            log('User pressed ignore button');
            return true;
          },
          child: child,
        );
  }
}
