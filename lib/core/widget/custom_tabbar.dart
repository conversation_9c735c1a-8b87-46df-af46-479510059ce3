import 'package:flutter/material.dart';

class CustomTabBarView extends StatelessWidget {
  final List<String> tabs;
  final List<Widget> tabViews;

  const CustomTabBarView({
    super.key,
    required this.tabs,
    required this.tabViews,
  }) : assert(
         tabs.length == tabViews.length,
         'Tabs and Views count must match',
       );

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final colorScheme = Theme.of(context).colorScheme;

    return DefaultTabController(
      length: tabs.length,
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[800] : Colors.grey[200],
              borderRadius: BorderRadius.circular(25),
            ),
            child: TabBar(
              padding: const EdgeInsets.all(4),
              indicator: BoxDecoration(
                color: colorScheme.secondary,
                borderRadius: BorderRadius.circular(25),
              ),
              labelColor: Colors.white,
              unselectedLabelColor: isDark ? Colors.white70 : Colors.black87,
              labelStyle: const TextStyle(fontWeight: FontWeight.bold),
              tabs: tabs.map((label) => Tab(text: label)).toList(),
            ),
          ),
          Expanded(child: TabBarView(children: tabViews)),
        ],
      ),
    );
  }
}
