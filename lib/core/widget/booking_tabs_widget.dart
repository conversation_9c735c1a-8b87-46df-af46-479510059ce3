import 'package:flutter/material.dart';

class BookingTabsWidget extends StatefulWidget {
  final Widget currentBooking;
  final Widget activeBooking;
  final bool showAppBar;

  const BookingTabsWidget({
    super.key,
    required this.currentBooking,
    required this.activeBooking,
    this.showAppBar = true,
  });

  @override
  State<BookingTabsWidget> createState() => _BookingTabsWidgetState();
}

class _BookingTabsWidgetState extends State<BookingTabsWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      children: [
        if (widget.showAppBar)
          Container(
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              boxShadow: [
                if (!isDark)
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
              ],
            ),
            child: TabBar(
              controller: _tabController,
              labelColor: theme.colorScheme.primary,
              unselectedLabelColor: isDark ? Colors.grey[400] : Colors.grey,
              indicatorColor: theme.colorScheme.primary,
              indicatorWeight: 3,
              labelStyle: const TextStyle(fontWeight: FontWeight.w600),
              tabs: const [
                Tab(text: 'Current Booking'),
                Tab(text: 'Active Booking'),
              ],
            ),
          ),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: theme.cardColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: TabBarView(
              controller: _tabController,
              children: [widget.currentBooking, widget.activeBooking],
            ),
          ),
        ),
      ],
    );
  }
}
