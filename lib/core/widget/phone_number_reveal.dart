import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';

class PhoneNumberReveal extends StatefulWidget {
  final String phoneNumber;
  final bool shouldReveal;
  final bool tappable;

  const PhoneNumberReveal({
    super.key,
    required this.phoneNumber,
    this.shouldReveal = false,
    this.tappable = false,
  });

  @override
  State<PhoneNumberReveal> createState() => _PhoneNumberRevealState();
}

class _PhoneNumberRevealState extends State<PhoneNumberReveal> {
  bool _isRevealed = false;

  String get maskedNumber {
    final number = widget.phoneNumber;
    if (number.length <= 3) return '**';
    final lastThree = number.substring(number.length - 3);
    return '*******$lastThree';
  }

  void _handleTap() {
    if (!widget.tappable || !widget.shouldReveal || _isRevealed) return;
    setState(() {
      _isRevealed = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final text = _isRevealed ? widget.phoneNumber : maskedNumber;

    return GestureDetector(
      onTap: _handleTap,
      child: Text(
        text,
        style: TextStyle(fontSize: 14, color: T.c(context).secondary),
      ),
    );
  }
}
