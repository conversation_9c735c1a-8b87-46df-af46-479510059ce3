import 'dart:io' show Platform;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:safari_yatri/core/widget/annonated_region.dart';

class PlatformScaffold extends StatelessWidget {
  final Widget body;
  final PreferredSizeWidget? appBar;
  final Widget? floatingActionButton;
  final Widget? bottomNavigationBar;
  final Widget? drawer;
  final bool resizeToAvoidBottomInset;
  final Color? backgroundColor;

  const PlatformScaffold({
    super.key,
    required this.body,
    this.appBar,
    this.floatingActionButton,
    this.bottomNavigationBar,
    this.drawer,
    this.resizeToAvoidBottomInset = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    if (Platform.isIOS) {
      return CupertinoPageScaffold(
        navigationBar:
            appBar is CupertinoNavigationBar
                ? appBar as CupertinoNavigationBar
                : null,
        backgroundColor: backgroundColor,
        child: Safe<PERSON><PERSON>(child: body),
      );
    } else {
      return AppAnnotatedRegion(
        child: Scaffold(
          appBar: appBar,
          body: SafeArea(child: body),
          floatingActionButton: floatingActionButton,
          bottomNavigationBar:
              bottomNavigationBar == null
                  ? null
                  : SafeArea(top: false, child: bottomNavigationBar!),
          resizeToAvoidBottomInset: resizeToAvoidBottomInset,
          drawer: drawer,
          backgroundColor: backgroundColor,
        ),
      );
    }
  }
}
