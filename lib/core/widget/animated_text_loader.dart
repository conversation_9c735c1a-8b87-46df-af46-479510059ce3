import 'dart:async';
import 'package:flutter/material.dart';

enum AnimationType {
  fade,
  slideUp,
  slideDown,
  slideLeft,
  slideRight,
  scale,
  rotation,
  typewriter,
  bounceIn,
  flipX,
  flipY,
}

class AnimatedTextLoaderController {
  _AnimatedTextLoaderState? _state;

  void _attach(_AnimatedTextLoaderState state) {
    _state = state;
  }

  void _detach() {
    _state = null;
  }

  void reset() {
    _state?.resetAnimation();
  }

  void start() {
    _state?.startAnimation();
  }

  void pause() {
    _state?.pauseAnimation();
  }

  bool get isFinished => _state?._isFinished ?? false;

  bool get isShowingLastMessage => _state?._showingLastMessage ?? false;
}

class AnimatedTextLoader extends StatefulWidget {
  final List<String> messages;
  final Duration switchDuration;
  final Duration animationDuration;
  final TextStyle? textStyle;
  final AnimationType animationType;
  final bool autoStart;
  final Curve curve;
  final TextAlign textAlign;
  final bool showLoadingDots;
  final Color? loadingDotsColor;
  final Duration totalDuration;
  final String? lastMessage;
  final bool showProgressBar;
  final bool reverseProgress;
  final AnimatedTextLoaderController? controller;

  const AnimatedTextLoader({
    super.key,
    required this.messages,
    this.switchDuration = const Duration(seconds: 2),
    this.animationDuration = const Duration(milliseconds: 800),
    this.textStyle,
    this.animationType = AnimationType.fade,
    this.autoStart = true,
    this.curve = Curves.easeInOut,
    this.textAlign = TextAlign.center,
    this.showLoadingDots = false,
    this.loadingDotsColor,
    this.totalDuration = const Duration(seconds: 120),
    this.lastMessage,
    this.showProgressBar = true,
    this.reverseProgress = true,
    this.controller,
  });

  @override
  State<AnimatedTextLoader> createState() => _AnimatedTextLoaderState();
}

class _AnimatedTextLoaderState extends State<AnimatedTextLoader>
    with TickerProviderStateMixin {
  late int _currentIndex;
  Timer? _timer;
  Timer? _globalTimer;
  Timer? _progressTimer;
  bool _isFinished = false;
  bool _showingLastMessage = false;
  bool _isPaused = false;

  late AnimationController _controller;
  late AnimationController _typewriterController;
  late AnimationController _dotsController;
  late AnimationController _progressController;

  late Animation<double> _animation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<int> _typewriterAnimation;
  late Animation<double> _dotsAnimation;
  late Animation<double> _progressAnimation;

  String _displayedText = '';

  @override
  void initState() {
    super.initState();

    widget.controller?._attach(this);

    _initializeState();
    _setupControllers();
    _setupAnimations();

    if (widget.autoStart) {
      _startSequence();
    }
  }

  @override
  void dispose() {
    widget.controller?._detach();

    _timer?.cancel();
    _globalTimer?.cancel();
    _progressTimer?.cancel();
    _controller.dispose();
    _typewriterController.dispose();
    _dotsController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  void _initializeState() {
    _currentIndex = 0;
    _isFinished = false;
    _showingLastMessage = false;
    _displayedText = '';
    _isPaused = false;
  }

  void _setupControllers() {
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _typewriterController = AnimationController(
      duration: Duration(
        milliseconds: widget.switchDuration.inMilliseconds - 200,
      ),
      vsync: this,
    );

    _dotsController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressController = AnimationController(
      duration: widget.totalDuration,
      vsync: this,
    );
  }

  void _setupAnimations() {
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    _slideAnimation = Tween<Offset>(
      begin: _getSlideBeginOffset(),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    _setupTypewriterAnimation();

    _dotsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _dotsController, curve: Curves.easeInOut),
    );

    if (widget.reverseProgress) {
      _progressAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
        CurvedAnimation(parent: _progressController, curve: Curves.linear),
      );
    } else {
      _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: _progressController, curve: Curves.linear),
      );
    }

    if (widget.showLoadingDots) {
      _dotsController.repeat(reverse: true);
    }
  }

  void _setupTypewriterAnimation() {
    if (_currentIndex < widget.messages.length) {
      _typewriterAnimation = IntTween(
        begin: 0,
        end: widget.messages[_currentIndex].length,
      ).animate(
        CurvedAnimation(parent: _typewriterController, curve: Curves.easeInOut),
      );

      _typewriterAnimation.addListener(_updateTypewriterText);
    }
  }

  void _updateTypewriterText() {
    if (_currentIndex < widget.messages.length) {
      setState(() {
        _displayedText = widget.messages[_currentIndex].substring(
          0,
          _typewriterAnimation.value.clamp(
            0,
            widget.messages[_currentIndex].length,
          ),
        );
      });
    }
  }

  Offset _getSlideBeginOffset() {
    switch (widget.animationType) {
      case AnimationType.slideUp:
        return const Offset(0, 1);
      case AnimationType.slideDown:
        return const Offset(0, -1);
      case AnimationType.slideLeft:
        return const Offset(1, 0);
      case AnimationType.slideRight:
        return const Offset(-1, 0);
      default:
        return const Offset(0, 1);
    }
  }

  void _startSequence() {
    if (_isPaused) return;
    _startAnimation();
    _startTimer();
    _startProgressAnimation();
  }

  void _startAnimation() {
    if (_isPaused) return;
    _controller.forward();
    if (widget.animationType == AnimationType.typewriter) {
      _typewriterController.forward();
    }
  }

  void _startTimer() {
    if (_isPaused) return;
    _timer = Timer.periodic(widget.switchDuration, (timer) {
      if (!_isFinished && !_showingLastMessage && !_isPaused) {
        _nextMessage();
      }
    });
  }

  void _startProgressAnimation() {
    if (_isPaused) return;
    _progressController.forward();

    _progressTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_progressController.value >= 1.0 && !_isFinished && !_isPaused) {
        _finishAnimation();
      }
    });
  }

  void _finishAnimation() {
    setState(() {
      _isFinished = true;
      _showingLastMessage = true;
    });

    _timer?.cancel();
    _progressTimer?.cancel();

    _showLastMessage();
  }

  void _showLastMessage() async {
    if (_isPaused) return;

    await _controller.reverse();

    setState(() {
      _currentIndex = widget.messages.length;
    });

    _controller.reset();
    await _controller.forward();
  }

  Future<void> _nextMessage() async {
    if (_isPaused) return;

    await _controller.reverse();

    if (widget.animationType == AnimationType.typewriter) {
      _typewriterController.reset();
    }

    setState(() {
      _currentIndex = (_currentIndex + 1) % widget.messages.length;
    });

    if (widget.animationType == AnimationType.typewriter) {
      _setupTypewriterAnimation();
    }

    _startAnimation();
  }

  String _getCurrentText() {
    if (_showingLastMessage) {
      return widget.lastMessage ?? widget.messages.last;
    }

    if (_currentIndex >= widget.messages.length) {
      return widget.lastMessage ?? widget.messages.last;
    }

    if (widget.animationType == AnimationType.typewriter) {
      return _displayedText;
    }

    return widget.messages[_currentIndex];
  }

  Widget _buildAnimatedText() {
    final text = _getCurrentText();

    Widget textWidget = Text(
      text,
      style: widget.textStyle,
      textAlign: widget.textAlign,
    );

    if (widget.showLoadingDots &&
        widget.animationType == AnimationType.typewriter &&
        !_showingLastMessage) {
      textWidget = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          textWidget,
          AnimatedBuilder(
            animation: _dotsAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _dotsAnimation.value,
                child: Text(
                  '...',
                  style:
                      widget.textStyle?.copyWith(
                        color:
                            widget.loadingDotsColor ??
                            Theme.of(context).primaryColor,
                      ) ??
                      TextStyle(
                        color:
                            widget.loadingDotsColor ??
                            Theme.of(context).primaryColor,
                      ),
                ),
              );
            },
          ),
        ],
      );
    }

    return textWidget;
  }

  Widget _buildWithAnimation(Widget child) {
    if (_showingLastMessage) {
      return Opacity(opacity: 1.0, child: child);
    }

    switch (widget.animationType) {
      case AnimationType.fade:
        return FadeTransition(opacity: _animation, child: child);

      case AnimationType.slideUp:
      case AnimationType.slideDown:
      case AnimationType.slideLeft:
      case AnimationType.slideRight:
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(opacity: _animation, child: child),
        );

      case AnimationType.scale:
        return ScaleTransition(
          scale: _scaleAnimation,
          child: FadeTransition(opacity: _animation, child: child),
        );

      case AnimationType.rotation:
        return AnimatedBuilder(
          animation: _rotationAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _rotationAnimation.value * 0.5,
              child: FadeTransition(opacity: _animation, child: child),
            );
          },
        );

      case AnimationType.bounceIn:
        return ScaleTransition(
          scale: Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
          ),
          child: child,
        );

      case AnimationType.flipX:
        return AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            if (_controller.value == 0.0) return Container();
            return Transform(
              alignment: Alignment.center,
              transform:
                  Matrix4.identity()
                    ..setEntry(3, 2, 0.001)
                    ..rotateX(_controller.value * 3.14159),
              child: child,
            );
          },
        );

      case AnimationType.flipY:
        return AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            if (_controller.value == 0.0) return Container();
            return Transform(
              alignment: Alignment.center,
              transform:
                  Matrix4.identity()
                    ..setEntry(3, 2, 0.001)
                    ..rotateY(_controller.value * 3.14159),
              child: child,
            );
          },
        );

      case AnimationType.typewriter:
        return FadeTransition(opacity: _animation, child: child);
    }
  }

  void resetAnimation() {
    _timer?.cancel();
    _globalTimer?.cancel();
    _progressTimer?.cancel();

    _controller.reset();
    _typewriterController.reset();
    _dotsController.reset();
    _progressController.reset();

    setState(() {
      _initializeState();
    });

    _setupAnimations();
    _startSequence();
  }

  void startAnimation() {
    setState(() {
      _isPaused = false;
    });
    if (!_isFinished) {
      _startSequence();
    }
  }

  void pauseAnimation() {
    setState(() {
      _isPaused = true;
    });

    _timer?.cancel();
    _progressTimer?.cancel();
    _controller.stop();
    _typewriterController.stop();
    _progressController.stop();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildWithAnimation(_buildAnimatedText()),
        const SizedBox(height: 16),
        if (widget.showProgressBar)
          AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return LinearProgressIndicator(
                value: _progressAnimation.value,
                minHeight: 6,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation(
                  Theme.of(context).primaryColor,
                ),
                borderRadius: BorderRadius.circular(3),
              );
            },
          ),
        const SizedBox(height: 16),

        if (!_showingLastMessage) ...[],
      ],
    );
  }
}
