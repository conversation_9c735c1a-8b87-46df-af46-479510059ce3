
import 'package:flutter/material.dart';
import 'package:safari_yatri/core/widget/booking_empty_state.dart';
import 'package:safari_yatri/core/widget/booking_trip_widget.dart';
import 'package:safari_yatri/features/booking/models/booking_model.dart';

class BuildAllBookings extends StatelessWidget {
  const BuildAllBookings({super.key, required this.bookings, this.onRefresh});

  final List<BookingModel> bookings;
  final void Function()? onRefresh;

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        onRefresh?.call();
      },
      child:  bookings.isEmpty
          ? const EmptyBookingWidget()
          : ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: bookings.length,
        itemBuilder: (context, index) {
          final booking = bookings[index];
          return TripBookingCart(booking: booking);
        },
      ),
    );
  }
}
