// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/app_cubit/app_cubit.dart';
import 'package:safari_yatri/common/blocs/logout/logout_cubit.dart';
import 'package:safari_yatri/common/widgets/custom_toast.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';

class LogoutCubitListener extends StatelessWidget {
  const LogoutCubitListener({super.key, required this.child});
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BlocListener<LogoutCubit, LogoutState>(
      listener: (context, state) async {
        state.whenOrNull(
          loaded: (data) async {
            // Set user logged out first
            persistenceSl<AppCubit>().setUserLoggedIn(false);

            // Only need to navigate,we don't restart here
            //restart means we dont restart widget

            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (context.mounted) {
                context.goNamed(AppRoutesName.signIn);
              }
            });
            return;
          },
          failure: (failure) {
            CustomToast.showError(failure.message);
          },
        );
      },
      child: child,
    );
  }
}
