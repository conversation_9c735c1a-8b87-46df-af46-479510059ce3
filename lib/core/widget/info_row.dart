import 'package:flutter/material.dart';

class InfoRow extends StatelessWidget {
  final String label;
  final String value;
  final IconData? icon;
  final double spacing;
  final CrossAxisAlignment alignment;
  final double? labelFontSize;
  final double? valueFontSize;

  const InfoRow({
    super.key,
    required this.label,
    required this.value,
    this.icon,
    this.spacing = 8.0,
    this.alignment = CrossAxisAlignment.start,
    this.labelFontSize,
    this.valueFontSize,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final labelStyle = theme.textTheme.bodyMedium?.copyWith(
      fontSize: labelFontSize,
      color: theme.colorScheme.onSurface.withOpacity(0.7),
      fontWeight: FontWeight.w500,
    );
    final valueStyle = theme.textTheme.bodyMedium?.copyWith(
      fontSize: valueFontSize,
      color: theme.colorScheme.onSurface,
      fontWeight: FontWeight.w600,
    );

    return Row(
      crossAxisAlignment: alignment,
      children: [
        if (icon != null)
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: Icon(
              icon,
              size: (labelFontSize ?? 14) + 2,
              color: labelStyle?.color,
            ),
          ),
        Text('$label:', style: labelStyle),
        SizedBox(width: spacing),
        Expanded(
          child: Text(
            value,
            style: valueStyle,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
