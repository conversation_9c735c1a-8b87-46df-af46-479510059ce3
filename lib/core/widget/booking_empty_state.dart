import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';

class EmptyBookingWidget extends StatelessWidget {
  const EmptyBookingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.drive_eta_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text('No trips found', style: T.t(context).displaySmall),
          const SizedBox(height: 8),
          Text(
            'Your booking history will appear here',
            style: T.t(context).labelSmall,
          ),

          const SizedBox(height: 24),
        ],
      ),
    );
  }
}
