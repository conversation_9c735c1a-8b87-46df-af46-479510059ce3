// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

import 'package:safari_yatri/common/blocs/logout/logout_cubit.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/utils/theme_utils.dart';

class LogoutAlertDialog extends StatefulWidget {
  const LogoutAlertDialog({super.key});

  @override
  State<LogoutAlertDialog> createState() => _LogoutAlertDialogState();
}

class _LogoutAlertDialogState extends State<LogoutAlertDialog> {
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'Confirm Logout',
        style: T
            .t(context)
            .headlineSmall
            ?.copyWith(fontWeight: FontWeight.bold),
      ),
      content: Text(
        'Are you sure you want to logout?',
        style: T.t(context).bodyMedium,
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: Text(
            'Cancel',
            style: T
                .t(context)
                .labelLarge
                ?.copyWith(color: T.c(context).onSurfaceVariant),
          ),
        ),
        FilledButton(
          onPressed: () {
            Navigator.of(context).pop();
            persistenceSl<LogoutCubit>().logout();
          },
          style: FilledButton.styleFrom(
            backgroundColor: T.c(context).error,
            foregroundColor: T.c(context).onError,
          ),
          child: Text(
            L.t.settingScreenLogoutTitle,
            style: T
                .t(context)
                .labelLarge
                ?.copyWith(color: T.c(context).onError),
          ),
        ),
      ],
    );
  }
}
