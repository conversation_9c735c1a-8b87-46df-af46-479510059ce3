import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget title;
  final List<Widget>? actions;
  final Widget? leading;
  final Color? backgroundColor;
  final double elevation;
  final bool centerTitle;
  final PreferredSizeWidget? bottom;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.backgroundColor,
    this.elevation = 0.2,
    this.centerTitle = false,
    this.bottom,
  });

  @override
  Size get preferredSize {
    final baseHeight = kToolbarHeight;
    final bottomHeight = bottom?.preferredSize.height ?? 0.0;
    return Size.fromHeight(baseHeight + bottomHeight);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textColor = theme.colorScheme.onSurfaceVariant;

    return AppBar(
      backgroundColor: backgroundColor,
      elevation: elevation,
      centerTitle: centerTitle,
      leading: leading ?? _buildDefaultLeading(context, textColor),
      title: DefaultTextStyle(
        style: GoogleFonts.poppins(
          textStyle:
              theme.textTheme.titleLarge?.copyWith(
                color: textColor,
                fontWeight: FontWeight.w600,
                fontSize: 20,
              ) ??
              TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: textColor,
              ),
        ),
        child: title,
      ),
      actions: actions,
      bottom: bottom,
    );
  }

  Widget? _buildDefaultLeading(BuildContext context, Color iconColor) {
    final ModalRoute<Object?>? parentRoute = ModalRoute.of(context);
    final bool canPop = parentRoute?.canPop ?? false;
    final ScaffoldState? scaffold = Scaffold.maybeOf(context);

    if (scaffold != null && scaffold.hasDrawer) {
      return IconButton(
        icon: Icon(Icons.menu, color: iconColor),
        onPressed: () => scaffold.openDrawer(),
      );
    }

    if (canPop) {
      return IconButton(
        icon: Icon(Icons.arrow_back, color: iconColor),
        onPressed: () => Navigator.maybePop(context),
      );
    }

    return null;
  }
}
