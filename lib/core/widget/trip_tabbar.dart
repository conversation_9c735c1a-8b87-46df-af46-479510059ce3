// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/widget/custom_appbar.dart';
import 'package:safari_yatri/core/widget/custom_platform_scaffold.dart';

class TripTabBar extends StatelessWidget {
  final Widget currentBooking;
  final Widget activeBooking;
  final String appBarTitle;

  const TripTabBar({
    super.key,
    required this.currentBooking,
    required this.activeBooking,
    required this.appBarTitle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return DefaultTabController(
      length: 2,
      child: PlatformScaffold(
        appBar: CustomAppBar(
          title: Text(appBarTitle),

          bottom: TabBar(
            isScrollable: false,
            indicator: const BoxDecoration(
              color: Colors.transparent,
            ), // removes underline
            labelColor: theme.textTheme.titleMedium?.color,
            unselectedLabelColor: theme.textTheme.bodyMedium?.color
                ?.withOpacity(0.7),
            labelStyle: const TextStyle(fontWeight: FontWeight.w600),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.normal,
            ),
            tabs: [
              Tab(text: L.t.tripTabBarCurrentBooking),
              Tab(text: L.t.tripTabBarActiveBooking),
            ],
          ),
        ),
        body: TabBarView(children: [currentBooking, activeBooking]),
      ),
    );
  }
}
