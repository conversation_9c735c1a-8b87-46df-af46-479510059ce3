import 'package:shared_preferences/shared_preferences.dart';

class LocalUserModelService {
  static final LocalUserModelService _instance =
      LocalUserModelService._internal();

  factory LocalUserModelService() => _instance;

  LocalUserModelService._internal();

  static const _keyUserMode = 'local_user_mode';

  SharedPreferences? _prefs;

  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Save user mode (e.g. 'rider', 'passenger', 'admin')
  Future<void> saveUserMode(String role) async {
    await init();
    await _prefs!.setString(_keyUserMode, role);
  }

  /// Get saved user mode
  Future<String?> getUserMode() async {
    await init();
    return _prefs!.getString(_keyUserMode);
  }

  Future<bool> isRider() async => (await getUserMode()) == 'rider';
  Future<bool> isPassenger() async => (await getUserMode()) == 'passenger';
  Future<bool> isAdmin() async => (await getUserMode()) == 'admin';

  Future<void> clearUserMode() async {
    await init();
    try {
      await _prefs!.remove(_keyUserMode);
    } catch (e) {
      print('Error clearing user mode: $e');
    }
  }
}
