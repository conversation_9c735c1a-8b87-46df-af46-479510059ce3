import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';

class PollingTask<T> {
  final String id;
  final Duration interval;
  final void Function(DateTime? lastPolledAt) onPoll;
  final bool immediate;

  Timer? _timer;
  final StreamController<void> _controller = StreamController.broadcast();
  bool _isRunning = false;
  DateTime? _lastPolledAt;

  PollingTask({
    required this.id,
    required this.interval,
    required this.onPoll,
    this.immediate = true,
  });

  bool get isRunning => _isRunning;

  Future<void> start() async {
    if (_isRunning) return;
    _isRunning = true;

    _lastPolledAt = await _getLastPolledTime();

    if (immediate) _trigger();
    _startTimer();
  }

  Future<void> resume() async {
    if (_isRunning) return;
    _isRunning = true;

    _lastPolledAt = await _getLastPolledTime();

    final now = DateTime.now();
    if (_lastPolledAt == null || now.difference(_lastPolledAt!) >= interval) {
      _trigger();
      _startTimer();
    } else {
      final remaining = interval - now.difference(_lastPolledAt!);
      _timer = Timer(remaining, () {
        _trigger();
        _startTimer();
      });
    }
  }

  void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(interval, (_) => _trigger());
  }

  Future<void> _trigger() async {
    onPoll(_lastPolledAt);
    _lastPolledAt = DateTime.now();
    await _saveLastPolledTime(_lastPolledAt!);
  }

  Future<DateTime?> _getLastPolledTime() async {
    final prefs = await SharedPreferences.getInstance();
    final millis = prefs.getInt('polling_last_time_$id');
    return millis != null ? DateTime.fromMillisecondsSinceEpoch(millis) : null;
  }

  Future<void> _saveLastPolledTime(DateTime time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('polling_last_time_$id', time.millisecondsSinceEpoch);
  }

  void stop() {
    _timer?.cancel();
    _timer = null;
    _isRunning = false;
  }

  Future<void> restart() async {
    stop();
    await start();
  }

  static Future<void> clearAllPollingTimes() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys();

    for (final key in keys) {
      if (key.startsWith('polling_last_time_')) {
        await prefs.remove(key);
      }
    }
  }

  void dispose() {
    stop();
    _controller.close();
  }
}
