import 'dart:async';

import 'polling_task.dart';

class PollingService {
  final Map<String, PollingTask> _tasks = {};

  bool hasTask(String id) => _tasks.containsKey(id);

  void register<T>({
    required String id,
    required void Function(DateTime? lastPolledAt) onPoll,
    required Duration interval,
    bool immediate = true,
  }) {
    if (_tasks.containsKey(id)) {
      throw Exception("Polling task with id '$id' already exists");
    }

    _tasks[id] = PollingTask<T>(
      id: id,
      interval: interval,
      onPoll: onPoll,
      immediate: immediate,
    );
  }

  Future<void> start(String id) async {
    final task = _tasks[id];
    if (task == null) throw Exception("Polling task '$id' not found");
    await task.start();
  }

  Future<void> resume(String id) async {
    final task = _tasks[id];
    if (task == null) throw Exception("Polling task '$id' not found");
    await task.resume();
  }

  void stop(String id) {
    final task = _tasks[id];
    if (task == null) throw Exception("Polling task '$id' not found");
    task.stop();
  }

  Future<void> restart(String id) async {
    final task = _tasks[id];
    if (task == null) throw Exception("Polling task '$id' not found");
    await task.restart();
  }

  void dispose(String id) {
    final task = _tasks.remove(id);
    task?.dispose();
  }

  Future<void> disposeAll() async {
    for (final task in _tasks.values) {
      task.dispose();
    }
    _tasks.clear();
    await PollingTask.clearAllPollingTimes();
  }

  List<String> get activeTaskIds => _tasks.keys.toList();
}
