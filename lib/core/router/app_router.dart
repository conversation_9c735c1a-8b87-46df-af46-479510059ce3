import 'package:async/async.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/blocs/app_cubit/app_cubit.dart';
import 'package:safari_yatri/common/extensions/path_extension.dart';
import 'package:safari_yatri/core/config/env_config.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart';
import 'package:safari_yatri/core/router/admin_app_router.dart';
import 'package:safari_yatri/core/router/driver_app_router.dart';
import 'package:safari_yatri/core/router/passenger_app_router.dart';
import 'package:safari_yatri/core/router/general_app_router.dart';
import 'package:safari_yatri/core/router/refresh_listenable.dart';
import 'app_route_names.dart';

final rootNavigatorKey = GlobalKey<NavigatorState>(debugLabel: 'rootNavigator');
final appCubit = persistenceSl<AppCubit>();

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: AppRoutesName.signIn.path,
    navigatorKey: rootNavigatorKey,
    debugLogDiagnostics: EnvConfig.instance.enableLogging,
    refreshListenable: GoRouterRefreshStream(
      StreamGroup.merge([appCubit.stream]),
    ),
  
    routes: [
      ...adminAppRoutes,
      ...driverAppRoutes,
      ...passengerAppRoutes,
      ...generalAppRoutes,
    ],
  );
}
