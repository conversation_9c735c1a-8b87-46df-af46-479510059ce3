import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:safari_yatri/common/extensions/path_extension.dart';
import 'package:safari_yatri/core/constant/string_constant.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/router/app_router.dart';
import 'package:safari_yatri/core/widget/role_base_navigator.dart';
import 'package:safari_yatri/features/auth/pages/change_password_page.dart';
import 'package:safari_yatri/features/auth/pages/forget_password_page.dart';
import 'package:safari_yatri/features/auth/pages/otp_verification_page.dart';
import 'package:safari_yatri/features/auth/pages/sign_in.dart';
import 'package:safari_yatri/features/auth/pages/sign_up.dart';
import 'package:safari_yatri/features/drawer/pages/language_selection_page.dart';
import 'package:safari_yatri/features/drawer/pages/payment_method_page.dart';
import 'package:safari_yatri/features/rating/pages/rating_page.dart';
import 'package:safari_yatri/features/my_profile/pages/profile_page.dart';
import 'package:safari_yatri/features/drawer/pages/setting_page.dart';
import 'package:safari_yatri/features/location/pages/location_permission_page.dart';
import 'package:safari_yatri/features/on_boarding/language_selection_page.dart';
import 'package:safari_yatri/features/on_boarding/on_boarding_page.dart';
import 'package:safari_yatri/features/location/pages/set_location_pages.dart';
import '../../features/drawer/pages/notification_page.dart';
import '../../features/drawer/pages/emergency_and_saftey.dart';

List<GoRoute> generalAppRoutes = [
  GoRoute(
    path: AppRoutesName.languageSelection.path,
    name: AppRoutesName.languageSelection,
    builder: (context, state) => LanguageSelectionPage(),
  ),
  GoRoute(
    path: AppRoutesName.onBoarding.path,
    name: AppRoutesName.onBoarding,
    builder: (context, state) => OnBoardingPage(),
  ),
  GoRoute(
    path: AppRoutesName.signIn.path,
    name: AppRoutesName.signIn,
    redirect: (context, state) {
      final isOnboardingCompleted = appCubit.state.isOnBoardingCompleted;

      if (!isOnboardingCompleted) {
        return AppRoutesName.languageSelection.path;
      }

      bool isUserLoggedIn = appCubit.state.isUserLoggedIn;
      if (!isUserLoggedIn) return null;

      final remoteRole = appCubit.state.userRemoteRole;
      final localUserMode = appCubit.state.localUserMode;

      return getRedirectPathFromRoles(
        ifBothNullRedirect: AppRoutesName.roleBaseNavigatorPage.path,
        localUserMode: localUserMode,
        remoteRole: remoteRole,
      );
    },
    builder: (context, state) {
      return SignInPage();
    },
  ),
  GoRoute(
    path: AppRoutesName.signUp.path,
    name: AppRoutesName.signUp,
    builder: (context, state) => SignUpPage(),
  ),
  GoRoute(
    path: AppRoutesName.forgetPassword.path,
    name: AppRoutesName.forgetPassword,
    builder: (context, state) => ForgetPasswordPage(),
  ),
  GoRoute(
    path: AppRoutesName.locationPermissionHandler.path,
    name: AppRoutesName.locationPermissionHandler,
    redirect: (context, state) {
      final isLocationGranted = appCubit.state.isLocationGranted;
      if (isLocationGranted) {
        return AppRoutesName.roleBaseNavigatorPage.path;
      }
      return null;
    },
    builder: (context, state) => LocationPermissionPage(),
  ),
  GoRoute(
    path: AppRoutesName.verifyOpt.path,
    name: AppRoutesName.verifyOpt,
    builder: (context, state) {
      final phoneNumber = state.extra as String?;
      return OTPVerificationPage(phoneNumber: phoneNumber);
    },
  ),
  GoRoute(
    path: AppRoutesName.setting.path,
    name: AppRoutesName.setting,
    builder: (context, state) => SettingPage(),
  ),
  GoRoute(
    path: AppRoutesName.changePassword.path,
    name: AppRoutesName.changePassword,
    builder: (context, state) => ChangePasswordPage(),
  ),
  GoRoute(
    path: AppRoutesName.selectLanguage.path,
    name: AppRoutesName.selectLanguage,
    builder: (context, state) => SelectLanguagePage(),
  ),
  GoRoute(
    path: AppRoutesName.emergencyAndSaftey.path,
    name: AppRoutesName.emergencyAndSaftey,
    builder: (context, state) => EmergencyAndSafety(),
  ),
  GoRoute(
    path: AppRoutesName.profileScreen.path,
    name: AppRoutesName.profileScreen,
    builder: (context, state) => ProfilePage(),
  ),
  GoRoute(
    path: AppRoutesName.notificationPage.path,
    name: AppRoutesName.notificationPage,
    builder: (context, state) => NotificationPage(),
  ),
  GoRoute(
    path: AppRoutesName.paymentMethodPage.path,
    name: AppRoutesName.paymentMethodPage,
    builder: (context, state) => PaymentMethodPage(),
  ),
  GoRoute(
    path: AppRoutesName.setHomeLocation.path,
    name: AppRoutesName.setHomeLocation,
    builder: (context, state) {
      final extra = state.extra as Map<String, dynamic>;
      final LatLng? initialLocation = extra['initialLocation'];
      return SetLocationPages(initialLocation: initialLocation);
    },
  ),

  GoRoute(
    path: AppRoutesName.ratingPage.path,
    name: AppRoutesName.ratingPage,
    builder: (context, state) {
      final extra = state.extra as Map<String, dynamic>;
      return RatingPage(
        bookingModel: extra['booking'],
        isPassenger: extra['isPassenger'],
      );
    },
  ),

  GoRoute(
    path: AppRoutesName.roleBaseNavigatorPage.path,
    name: AppRoutesName.roleBaseNavigatorPage,
    redirect: (context, state) {
      return getRedirectPathFromRoles(
        localUserMode: appCubit.state.localUserMode,
        remoteRole: appCubit.state.userRemoteRole,
      );
    },
    builder: (context, state) => RoleBaseNavigatorPage(),
  ),
];

String? getRedirectPathFromRoles({
  required String? localUserMode,
  required String? remoteRole,
  String? ifBothNullRedirect,
}) {
  final role = localUserMode ?? remoteRole;

  if (role == kPassengerRole) return AppRoutesName.passengerHome.path;
  if (role == kRiderRole) return AppRoutesName.driverHome.path;
  if (role == kAdminRole) return AppRoutesName.adminDashboard.path;

  ///yadi duitaii xaina vane role base navigator
  return ifBothNullRedirect;
}
