import 'package:go_router/go_router.dart';
import 'package:safari_yatri/common/extensions/path_extension.dart';
import 'package:safari_yatri/core/router/app_route_names.dart';
import 'package:safari_yatri/core/widget/bottom_sheet_page.dart';
import 'package:safari_yatri/features/booking/models/booking_add_to_cart.dart';
import 'package:safari_yatri/features/passenger/home/<USER>/passenger_home.dart';
import 'package:safari_yatri/features/passenger/location/models/location_picking_type.dart';
import 'package:safari_yatri/features/passenger/location/pages/draggable_location.dart';
import 'package:safari_yatri/features/passenger/location/pages/enter_route_bottom_sheet.dart';
import 'package:safari_yatri/features/passenger/ride/pages/passenger_trip_page.dart';
import 'package:safari_yatri/features/passenger/ride/pages/ride_tracking_page_for_passenger.dart';
import 'package:safari_yatri/features/passenger/ride_request_sheet/pages/ride_request_page.dart';

List<GoRoute> passengerAppRoutes = [
  GoRoute(
    path: AppRoutesName.passengerHome.path,
    name: AppRoutesName.passengerHome,
    builder: (context, state) => PassengerHomePage(),
  ),

  GoRoute(
    path: AppRoutesName.draggableLocation.path,
    name: AppRoutesName.draggableLocation,
    builder: (context, state) {
      final extra = state.extra as Map<String, dynamic>;
      final locationPickingType = extra['type'] as LocationPickingType;
      return DraggableLocationPickerMap(type: locationPickingType);
    },
  ),
  GoRoute(
    path: AppRoutesName.enterRoute.path,
    name: AppRoutesName.enterRoute,
    pageBuilder: (context, state) {
      final extra = state.extra as Map<String, dynamic>;
      final locationPickingType = extra['type'] as LocationType;
      final bool? shouldHidePickUpLocation = extra['shouldHidePickupLocation'];
      return BottomSheetPage(
        builder:
            (context) => EnterRouteBottomSheetPage(
              type: locationPickingType,
              shouldHidePickupLocation: shouldHidePickUpLocation ?? false,
            ),
      );
    },
  ),

  GoRoute(
    path: AppRoutesName.rideRequestPage.path,
    name: AppRoutesName.rideRequestPage,
    builder: (context, state) {
      final addToCartMap = state.extra as Map<String, dynamic>;
      final bookingAddToCartModel = BookingAddToCartModel.fromMap(addToCartMap);
      return RideRequestPage(bookingAddToCartModel: bookingAddToCartModel);
    },
  ),

  GoRoute(
    path: AppRoutesName.rideTrackingPageForPassenger.path,
    name: AppRoutesName.rideTrackingPageForPassenger,
    builder: (context, state) {
      final extra = state.extra as Map<String, dynamic>;
      return RideTrackingPageForPassenger(
        riderId: extra['riderId'],
        bookingId: extra['bookingId'],
      );
    },
  ),

  GoRoute(
    path: AppRoutesName.passengerBookingTrips.path,
    name: AppRoutesName.passengerBookingTrips,
    builder: (context, state) {
      return PassengerTripPage();
    },
  ),
];
