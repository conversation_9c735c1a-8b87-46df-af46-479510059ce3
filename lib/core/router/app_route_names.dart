class AppRoutesName {
  static const String languageSelection = 'languageSelection';
  static const String onBoarding = 'onBoarding';
  static const String signUp = 'sign-up';
  static const String signIn = 'sign-in';
  static const String forgetPassword = 'forgetPassword';
  static const String driverHome = 'driver-home';
  static const String driverDocumentPage = 'driverDocumentPage';
  static const String passengerHome = 'passenger-home';
  static const String setting = 'setting';
  static const String changePassword = 'changePassword';
  static const String selectLanguage = 'selectLanguage';
  static const String emergencyAndSaftey = 'emergencyAndSaftey';
  static const String locationPermissionHandler = 'location-permission-handler';
  static const String verifyOpt = 'verify-opt';
  static const String draggableLocation = 'draggable-location';
  static const String enterRoute = 'enter-route';
  static const String whatsYourNamePage = 'whatsYourNamePage';
  static const String contactInformationPage = 'contactInformationPage';
  static const String vehicleDetailsPage = 'vehicleDetailsPage';
  static const String documentReviewPage = 'documentReviewPage';
  static const String selfePage = 'selfePage';
  static const String profileScreen = 'profileScreen';
  static const String rideCompletedScreen = 'rideCompletedScreen';
  static const String rideSummaryPage = 'rideSummaryPage';
  static const String ratingPage = 'ratingPage';
  static const String notificationPage = 'notificationPage';
  static const String paymentMethodPage = 'paymentMethodPage';
  static const String rideRequestDetailsPage = 'rideRequestDetailsPage';
  static const String rideRequestPage = 'ride-request-page';
  static const String setHomeLocation = 'setHomeLocation';
  static const String roleBaseNavigatorPage = 'RoleBaseNavigatorPage';
  static const String rideTrackingPageForDriver =
      'rider-to-passenger-pickup-navigator';
  static const String rideTrackingPageForPassenger =
      'passenger-ride-tracking-page';

  static const String passengerBookingTrips = 'passenge-trips';
  static const String myPassengerTrips = 'driver-trips';

  // Admin routes
  static const String adminDashboard = 'admin-dashboard';
  static const String adminUserManagement = 'admin-user-management';
  static const String adminOfficeSettings = 'admin-office-settings';
  static const String adminOfficeSettingsEdit = 'admin-office-settings-edit';
  static const String adminPayments = 'admin-payments';
  static const String adminRoles = 'admin-roles';
  static const String officeManagement = 'office-management';
  static const String rideShiftManagement = 'rideShiftManagement';
  static const String rideShiftForm = 'rideShiftForm';
  static const String notification = 'notification';
  static const String rideShiftDetails = 'rideShiftDetails';
  static const String userList = 'user-list';
  static const String userDetail = 'user-detail';
  // static const String pendingRiders = 'pending-riders';
  static const String riderManagement = 'rider-management';
  static const String pendingRiderDetails = 'pending-riders-details';
}
