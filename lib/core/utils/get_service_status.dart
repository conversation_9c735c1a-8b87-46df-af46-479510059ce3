

import 'package:flutter/material.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart' show L;
import 'package:safari_yatri/core/utils/theme_utils.dart';

String getBookingServiceTranslatedStatus(String status) {
  switch (status.toLowerCase()) {
    case "booked":
      return L.t.serviceStatusBooked;
    case "servicestarted":
      return L.t.serviceStatusStarted;
    case "cancelled":
      return L.t.serviceStatusCancelled;
    case "completed":
      return L.t.serviceStatusCompleted;
    default:
      return status;
  }
}


Color getBookingStatusColor(String status, BuildContext context) {
    switch (status.toLowerCase()) {
      case 'servicestarted':
        return T.c(context).primary;
      case 'booked':
        return T.c(context).secondary;
      case 'cancelled':
        return T.c(context).error;
      case 'completed':
        return T.c(context).tertiary;
      default:
        return T.c(context).secondary;
    }
  }
