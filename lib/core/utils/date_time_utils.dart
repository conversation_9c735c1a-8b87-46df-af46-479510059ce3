import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class AppDateTimeUtils {
  /// Format: 2025-06-15
  static String getDateInYYMMDD(DateTime datetime) {
    return DateFormat('yyyy-MM-dd').format(datetime);
  }

  static String getStringDateTime(String date, String time) {
    try {
      DateTime dateTime = DateTime.parse('$date $time');
      return DateFormat('yyyy-MM-dd hh:mm a').format(dateTime);
    } catch (e) {
      return '$date $time';
    }
  }

  /// Parses a datetime string in format "M/d/yyyy hh:mm a" (e.g., "7/21/2025 12:53 pm")
  /// and returns it formatted as "yyyy-MM-dd hh:mm a" (e.g., "2025-07-21 12:53 pm").
  /// If parsing fails, returns the original string.
  static String getMMDDYYHHMMAFormatInString(String datetime) {
    try {
      final parts = datetime.trim().toLowerCase().split(
        ' ',
      ); // ['7/20/2025', '12:53', 'pm']
      if (parts.length != 3) return datetime;

      final datePart = parts[0]; // "7/20/2025"
      final timePart = parts[1]; // "12:53"
      final amPmPart = parts[2]; // "pm"

      // Parse date and time ignoring am/pm (parse as 24-hour initially)
      DateFormat dtFormat = DateFormat("M/d/yyyy HH:mm");
      // convert 12-hour time to 24-hour time string first:
      final timeSplit = timePart.split(':');
      int hour = int.parse(timeSplit[0]);
      final minute = int.parse(timeSplit[1]);

      // Adjust hour based on am/pm manually:
      if (amPmPart == 'pm' && hour < 12) hour += 12;
      if (amPmPart == 'am' && hour == 12) hour = 0;

      // Rebuild 24-hour time string
      final twentyFourHourTime =
          '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';

      // Parse final datetime string
      DateTime dateTime = dtFormat.parse('$datePart $twentyFourHourTime');

      // Format as desired output
      return DateFormat('yyyy-MM-dd hh:mm a').format(dateTime);
    } catch (e) {
      print('Manual parse error: $e');
      return datetime;
    }
  }

  /// Format: 02:05 pm
  static String getTimeInHHMMAndPeriod(TimeOfDay? timeOfDay) {
    if (timeOfDay == null) return '00:00 am';
    final hour = timeOfDay.hourOfPeriod.toString().padLeft(2, '0');
    final minute = timeOfDay.minute.toString().padLeft(2, '0');
    final period = timeOfDay.period == DayPeriod.am ? 'am' : 'pm';
    return "$hour:$minute $period";
  }

  /// Parse date and time (e.g. "2025-06-15", "14:9 pm") into DateTime
  static DateTime getTimeFromString(String date, String time) {
    final cleanedTime = _cleanTime(time);
    final dateTimeString = "$date $cleanedTime";

    try {
      return DateFormat("yyyy-MM-dd hh:mm a").parse(dateTimeString);
    } catch (e) {
      // fallback if AM/PM is missing (assuming 24-hour time)
      try {
        return DateFormat("yyyy-MM-dd HH:mm").parse("$date $time");
      } catch (e) {
        return DateTime.now(); // fallback
      }
    }
  }

  /// Normalizes a string like "14:9 pm" to "02:09 pm"
  static String _cleanTime(String time) {
    final regex = RegExp(
      r"(\d{1,2}):(\d{1,2})\s?(am|pm)",
      caseSensitive: false,
    );
    final match = regex.firstMatch(time.toLowerCase());

    if (match != null) {
      int hour = int.parse(match.group(1)!);
      int minute = int.parse(match.group(2)!);
      String period = match.group(3)!;

      if (hour > 12) hour -= 12;
      return "${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period";
    }

    return time; // return as-is if no match
  }

  ///it will only work on when we have AM and PM period
  static TimeOfDay? parseTimeOfDayFromString(String timeString) {
    try {
      final parts = timeString.trim().split(' '); // ["05:30", "AM"]
      if (parts.length != 2) return null;

      final timePart = parts[0]; // "05:30"
      final period = parts[1].toUpperCase(); // "AM" or "PM"

      final timeParts = timePart.split(':');
      if (timeParts.length != 2) return null;

      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);

      // Convert to 24-hour format
      if (period == 'PM' && hour != 12) {
        hour += 12;
      } else if (period == 'AM' && hour == 12) {
        hour = 0;
      }

      return TimeOfDay(hour: hour, minute: minute);
    } catch (e) {
      print('Error parsing time string: $e');
      return null;
    }
  }
}
