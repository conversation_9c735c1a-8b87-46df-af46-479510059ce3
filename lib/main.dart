import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:safari_yatri/app_not_initialize.dart';
import 'package:safari_yatri/common/blocs/app_cubit/app_cubit.dart';
import 'package:safari_yatri/common/blocs/booking_detail/booking_detail_bloc.dart';
import 'package:safari_yatri/common/blocs/current_latlng_address/current_lat_lng_address_bloc.dart';
import 'package:safari_yatri/common/blocs/get_ride_shift/get_ride_shift_bloc.dart';
import 'package:safari_yatri/common/blocs/get_vehicle_type/get_vehicle_type_bloc.dart';
import 'package:safari_yatri/common/blocs/image_picker/image_picker_bloc.dart';
import 'package:safari_yatri/common/blocs/locale_cubit/locale_cubit.dart';
import 'package:safari_yatri/common/blocs/logout/logout_cubit.dart';
import 'package:safari_yatri/common/blocs/ride_notify_alert/ride_notify_alert_bloc.dart';
import 'package:safari_yatri/common/blocs/selected_vehicle_type/selected_vehicle_type_cubit.dart';
import 'package:safari_yatri/common/blocs/theme_mode/theme_mode_cubit.dart';
import 'package:safari_yatri/core/app/restart_widget.dart';
import 'package:safari_yatri/core/config/env_config.dart';
import 'package:safari_yatri/core/di/dependency_injection.dart' as di;
import 'package:safari_yatri/common/blocs/once_cubit/once_cubit.dart';
import 'package:safari_yatri/common/blocs/local_user_mode/local_user_mode_cubit.dart';
import 'package:safari_yatri/core/router/app_router.dart';
import 'package:safari_yatri/core/services/cache_service.dart';
import 'package:safari_yatri/core/services/hive_core_cached_service.dart';
import 'package:safari_yatri/core/utils/localization_utils.dart';
import 'package:safari_yatri/core/services/once_cache_service.dart';
import 'package:safari_yatri/core/theme/app_theme.dart';
import 'package:safari_yatri/features/admin/core/blocs/change_email_password/change_email_password_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_admin_role_list/get_admin_role_list_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_admin_user_role/get_admin_user_role_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/manage_user_list/manage_user_list_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_office/get_office_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_pending_rider/get_pending_rider_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/get_pending_rider_details/get_pending_rider_detail_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/manage_fare_rate/manage_fare_rate_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/manage_ride_shift/manage_ride_shift_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/remember_me/remember_me_cubit.dart';
import 'package:safari_yatri/features/admin/core/blocs/update_office_setting/update_office_setting_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/user_detail/user_detail_bloc.dart';
import 'package:safari_yatri/features/admin/core/blocs/user_list/user_list_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/forget_password/forget_password_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/password_toggle_cubit/password_toggle_cubit.dart';
import 'package:safari_yatri/features/auth/blocs/resend_otp/resend_opt_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/user_login/user_login_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/user_register/user_register_bloc.dart';
import 'package:safari_yatri/features/auth/blocs/verify_otp/verify_otp_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/accept_passenger_request/accept_passenger_request_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/accept_rider_request/accept_rider_request_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/add_to_cart_booking/add_to_cart_booking_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_accepting_riders/get_my_accepting_riders_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/clear_my_cart/clear_my_cart_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_current_passengers_booking/get_my_current_passengers_booking_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_passengers_booking.dart/get_my_passengers_booking_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_new_passenger/get_new_passenger_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/polylines_points/polylines_points_bloc.dart';
import 'package:safari_yatri/features/driver/request_for_driver/blocs/request_for_driver/request_for_driver_bloc.dart';
import 'package:safari_yatri/features/driver/ride/ride_start_completor_bloc/ride_start_completor_bloc.dart';
import 'package:safari_yatri/features/location/blocs/current_location/current_location_bloc.dart';
import 'package:safari_yatri/features/location/blocs/location/location_bloc.dart';
import 'package:safari_yatri/features/location/blocs/location_permission/location_permission_bloc.dart';
import 'package:safari_yatri/features/location/blocs/current_location_navigator/current_location_navigator_cubit.dart';
import 'package:safari_yatri/features/location/blocs/places/places_bloc.dart';
import 'package:safari_yatri/features/location/blocs/set_choosed_location/set_choosed_location_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/get_my_profile/my_profile_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/terminate_account/terminate_account_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/update_my_profile/update_my_profile_bloc.dart';
import 'package:safari_yatri/features/my_profile/bloc/upload_profile_image/upload_profile_image_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/is_coming_from_draggable/is_coming_from_draggable_cubit.dart';
import 'package:safari_yatri/features/passenger/core/blocs/map_movement/map_movement_cubit.dart';
import 'package:safari_yatri/features/passenger/core/blocs/my_all_current_only_booking/my_all_current_only_booking_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/passenger_route/passenger_route_bloc.dart';
import 'package:safari_yatri/features/passenger/core/blocs/pick_location_picking_status/pickup_location_picking_status_cubit.dart';
import 'package:safari_yatri/features/booking/blocs/cancel_request/cancel_request_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_booking/get_my_booking_bloc.dart';
import 'package:safari_yatri/features/booking/blocs/get_my_current_booking/get_my_current_booking_bloc.dart';
import 'package:safari_yatri/features/rating/rating_bloc/rating_bloc.dart';
import 'package:safari_yatri/features/role/blocs/get_user_role/get_user_role_bloc.dart';
import 'package:safari_yatri/features/role/services/remote_role_service.dart';

import 'package:safari_yatri/l10n/app_localizations.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:safari_yatri/features/auth/blocs/change_password/change_password_bloc.dart';
import 'package:upgrader/upgrader.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await dotenv.load(fileName: ".env");

  EnvConfig.initialize(Environment.development);

  final documentPath = await getApplicationDocumentsDirectory();

  Hive.init(documentPath.path);

  await di.init();

  await CoreLocalDataSource.init();

  await CacheService.instance.init();

  await RemoteRoleService.init();

  await OnceCacheService.init();

  // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  // // await Firebase.initializeApp();
  // await initializeAnalytics();

  // FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;

  await Upgrader.clearSavedSettings();

  runApp(
    MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => di.persistenceSl<LocaleCubit>()),

        BlocProvider(create: (context) => di.persistenceSl<LogoutCubit>()),
        BlocProvider(
          lazy: false,
          create: (context) => di.persistenceSl<AppCubit>()..init(),
        ),
        BlocProvider(create: (context) => di.persistenceSl<RememberMeCubit>()),

        BlocProvider(create: (context) => di.persistenceSl<AppThemeCubit>()),
      ],

      child: const RestartWidget(child: SafariYatriApp()),
    ),
  );
}

class SafariYatriApp extends StatefulWidget {
  const SafariYatriApp({super.key});

  @override
  State<SafariYatriApp> createState() => _SafariYatriAppState();
}

class _SafariYatriAppState extends State<SafariYatriApp> {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    di.persistenceSl<LogoutCubit>().stream.listen((state) {
      state.whenOrNull(
        loaded: (data) {
          RestartWidget.restartApp(context);
          return;
        },
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => di.sl<LocationPermissionBloc>()),
        BlocProvider(create: (context) => di.sl<CurrentLatLngAddressBloc>()),
        BlocProvider(create: (context) => di.sl<CurrentLocationBloc>()),
        BlocProvider(
          create: (context) => di.sl<PickupLocationPickingStatusCubit>(),
        ),
        BlocProvider(create: (context) => di.sl<UserRegisterBloc>()),
        BlocProvider(create: (context) => di.sl<UserLoginBloc>()),
        BlocProvider(create: (context) => di.sl<VerifyOtpBloc>()),
        BlocProvider(create: (context) => di.sl<ForgetPasswordBloc>()),
        BlocProvider(create: (context) => di.sl<ChangePasswordBloc>()),
        BlocProvider(create: (context) => di.sl<PassengerRouteBloc>()),
        BlocProvider(create: (context) => di.sl<ResendOptBloc>()),
        BlocProvider(create: (context) => di.sl<PasswordToggleCubit>()),
        BlocProvider(create: (context) => di.sl<MapMovementCubit>()),
        BlocProvider(create: (context) => di.sl<OnceCubit>()),
        BlocProvider(create: (context) => di.sl<LocalUserModeCubit>()),
        BlocProvider(
          create: (context) => di.sl<CurrentLocationNavigatorCubit>(),
        ),
        BlocProvider(create: (context) => di.sl<ImagePickerBloc>()),
        BlocProvider(create: (context) => di.sl<RequestForDriverBloc>()),
        BlocProvider(create: (context) => di.sl<MyProfileBloc>()),
        BlocProvider(create: (context) => di.sl<UpdateMyProfileBloc>()),
        BlocProvider(create: (context) => di.sl<TerminateAccountBloc>()),
        BlocProvider(create: (context) => di.sl<UploadProfileImageBloc>()),
        BlocProvider(create: (context) => di.sl<UserListBloc>()),
        BlocProvider(create: (context) => di.sl<UserDetailBloc>()),
        BlocProvider(
          create:
              (context) => di.sl<GetOfficeBloc>()..add(GetOfficeEvent.get()),
        ),
        BlocProvider(create: (context) => di.sl<UpdateOfficeSettingBloc>()),
        BlocProvider(create: (context) => di.sl<ChangeEmailPasswordBloc>()),
        BlocProvider(
          create:
              (context) =>
                  di.sl<GetPendingRiderBloc>()..add(GetPendingRiderEvent.get()),
        ),
        BlocProvider(create: (context) => di.sl<LocationBloc>()),
        BlocProvider(
          create:
              (context) =>
                  di.sl<GetRemoteUserRoleBloc>()
                    ..add(GetRemoteUserRoleEvent.get()),
        ),
        BlocProvider(create: (context) => di.sl<IsComingFromDraggableCubit>()),
        BlocProvider(create: (context) => di.sl<PlacesBloc>()),
        BlocProvider(create: (context) => di.sl<GetVehicleTypeBloc>()),
        BlocProvider(create: (context) => di.sl<ManageUserListBloc>()),
        BlocProvider(create: (context) => di.sl<GetPendingRiderDetailBloc>()),
        BlocProvider(create: (context) => di.sl<AddToCartBookingBloc>()),
        BlocProvider(create: (context) => di.sl<GetRideShiftBloc>()),
        BlocProvider(create: (context) => di.sl<GetNewPassengerBloc>()),
        BlocProvider(create: (context) => di.sl<PolylinesPointsBloc>()),
        BlocProvider(create: (context) => di.sl<AcceptPassengerRequestBloc>()),
        BlocProvider(create: (context) => di.sl<GetMyAcceptingRidersBloc>()),
        BlocProvider(create: (context) => di.sl<ClearMyCartBloc>()),
        BlocProvider(create: (context) => di.sl<RideNotifyAlertBloc>()),
        BlocProvider(
          create: (context) => di.sl<GetMyCurrentPassengerBookingBloc>(),
        ),
        BlocProvider(create: (context) => di.sl<AcceptRiderRequestBloc>()),
        BlocProvider(create: (context) => di.sl<RideStartCompletorBloc>()),
        BlocProvider(create: (context) => di.sl<GetMyCurrentBookingBloc>()),
        BlocProvider(create: (context) => di.sl<CancelRequestBloc>()),
        BlocProvider(create: (context) => di.sl<GetMyBookingBloc>()),
        BlocProvider(create: (context) => di.sl<GetMyPassengersBookingBloc>()),
        BlocProvider(create: (context) => di.sl<ManageRideShiftBloc>()),
        BlocProvider(create: (context) => di.sl<ManageFareRateBloc>()),
        BlocProvider(create: (context) => di.sl<GetAdminRoleListBloc>()),
        BlocProvider(create: (context) => di.sl<GetAdminRoleListBloc>()),
        BlocProvider(create: (context) => di.sl<SelectedVehicleTypeCubit>()),
        BlocProvider(create: (context) => di.sl<BookingDetailBloc>()),
        BlocProvider(create: (context) => di.sl<SetChoosedLocationBloc>()),
        BlocProvider(create: (context) => di.sl<GetAdminUserRoleBloc>()),
        BlocProvider(create: (context) => di.sl<RatingBloc>()),
        BlocProvider(create: (context) => di.sl<MyAllCurrentOnlyBookingBloc>()),
      ],
      child: ShadApp.custom(
        appBuilder:
            (_) => Builder(
              builder: (context) {
                final locale = context.watch<LocaleCubit>().state;
                final theme = context.watch<AppThemeCubit>().state;
                final appState = context.watch<AppCubit>().state;

                return MaterialApp.router(
                  builder: (context, child) {
                    final style = AppTheme.getSystemUiOverlayStyleFromMode(
                      theme.mode,
                    );

                    SystemChrome.setSystemUIOverlayStyle(style);
                    L.init(AppLocalizations.of(context)!);
                    if (!appState.isInitialized) {
                      return AppNotInitialize(thememode: theme.mode);
                    }

                    return child!;
                  },
                  title: 'Safari Yatri',
                  debugShowCheckedModeBanner: false,
                  locale: locale,
                  supportedLocales: AppLocalizations.supportedLocales,
                  localizationsDelegates:
                      AppLocalizations.localizationsDelegates,
                  localeResolutionCallback: (locale, supportedLocales) {
                    if (locale == null) return supportedLocales.first;
                    return supportedLocales.firstWhere(
                      (supportedLocale) =>
                          supportedLocale.languageCode == locale.languageCode,
                      orElse: () => supportedLocales.first,
                    );
                  },
                  theme: AppTheme.lightTheme,
                  darkTheme: AppTheme.darkTheme,
                  themeMode: theme.mode,
                  routerConfig: AppRouter.router,
                );
              },
            ),
      ),
    );
  }
}
