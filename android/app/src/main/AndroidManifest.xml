<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 🌐 Network Permissions -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>

    <!-- 📍 Location Permissions -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>

    <!-- 📸 Camera & Media -->
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>

    <!-- 🔐 Ad Identifier -->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>

    <application
        android:label="Safari Yatri"
        android:name="${applicationName}"
        android:icon="@mipmap/launcher_icon"
        android:enableOnBackInvokedCallback="true">

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">

            <!-- Flutter UI Initialization Theme -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />

            <!-- App Launch Intent -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <!-- Required for Flutter Plugin Registration -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <!-- Google Maps or Location Services API Key -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyDhhXoAJDKWWSp0c2R0PYPXLu1Dnw3cfoU"/>

        <!-- Google Analytics Ad Personalization (Optional, Safe to Keep) -->
        <meta-data
            android:name="google_analytics_adid_collection_enabled"
            android:value="true" />
        <meta-data
            android:name="google_analytics_default_allow_ad_personalization_signals"
            android:value="true" />

    </application>

    <!-- Required to query activities that can process text -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>

</manifest>
